# XCM32L项目代码规范

## 1. 文件组织规范

### 1.1 文件命名
- 头文件：`module_name.h` 或 `module_name_optimized.h`
- 源文件：`module_name.c` 或 `module_name_optimized.c`
- 配置文件：`config_name.h`
- 文档文件：`README.md`, `CHANGELOG.md`

### 1.2 目录结构
```
User/
├── system_config.h          # 系统配置和常量定义
├── build_config.h           # 构建配置和特性开关
├── main_optimized.h/c       # 主函数和硬件初始化
├── user_main_optimized.h/c  # 用户主逻辑
├── gpio_optimized.h/c       # GPIO控制模块
├── uart_optimized.h/c       # UART通信模块
├── protocol_optimized.h/c   # 协议处理模块
├── rtc_optimized.h          # RTC模块头文件
└── CODING_STANDARDS.md      # 代码规范文档
```

## 2. 命名规范

### 2.1 变量命名
- **全局变量**：`g_module_variable_name`
  ```c
  static int g_system_status = 0;
  static uint8_t g_mcu_first_boot = 1;
  ```

- **静态变量**：`s_variable_name`
  ```c
  static uint32_t s_tick_counter = 0;
  ```

- **局部变量**：`variable_name`
  ```c
  int buffer_size = 1024;
  uint8_t received_data = 0;
  ```

### 2.2 函数命名
- **模块函数**：`Module_Function_Name`
  ```c
  void GPIO_Configure_Pin(const gpio_config_t* config);
  int Protocol_Parse_JSON(char *buf);
  ```

- **内部函数**：使用`static`修饰，小写加下划线
  ```c
  static void system_clock_config(void);
  static int validate_input_data(uint8_t *data, int len);
  ```

### 2.3 常量和宏定义
- **常量**：`MODULE_CONSTANT_NAME`
  ```c
  #define SYSTEM_STATUS_IDLE          1
  #define UART_BUFFER_SIZE            1024
  #define DEFAULT_PERIOD_INTERVAL     14400
  ```

- **函数式宏**：`MODULE_MACRO_NAME`
  ```c
  #define red_led_ON    PORT_WriteBit(LED_RED_PORT, LED_RED_PIN, Bit_SET)
  #define CHECK_NULL_POINTER(ptr) \
      do { if ((ptr) == NULL) return -1; } while(0)
  ```

### 2.4 类型定义
- **结构体**：`module_name_t`
  ```c
  typedef struct {
      uint8_t id;
      uint16_t distance;
      uint8_t date[20];
  } log_info_t;
  ```

- **枚举**：`MODULE_ENUM_NAME`
  ```c
  typedef enum {
      SYSTEM_STATUS_IDLE = 1,
      SYSTEM_STATUS_FIRST_ON = 2
  } system_status_t;
  ```

## 3. 代码格式规范

### 3.1 缩进和空格
- 使用4个空格缩进，不使用Tab
- 运算符前后加空格
- 逗号后加空格
- 函数参数列表中的逗号后加空格

```c
// 正确
int result = calculate_value(param1, param2, param3);
if (condition1 && condition2) {
    // code
}

// 错误
int result=calculate_value(param1,param2,param3);
if(condition1&&condition2){
    // code
}
```

### 3.2 大括号风格
- 函数大括号独占一行
- 控制语句大括号跟随在同一行

```c
// 函数定义
void Function_Name(void)
{
    // function body
}

// 控制语句
if (condition) {
    // code
} else {
    // code
}

for (int i = 0; i < count; i++) {
    // code
}
```

### 3.3 行长度
- 每行代码不超过100个字符
- 长行需要适当换行

```c
// 长函数调用换行
result = very_long_function_name(parameter1, parameter2, 
                                parameter3, parameter4);

// 长条件语句换行
if ((condition1 && condition2) || 
    (condition3 && condition4)) {
    // code
}
```

## 4. 注释规范

### 4.1 文件头注释
```c
/**
 * @file    filename.c
 * @brief   Brief description of the file
 * @version 1.0
 * @date    2024-01-01
 */
```

### 4.2 函数注释
```c
/**
 * @brief  Brief description of the function
 * @param  param1: description of parameter 1
 * @param  param2: description of parameter 2
 * @retval Return value description
 */
int Function_Name(int param1, char *param2)
{
    // function implementation
}
```

### 4.3 变量注释
```c
static int g_system_status = 0;     /* Current system status */
static uint8_t g_retry_count = 0;   /* Number of retry attempts */

typedef struct {
    uint8_t  id;                /* Log ID */
    uint16_t distance;          /* Radar distance */
    uint8_t  date[20];          /* Date string */
} log_info_t;
```

### 4.4 代码块注释
```c
/* Initialize system clock */
System_Clock_Config();

/* Configure GPIO pins */
GPIO_Configure_Group(led_configs, sizeof(led_configs) / sizeof(led_configs[0]));

/* Main processing loop */
while (1) {
    /* Update current time */
    g_timing_info.current_time = GetRtcSecond();
    
    /* Process radar control */
    Radar_Control_Process();
}
```

## 5. 错误处理规范

### 5.1 返回值约定
- 成功：返回0或正值
- 失败：返回负值
- 布尔函数：返回true/false

```c
int Protocol_Parse_JSON(char *buf)
{
    if (buf == NULL) {
        return -1;  /* Invalid parameter */
    }
    
    /* Process data */
    
    return 0;  /* Success */
}
```

### 5.2 错误检查
```c
/* 检查空指针 */
if (buffer == NULL) {
    printf("Error: NULL pointer\r\n");
    return -1;
}

/* 检查边界 */
if (index >= BUFFER_SIZE) {
    printf("Error: Index out of bounds\r\n");
    return -1;
}

/* 检查函数返回值 */
result = some_function();
if (result != 0) {
    printf("Error: Function failed with code %d\r\n", result);
    return result;
}
```

## 6. 内存管理规范

### 6.1 动态内存分配
```c
/* 分配内存 */
char *buffer = malloc(size);
if (buffer == NULL) {
    printf("Error: Memory allocation failed\r\n");
    return -1;
}

/* 使用内存 */
// ... use buffer ...

/* 释放内存 */
free(buffer);
buffer = NULL;  /* 防止悬空指针 */
```

### 6.2 缓冲区操作
```c
/* 使用安全的字符串函数 */
strncpy(dest, src, sizeof(dest) - 1);
dest[sizeof(dest) - 1] = '\0';

/* 检查缓冲区边界 */
if (pos < BUFFER_SIZE) {
    buffer[pos] = data;
    pos++;
}
```

## 7. 性能优化建议

### 7.1 避免重复计算
```c
/* 错误：重复计算 */
for (int i = 0; i < strlen(str); i++) {
    // process str[i]
}

/* 正确：缓存计算结果 */
int len = strlen(str);
for (int i = 0; i < len; i++) {
    // process str[i]
}
```

### 7.2 使用适当的数据类型
```c
/* 使用最小的合适数据类型 */
uint8_t  flag = 0;          /* 布尔值或小整数 */
uint16_t distance = 0;      /* 距离值 */
uint32_t timestamp = 0;     /* 时间戳 */
```

## 8. 代码审查检查清单

### 8.1 功能性检查
- [ ] 函数是否实现了预期功能
- [ ] 边界条件是否正确处理
- [ ] 错误情况是否正确处理
- [ ] 内存是否正确分配和释放

### 8.2 代码质量检查
- [ ] 命名是否清晰和一致
- [ ] 注释是否充分和准确
- [ ] 代码格式是否符合规范
- [ ] 是否存在重复代码

### 8.3 性能检查
- [ ] 是否存在性能瓶颈
- [ ] 内存使用是否合理
- [ ] 是否有不必要的计算
- [ ] 算法复杂度是否合理

### 8.4 安全性检查
- [ ] 缓冲区溢出检查
- [ ] 空指针检查
- [ ] 整数溢出检查
- [ ] 输入验证检查
