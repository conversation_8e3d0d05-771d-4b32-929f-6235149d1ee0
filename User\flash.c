
#include "XCM32Lxx_conf.h"
#include "stdio.h"

uint32_t wBuffer[256] = {0}, rBuffer[256] = {0};
uint16_t rBuffer16Bits[2] = {0};
uint8_t rBuffer8Bits[4] = {0};
/*
void DelayMs(uint16_t ms)
{
  uint16_t i, j;
  
  for (i = 0; i < ms; i++)
  {
    for (j = 0; j < 2500; j++);
  }
}*/

void CMU_HSI2HSE(void)
{
  // Switch sysclk from IRC16M to HSE(32MHz)
  
  /* Disable all interrupt */
  __disable_irq();
  
  /* At high speed system clock and insert flash read wait cycle */
  FLASH_ReadWaitCycleCmd(ENABLE);
  
  // Put on HSE
  CMU_HSECmd(CMU_CLKStartUpTime3, CMU_HSEDriver7, CMU_HSE_ON);
  
  // Check HSE is stable
  while (CMU_WaitForSysClkStartUp(CMU_SysClkSource_HSE) != SET);
  
  // Switch Systemclock from IRC16M to HSE
  CMU_SysClkConfig(CMU_SysClkSource_HSE);
  
  // Close IRC16M
  CMU_HSIConfig(CMU_IRC16M_Trim_16M, CMU_HSI_OFF);
  
  /* Enable all interrupt */
  __enable_irq();	
}
/*
static void NVIC_Init(void)
{
  __disable_irq();
  
  __enable_irq();
}*/
/*
int fputc(int ch, FILE *stream)
{
  while (UART_GetLineStatus(UART_2, UART_LSFLAG_TXREmpty_TXFIFOFull) == SET);
  UART_SendData(UART_2, ch);
  
  return ch;
}*/

int Flash_test_main(void)
{
  uint32_t i;
  
  //NVIC_Init();
  
  //HCLK is @16MHz
  FLASH_SetTNVS(0x64);
  FLASH_SetTPGS(0xC8);
  FLASH_SetTPROG(0x1E0);
  FLASH_SetTNVH(0x64);
  FLASH_SetTRCV(0x14);
  FLASH_SetTERASE(0x61A80);
  FLASH_SetTME(0x61A80);
  FLASH_SetTNVH1(0x7D0);
  
  //	//HCLK is @32MHz
  //	FLASH_SetTNVS(0xC8);
  //	FLASH_SetTPGS(0x190);
  //	FLASH_SetTPROG(0x3C0);
  //	FLASH_SetTNVH(0xC8);
  //	FLASH_SetTRCV(0x28);
  //	FLASH_SetTERASE(0xC3500);
  //	FLASH_SetTME(0xC3500);
  //	FLASH_SetTNVH1(0xFA0);
  
  printf("%x\n\r", FLASH->TNVS);
  printf("%x\n\r", FLASH->TPGS);
  printf("%x\n\r", FLASH->TPROG);
  printf("%x\n\r", FLASH->TNVH);
  printf("%x\n\r", FLASH->TRCV);
  printf("%x\n\r", FLASH->TERASE);
  printf("%x\n\r", FLASH->TME);
  printf("%x\n\r", FLASH->TNVH1);
  printf("%x\n\r", FLASH->Flash_PL0);	
  printf("%x\n\r", FLASH->Flash_PL1);	
  printf("%x\n\r", FLASH->Flash_PL2);	
  printf("%x\n\r", FLASH->Flash_PL3);	
  printf("%x\n\r", FLASH->Flash_PL4);	
  printf("%x\n\r", FLASH->Flash_PL5);	
  printf("%x\n\r", FLASH->Flash_PL6);	
  printf("%x\n\r", FLASH->Flash_PL7);	
  
  printf("-------------Bfore array initialize, rBuffer data...---------------\n\r");
  for (i = 0; i < 256; i++)
  {
    printf("rBuffer_Address: 0x%X, Data: 0x%X...\n\r", i, *(rBuffer + i));
  }
  
  printf("-------------After page erase, flash Read...---------------\n\r");	
  FLASH_UnLockPage(10);
  FLASH_ErasePage(10);
  
  for (i = 0; i < 256; i++)
  {
    while (FLASH_ReadWord(10 * 1024 + 4 * i, (rBuffer + i)) == FLASH_ERROR);
  }	
  
  for (i = 0; i < 256; i++)
  {
    printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (10 * 1024 + 4 * i), *(rBuffer + i));
  }
  
  printf("----------------Flash write...-----------------\n\r");
  
  for (i = 0; i < 256; i++)
  {
    while (FLASH_WriteWord(10 * 1024 + 4 * i, i) == FLASH_ERROR);
  }
  
  printf("----------------After Flash write, flash read...-----------------\n\r");	
  for (i = 0; i < 256; i++)
  {
    while (FLASH_ReadWord(10 * 1024 + 4 * i, (rBuffer + i)) == FLASH_ERROR);	
  }	
  
  for (i = 0; i < 256; i++)
  {
    printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (10 * 1024 + 4 * i), *(rBuffer + i));
  }
  while(1);
  printf("----------------Other method of flash read...-----------------\n\r");		
  FLASH_ErasePage(10);
  while (FLASH_WriteWord(0x2800, 0xC33CE55E) != FLASH_COMPLETE);	
  FLASH->FLASH_CTRL &= ~FLASH_CTRL_MODE;
  printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (0x2800), *(volatile uint32_t *)0x2800);	
  
  printf("----------------Flash design...-----------------\n\r");	
  printf("----------------Flash word by byte...-----------------\n\r");		
  FLASH_ErasePage(10);
  while (FLASH_WriteByte(0x2800, 0xC3) != FLASH_COMPLETE);
  while (FLASH_WriteByte(0x2800 + 1, 0x3C) != FLASH_COMPLETE);
  while (FLASH_WriteByte(0x2800 + 2, 0xE5) != FLASH_COMPLETE);
  while (FLASH_WriteByte(0x2800 + 3, 0x5E) != FLASH_COMPLETE);	
  FLASH->FLASH_CTRL &= ~FLASH_CTRL_MODE;
  printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (0x2800), *(volatile uint32_t *)0x2800);
  
  while (FLASH_ReadHalfWord(0x2800, rBuffer16Bits) != FLASH_COMPLETE);
  while (FLASH_ReadHalfWord(0x2800 + 2, rBuffer16Bits + 1) != FLASH_COMPLETE);
  printf("rBuffer16Bits[0] = 0x%X...\n\r", rBuffer16Bits[0]);
  printf("rBuffer16Bits[1] = 0x%X...\n\r", rBuffer16Bits[1]);
  
  while (FLASH_ReadByte(0x2800, rBuffer8Bits) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 1, rBuffer8Bits + 1) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 2, rBuffer8Bits + 2) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 3, rBuffer8Bits + 3) != FLASH_COMPLETE);	
  printf("rBuffer8Bits[0] = 0x%X...\n\r", rBuffer8Bits[0]);
  printf("rBuffer8Bits[1] = 0x%X...\n\r", rBuffer8Bits[1]);
  printf("rBuffer8Bits[2] = 0x%X...\n\r", rBuffer8Bits[2]);
  printf("rBuffer8Bits[3] = 0x%X...\n\r", rBuffer8Bits[3]);
  
  FLASH_ErasePage(10);
  while (FLASH_WriteWord(0x2800, 0xC33CE55E) != FLASH_COMPLETE);	
  FLASH->FLASH_CTRL &= ~FLASH_CTRL_MODE;
  printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (0x2800), *(volatile uint32_t *)0x2800);	
  
  printf("----------------Flash word by halfword...-----------------\n\r");		
  FLASH_ErasePage(10);
  while (FLASH_WriteHalfWord(0x2800, 0x8510) != FLASH_COMPLETE);
  while (FLASH_WriteHalfWord(0x2800 + 2, 0xAAF0) != FLASH_COMPLETE);
  FLASH->FLASH_CTRL &= ~FLASH_CTRL_MODE;
  printf("FLASH_Address: 0x%X, Data: 0x%X...\n\r", (0x2800), *(volatile uint32_t *)0x2800);
  
  while (FLASH_ReadHalfWord(0x2800, rBuffer16Bits) != FLASH_COMPLETE);
  while (FLASH_ReadHalfWord(0x2800 + 2, rBuffer16Bits + 1) != FLASH_COMPLETE);
  printf("rBuffer16Bits[0] = 0x%X...\n\r", rBuffer16Bits[0]);
  printf("rBuffer16Bits[1] = 0x%X...\n\r", rBuffer16Bits[1]);
  
  while (FLASH_ReadByte(0x2800, rBuffer8Bits) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 1, rBuffer8Bits + 1) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 2, rBuffer8Bits + 2) != FLASH_COMPLETE);
  while (FLASH_ReadByte(0x2800 + 3, rBuffer8Bits + 3) != FLASH_COMPLETE);	
  printf("rBuffer8Bits[0] = 0x%X...\n\r", rBuffer8Bits[0]);
  printf("rBuffer8Bits[1] = 0x%X...\n\r", rBuffer8Bits[1]);
  printf("rBuffer8Bits[2] = 0x%X...\n\r", rBuffer8Bits[2]);
  printf("rBuffer8Bits[3] = 0x%X...\n\r", rBuffer8Bits[3]);	
  
  while (1);
}

