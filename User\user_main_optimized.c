/**
 * @file    user_main_optimized.c
 * @brief   Optimized user main application logic
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include "system_config.h"
#include "main_optimized.h"
#include "gpio.h"
#include "protol.h"
#include "rtc.h"
#include "uart.h"
#include "user_main.h"
#include "i2cUart.h"

/* Private variables ---------------------------------------------------------*/
/* System state variables */
static system_state_t g_system_state = {0};
static device_power_state_t g_device_power = {0};
static timing_info_t g_timing_info = {0};
static statistics_info_t g_statistics = {0};

/* Application variables */
static uint8_t g_test_mode_enabled = 0;
static char *g_firmware_version = FIRMWARE_VERSION;
static uint8_t g_mcu_first_boot = 1;
static float g_battery_voltage = 0;

/* Radar processing variables */
static radar_info_t g_radar_info_buffer[RADAR_INFO_BUFFER_SIZE];
static int g_radar_info_index = 0;
static int g_radar_sub_index = 0;
static uint16_t g_current_energy = 0;
static uint16_t g_current_distance = 0;

/* Distance and trigger variables */
static int g_last_distance = 0;
static int g_first_query_distance = 0;
static int g_trigger_distance = 0;
static int g_last_have_car = 0;
static int g_current_have_car = 0;
static uint8_t g_car_status = 2;  /* 0: no car, 1: has car, 2: unknown */
static uint8_t g_force_out = 0;
static uint8_t g_need_retry = 0;

/* Counter variables */
static int g_trigger_stable_count = 0;
static int g_idle_count = 0;
static int g_ideal_trigger_distance = 0;
static uint8_t g_trigger_interval = 1;

/* Timing variables */
static long long g_period_trig_4g_time = -9999999;
static int g_startup_period = 0;

/* Status variables */
static uint8_t g_upload_server_flag = FALSE;
static int g_last_trip_type = 4;
static int g_snap_finished = 0;
static uint8_t g_first_retry = 1;

/* External variables */
extern int startUpPeriod;
extern char g_tmpval[TEMP_BUFFER_SIZE];

/* Private function prototypes -----------------------------------------------*/
static void System_State_Init(void);
static void Device_Power_Control(void);
static void Radar_Control_Process(void);
static void Communication_Process(void);
static void Periodic_Tasks(void);
static void Production_Test_Process(void);
static void Enter_Stop_Mode(void);
static void Start_Snap_Process(uint8_t have_car);
static int Process_Radar_Data(void);

/**
 * @brief  Initialize system state variables
 * @param  None
 * @retval None
 */
static void System_State_Init(void)
{
    /* Initialize system state */
    g_system_state.current_status = SYSTEM_STATUS_IDLE;
    g_system_state.radar_status = RADAR_STATUS_QUERY;
    g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_IN;
    g_system_state.mcu_first_boot = 1;
    g_system_state.test_mode_enabled = 0;
    g_system_state.boost_stopped = 0;
    
    /* Initialize device power state */
    g_device_power.radar_power_on = OFF;
    g_device_power.module_4g_power_on = OFF;
    g_device_power.hi3516_power_on = OFF;
    
    /* Initialize timing information */
    g_timing_info.current_time = GetRtcSecond();
    g_timing_info.period_set_time = g_timing_info.current_time;
    g_timing_info.print_time = g_timing_info.current_time;
    
    /* Initialize statistics */
    memset(&g_statistics, 0, sizeof(statistics_info_t));
    
    /* Initialize radar info buffer */
    memset(g_radar_info_buffer, 0, sizeof(g_radar_info_buffer));
    
    /* Set startup period */
    startUpPeriod = DEFAULT_PERIOD_INTERVAL;
    
    /* Get initial battery voltage */
    g_battery_voltage = get_voltage();
}

/**
 * @brief  Control device power states
 * @param  None
 * @retval None
 */
static void Device_Power_Control(void)
{
    /* This function can be expanded to manage power states */
    /* Currently, power control is handled by individual functions */
}

/**
 * @brief  Turn on radar
 * @param  None
 * @retval None
 */
void RadarTurnOn(void)
{
    ST_ULT_PWRCTL_ON;
    g_device_power.radar_power_on = ON;
    g_timing_info.radar_on_time = g_timing_info.current_time;
    
    /* Radar needs startup delay */
    Delay_ms(80);
    printf("\r\nRadar ON---------[%d]\r\n", GetSysTick());
}

/**
 * @brief  Turn off radar
 * @param  None
 * @retval None
 */
void RadarTurnOff(void)
{
    ST_ULT_PWRCTL_OFF;
    g_device_power.radar_power_on = OFF;
    g_timing_info.radar_off_time = g_timing_info.current_time;
    
    printf("\r\nRadar OFF---------[%d]\r\n", GetSysTick());
}

/**
 * @brief  Turn on 4G module
 * @param  None
 * @retval None
 */
void Switch_4G_On(void)
{
    ST_4G_PWRCTL_ON;
    g_device_power.module_4g_power_on = ON;
    g_timing_info.module_4g_on_time = g_timing_info.current_time;
    
    printf("\r\n4G_on_time-------------[%lld]\r\n", g_timing_info.current_time);
}

/**
 * @brief  Turn off 4G module
 * @param  None
 * @retval None
 */
void Switch_4G_Off(void)
{
    ST_4G_PWRCTL_OFF;
    g_device_power.module_4g_power_on = OFF;
    
    printf("\r\n4G_off_time-------------[%lld]\r\n", g_timing_info.current_time);
}

/**
 * @brief  Turn on snap module (HI3516)
 * @param  None
 * @retval None
 */
void SnapModule_On(void)
{
    ST_MAINPOW_CTRL_ON;
    g_device_power.hi3516_power_on = ON;
    g_timing_info.hi3516_on_time = g_timing_info.current_time;
    
    /* Adjust fill light based on time */
    if (RTC_GetHour() <= 0x06 || RTC_GetHour() >= 0x18) {
        TIMER_SetLoadCounter1(TIMER_2, 8000);
    }
    
    printf("\r\nsnapmodule_on_time-----[%lld] \r\n", g_timing_info.current_time);
}

/**
 * @brief  Turn off snap module (HI3516)
 * @param  None
 * @retval None
 */
void SnapModule_Off(void)
{
    ST_MAINPOW_CTRL_OFF;
    g_device_power.hi3516_power_on = OFF;
    
    printf("\r\n-----------------snapmodule_off_time-----[%lld] \r\n", g_timing_info.current_time);
    
    /* Turn off fill light */
    TIMER_SetLoadCounter1(TIMER_2, 0);
}

/**
 * @brief  Enter stop mode
 * @param  None
 * @retval None
 */
static void Enter_Stop_Mode(void)
{
    g_system_state.current_status = SYSTEM_STATUS_IDLE;
    g_system_state.radar_status = RADAR_STATUS_QUERY;
    
    /* Turn off all modules */
    SnapModule_Off();
    Switch_4G_Off();
    
    /* Turn off all LEDs */
    green_led_OFF;
    blue_led_OFF;
    red_led_OFF;
    sys_run_led_OFF;
    
    g_last_have_car = g_current_have_car;
    Booster_Stop();
    Delay_ms(2200);
    
    printf("\r\n\t stop_mode Time[%lld];RadarStatus[%d];g_Status[%d] \r\n",
           g_timing_info.current_time, g_system_state.radar_status, g_system_state.current_status);
}

/**
 * @brief  Production test process
 * @param  None
 * @retval None
 */
static void Production_Test_Process(void)
{
    /* Red LED test */
    red_led_ON;
    Delay_ms(600);
    red_led_OFF;
    
    /* Green LED test */
    green_led_ON;
    Delay_ms(600);
    green_led_OFF;
    
    /* Blue LED test */
    blue_led_ON;
    Delay_ms(800);
    blue_led_OFF;
    
    /* Fill light test */
    TIMER_SetLoadCounter1(TIMER_2, 2000);
    Delay_ms(2000);
    TIMER_SetLoadCounter1(TIMER_2, 0);
    
    blue_led_ON;
}

/**
 * @brief  Process radar data reception
 * @param  None
 * @retval 0 if data processed, -1 if no data
 */
static int Process_Radar_Data(void)
{
    int ret;

    /* Process radar data */
    do {
        ret = uart1_rx_mengmu_process(&g_current_energy, &g_current_distance);
        if (ret == 0) {
            g_radar_sub_index++;

            if (g_radar_sub_index >= 4) {
                g_radar_sub_index = 0;

                /* Turn off radar */
                RadarTurnOff();
                if (g_system_state.boost_stopped == 0) {
                    Booster_DeInit();
                }

                printf("\r\n\t----MengMu-Dis:[%d],LastDis[%d],Idx[%d]\r\n",
                       g_current_distance, g_last_distance, g_radar_info_index);

                /* Store radar information */
                g_radar_info_buffer[g_radar_info_index].time = g_timing_info.current_time;
                g_radar_info_buffer[g_radar_info_index].distance = g_current_distance;
                g_radar_info_buffer[g_radar_info_index].energy = g_current_energy;
                g_radar_info_index = (g_radar_info_index + 1) % RADAR_INFO_BUFFER_SIZE;
            }
        }
    } while (ret == 0);

    return 0;
}

/**
 * @brief  Start snap process
 * @param  have_car: car presence flag
 * @retval None
 */
static void Start_Snap_Process(uint8_t have_car)
{
    /* Stabilization delay */
    Delay_ms(2200);
    Booster_Start();

    g_statistics.trigger_count++;
    g_system_state.radar_status = RADAR_STATUS_STOP;

    /* Set LED based on trigger type */
    if (g_system_state.trigger_type == TRIGGER_TYPE_DRIVE_IN) {
        red_led_ON;
    } else {
        green_led_ON;
    }

    g_car_status = have_car;
    Switch_4G_On();
    g_system_state.current_status = SYSTEM_STATUS_RADAR_TRIG_4G_ON;
    g_timing_info.radar_stop_time = g_timing_info.current_time;

    Delay_ms(3000);
    SnapModule_On();
    g_system_state.current_status = SYSTEM_STATUS_RADAR_TRIG_MAINBOARD_ON;
    printf("\r\n -----------------trig success,SnapModule ON------\r\n");
}

/**
 * @brief  Process radar control logic
 * @param  None
 * @retval None
 */
static void Radar_Control_Process(void)
{
    /* Process radar data reception */
    Process_Radar_Data();

    /* Handle system status transitions */
    if (g_system_state.current_status == SYSTEM_STATUS_IDLE) {
        if (g_system_state.radar_status == RADAR_STATUS_QUERY) {
            /* Check if it's time to turn on radar */
            if (abs(g_timing_info.current_time - g_timing_info.radar_on_time) >= RADAR_QUERY_INTERVAL) {
                Booster_Init();
                RxReadPos[UART1_POS] = RxWritePos[UART1_POS] = 0;
                g_radar_sub_index = 0;
                RadarTurnOn();
            }

            /* Check radar timeout */
            if (abs(g_timing_info.current_time - g_timing_info.radar_on_time) > RADAR_ON_TIMEOUT &&
                g_device_power.radar_power_on == 1) {
                RadarTurnOff();
                if (g_system_state.boost_stopped == 0) {
                    Booster_DeInit();
                }
            }
        }
        else if (g_system_state.radar_status == RADAR_STATUS_TRIG) {
            /* Handle trigger mode radar control */
            if ((g_device_power.radar_power_on == 1) &&
                (abs(g_timing_info.current_time - g_timing_info.radar_on_time) >= 4)) {
                RadarTurnOff();
                if (g_system_state.boost_stopped == 0) {
                    Booster_DeInit();
                }
                printf("\r\nline[%d],LastDistance:[%d]\r\n", __LINE__, g_last_distance);
            }

            if (g_device_power.radar_power_on == 0) {
                /* Determine radar restart timing based on last trip type */
                int restart_delay = (g_last_trip_type == 0) ? RADAR_OFF_DELAY_DRIVE_IN : RADAR_OFF_DELAY_DRIVE_OUT;

                if (abs(g_timing_info.current_time - g_timing_info.radar_off_time) >= restart_delay) {
                    Booster_Init();
                    RxReadPos[UART1_POS] = RxWritePos[UART1_POS] = 0;
                    RadarTurnOn();
                }
            }
        }
    }
}

/**
 * @brief  Process communication with main module
 * @param  None
 * @retval None
 */
static void Communication_Process(void)
{
    int dev_type;
    char *buff = NULL;
    int buff_len = 0;

    /* Process communication data */
    do {
        buff_len = 0;
        uart2_rx_process(&dev_type, &buff, &buff_len);
        if (buff_len != 0) {
            cJsonParseProtocol(buff);
            break;
        }
    } while (buff_len != 0);
}

/**
 * @brief  Handle periodic tasks
 * @param  None
 * @retval None
 */
static void Periodic_Tasks(void)
{
    /* Check for periodic trigger */
    if ((startUpPeriod != 0) &&
        (g_system_state.current_status == SYSTEM_STATUS_IDLE) &&
        (abs(g_timing_info.current_time - g_timing_info.period_set_time) > (long long)startUpPeriod)) {

        g_statistics.period_count++;
        RadarTurnOff();
        Delay_ms(2200);
        Booster_Start();

        g_system_state.radar_status = RADAR_STATUS_STOP;
        g_system_state.current_status = SYSTEM_STATUS_PERIOD_TRIG_4G_ON;
        Switch_4G_On();
        g_timing_info.radar_stop_time = g_timing_info.current_time;

        blue_led_ON;
        g_trigger_distance = g_last_distance;
        g_period_trig_4g_time = g_timing_info.current_time;

        Delay_ms(3000);
        g_system_state.current_status = SYSTEM_STATUS_PERIOD_TRIG_MAINBOARD_ON;
        g_system_state.trigger_type = TRIGGER_TYPE_PERIOD;
        SnapModule_On();

        printf("\r\n%s->%d,period start --------------------------\r\n", __FILE__, __LINE__);
    }

    /* Check for system timeout */
    if (g_device_power.hi3516_power_on == ON &&
        (abs(g_timing_info.current_time - g_timing_info.hi3516_on_time) > SYSTEM_TIMEOUT) &&
        g_system_state.current_status != SYSTEM_STATUS_IDLE) {

        g_system_state.current_status = SYSTEM_STATUS_IDLE;
        g_statistics.over_3min_count++;
        g_timing_info.power_off_time = g_timing_info.current_time;

        printf("\r\n\tTimeout-180seconds,enter stop mode;line->[%d]\r\n", __LINE__);
        Enter_Stop_Mode();
        g_timing_info.period_set_time = g_timing_info.current_time;
    }

    /* Enter deep sleep if idle */
    if (g_system_state.current_status == SYSTEM_STATUS_IDLE &&
        g_system_state.radar_status != RADAR_STATUS_TRIG &&
        g_device_power.radar_power_on == OFF) {
        PMU_EnterDeepSleep3Mode(PMU_EnterMode_Now);
    }
}

/**
 * @brief  Process radar distance analysis
 * @param  None
 * @retval None
 */
static void Process_Radar_Distance_Analysis(void)
{
    int i, num_samples = 0, average_distance = 0;

    /* Calculate average distance from radar buffer */
    for (i = 0; i < RADAR_INFO_BUFFER_SIZE; i++) {
        if (g_radar_info_buffer[i].distance != 0) {
            average_distance += g_radar_info_buffer[i].distance;
            num_samples++;
        }
    }

    if (num_samples > 0) {
        average_distance = average_distance / num_samples;
        printf("\r\nQUERY Mengmu Dis:[%d];lastDis[%d];Num[%d]\r\n",
               average_distance, g_last_distance, num_samples);

        /* Clear radar buffer */
        memset(g_radar_info_buffer, 0, sizeof(g_radar_info_buffer));
        g_radar_info_index = 0;

        /* Process distance changes */
        if (g_system_state.radar_status == RADAR_STATUS_QUERY) {
            if ((g_last_distance != 0) && (average_distance != 0) &&
                (abs(average_distance - g_last_distance) > DISTANCE_CHANGE_THRESHOLD)) {
                g_system_state.radar_status = RADAR_STATUS_TRIG;
                g_first_query_distance = average_distance;
                printf("\r\nline[%d];QUERY DIS:[%d];lastDis:[%d]\r\n",
                       __LINE__, average_distance, g_last_distance);
            }
            else if (g_last_distance == 0 && average_distance != 0) {
                g_system_state.radar_status = RADAR_STATUS_TRIG;
                g_first_query_distance = average_distance;
                printf("\r\nline[%d];QUERY DIS:[%d];lastDis[%d]\r\n",
                       __LINE__, average_distance, g_last_distance);
            }
            else {
                g_system_state.radar_status = RADAR_STATUS_QUERY;
                g_first_query_distance = average_distance;
            }
        }
        else if (g_system_state.radar_status == RADAR_STATUS_TRIG) {
            /* Handle trigger mode distance processing */
            if (average_distance > 3900) {
                g_idle_count++;
                g_ideal_trigger_distance = average_distance;
            }

            if ((g_first_query_distance != 0 && average_distance != 0) &&
                (abs(average_distance - g_first_query_distance) > DISTANCE_STABLE_THRESHOLD)) {
                g_system_state.radar_status = RADAR_STATUS_TRIG;
                g_first_query_distance = average_distance;
                g_trigger_stable_count = 0;
                printf("\r\ntrig---change#dis[%d];lastDis:[%d]\r\n",
                       average_distance, g_last_distance);
            }
            else if ((g_first_query_distance != 0 && average_distance != 0) &&
                     (abs(average_distance - g_first_query_distance) <= DISTANCE_STABLE_THRESHOLD)) {
                g_trigger_stable_count++;

                if (g_trigger_stable_count >= RADAR_STABLE_COUNT_THRESHOLD) {
                    g_trigger_stable_count = 0;

                    if (abs(average_distance - g_last_distance) >= DISTANCE_CHANGE_THRESHOLD) {
                        g_last_distance = average_distance;
                        g_trigger_distance = average_distance;

                        if (g_trigger_distance <= DISTANCE_TRIGGER_THRESHOLD) {
                            g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_IN;
                            g_last_trip_type = g_system_state.trigger_type;
                            Start_Snap_Process(1);
                        } else {
                            g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_OUT;
                            g_last_trip_type = g_system_state.trigger_type;
                            Start_Snap_Process(0);
                        }
                    }
                    else {
                        if (g_idle_count >= IDLE_COUNT_THRESHOLD && g_last_trip_type == 0) {
                            printf("\r\n start 4G ,TrigDis[%d]\r\n", g_ideal_trigger_distance);
                            g_last_distance = g_ideal_trigger_distance;
                            g_trigger_distance = g_last_distance;

                            if (g_trigger_distance <= DISTANCE_TRIGGER_THRESHOLD) {
                                g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_IN;
                                g_last_trip_type = g_system_state.trigger_type;
                                Start_Snap_Process(1);
                            } else {
                                g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_OUT;
                                g_last_trip_type = g_system_state.trigger_type;
                                Start_Snap_Process(0);
                            }
                        }
                        else {
                            g_system_state.radar_status = RADAR_STATUS_QUERY;
                        }
                    }
                    g_idle_count = 0;
                    g_ideal_trigger_distance = 0;
                }
                printf("\r\nTrig---stable#dis[%d];lastDis[%d],RadarOnOff[%d]\r\n",
                       average_distance, g_last_distance, g_device_power.radar_power_on);
            }
            else {
                g_system_state.radar_status = RADAR_STATUS_QUERY;
                g_trigger_stable_count = 0;
            }
        }
    }
}

/**
 * @brief  Handle retry logic for failed uploads
 * @param  None
 * @retval None
 */
static void Handle_Retry_Logic(void)
{
    if (g_system_state.current_status == SYSTEM_STATUS_RETRY && g_need_retry == TRUE) {
        g_statistics.trigger_count++;
        Delay_ms(2200);
        Booster_Start();

        printf("重新上电:雷达触发 %-5lld \n", g_timing_info.current_time);

        g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_IN;
        red_led_ON;
        Switch_4G_On();
        g_system_state.current_status = SYSTEM_STATUS_RADAR_TRIG_4G_ON;
        g_timing_info.radar_stop_time = g_timing_info.current_time;

        Delay_ms(3000);
        SnapModule_On();

        if (g_trigger_distance <= DISTANCE_TRIGGER_THRESHOLD) {
            g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_IN;
            g_last_trip_type = g_system_state.trigger_type;
        } else {
            g_system_state.trigger_type = TRIGGER_TYPE_DRIVE_OUT;
            g_last_trip_type = g_system_state.trigger_type;
        }

        g_system_state.current_status = SYSTEM_STATUS_RADAR_TRIG_MAINBOARD_ON;
        g_need_retry = 0;
        printf("line[%d];!!sendRetry----------g_trigtype[%d]\r\n",
               __LINE__, g_system_state.trigger_type);
    }
}

/**
 * @brief  Main user application function
 * @param  None
 * @retval None
 */
void user_main(void)
{
    /* Update current time */
    g_timing_info.current_time = GetRtcSecond();

    printf("\r\n\t SYS_work_clkFreq:[%d],ClkSource:[%d]\r\n",
           CMU_GetSysClkFreq(), CMU_GetSysClkSource());
    printf("\r\n\t user_main_start_RTC_time:[%lld]\r\n", g_timing_info.current_time);
    printf("\r\n");

    sys_run_led_ON;

    /* Initialize system state on first run */
    if (g_mcu_first_boot) {
        System_State_Init();
        g_mcu_first_boot = 0;
    }

    /* Production test mode */
    if (g_test_mode_enabled) {
        g_test_mode_enabled = 0;
        Production_Test_Process();
    }

    g_system_state.current_status = SYSTEM_STATUS_IDLE;
    g_system_state.radar_status = RADAR_STATUS_QUERY;

    /* Initialize modules if not in test mode */
    if (!g_test_mode_enabled) {
        Switch_4G_On();
        Delay_ms(3000);
        SnapModule_On();
        g_system_state.radar_status = RADAR_STATUS_STOP;
        g_system_state.current_status = SYSTEM_STATUS_FIRST_ON;
    }

    /* Print status periodically */
    if (abs(g_timing_info.current_time - g_timing_info.print_time) >= PRINT_INTERVAL) {
        g_battery_voltage = get_voltage();
        printf("\r\n\tVer[%s];LastDis:[%d];period[%d]\r\n",
               g_firmware_version, g_last_distance, startUpPeriod);
        printf("\r\n\tVoltage:[%.3f],RadarStatus:[%d],nextTimeLeft:[%d]\r\n\r\n",
               g_battery_voltage, g_system_state.radar_status,
               abs(g_timing_info.current_time - g_timing_info.period_set_time));
        g_timing_info.print_time = g_timing_info.current_time;
    }

    /* Main processing loop */
    while (1) {
        /* Update current time */
        g_timing_info.current_time = GetRtcSecond();

        /* Handle retry logic */
        Handle_Retry_Logic();

        /* Process radar control */
        Radar_Control_Process();

        /* Process radar distance analysis */
        Process_Radar_Distance_Analysis();

        /* Process communication */
        Communication_Process();

        /* Handle periodic tasks */
        Periodic_Tasks();

        /* Small delay to prevent tight loop */
        Delay_ms(10);
    }
}
