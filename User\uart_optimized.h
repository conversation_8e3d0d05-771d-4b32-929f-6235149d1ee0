/**
 * @file    uart_optimized.h
 * @brief   Optimized UART communication module header file
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _UART_OPTIMIZED_H_
#define _UART_OPTIMIZED_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* Exported constants --------------------------------------------------------*/
#define UART_NUM                    2
#define UART1_POS                   0
#define UART2_POS                   1

#define UART1_RX_BUF_SIZE           (85 * 4)
#define UART2_RX_BUF_SIZE           1024
#define UART_BUF_LEN                1024

#define MAX_SUPERWAVE_DATA_NUM      20

#define UART_TX_TIMEOUT             1000

/* Exported types ------------------------------------------------------------*/
/**
 * @brief UART configuration structure
 */
typedef struct {
    uint32_t baud_rate;
    uint8_t data_bits;
    uint8_t stop_bits;
    uint8_t parity;
    uint8_t flow_control;
    uint8_t dma_enable;
} uart_config_t;

/**
 * @brief UART buffer structure
 */
typedef struct {
    volatile uint8_t* buffer;
    volatile uint32_t read_pos;
    volatile uint32_t write_pos;
    uint32_t size;
} uart_buffer_t;

/* Exported variables --------------------------------------------------------*/
extern volatile unsigned long long RxReadPos[UART_NUM];
extern volatile unsigned long long RxWritePos[UART_NUM];
extern volatile unsigned char uart2_RxBuf[UART2_RX_BUF_SIZE];
extern volatile unsigned char Uart1_RxBuf[UART1_RX_BUF_SIZE];
extern unsigned char uart_proc_buf[UART_BUF_LEN];
extern unsigned char uart_proced_buf[UART_BUF_LEN];
extern long long g_UpdateTime;

/* Exported function prototypes ----------------------------------------------*/

/* UART1 functions (Radar communication) */
/**
 * @brief  Initialize UART1 for radar communication
 * @param  BaudRate: UART baud rate
 * @param  dmaEnable: DMA enable flag
 * @retval None
 */
void UART1_Init(uint32_t BaudRate, uint8_t dmaEnable);

/**
 * @brief  Deinitialize UART1
 * @param  None
 * @retval None
 */
void UART1_DeInit(void);

/**
 * @brief  UART1 interrupt handler
 * @param  None
 * @retval None
 */
void UART1_IRQHandler(void);

/**
 * @brief  Process radar data from UART1
 * @param  pEnerge: pointer to energy value
 * @param  distance: pointer to distance value
 * @retval 0 if data processed, -1 if no data
 */
int uart1_rx_mengmu_process(unsigned short *pEnerge, unsigned short *distance);

/**
 * @brief  Transmit data via UART1
 * @param  bData: pointer to data buffer
 * @param  bLenth: data length
 * @retval true if successful, false if failed
 */
bool uart1_com_tx_data(uint8_t *bData, uint8_t bLenth);

/**
 * @brief  Receive data via UART1 with timeout
 * @param  pData: pointer to receive buffer
 * @param  Size: expected data size
 * @param  Timeout: timeout value
 * @retval true if successful, false if failed
 */
bool uart1_com_Receive(uint8_t *pData, uint16_t Size, uint32_t Timeout);

/**
 * @brief  Enable/disable UART1 interrupt
 * @param  uEnSta: enable status (1: enable, 0: disable)
 * @retval None
 */
void UART1_IRQ_SET(uint8_t uEnSta);

/* UART2 functions (Main module communication) */
/**
 * @brief  Initialize UART2 for main module communication
 * @param  None
 * @retval None
 */
void UART2_Init(void);

/**
 * @brief  Deinitialize UART2
 * @param  None
 * @retval None
 */
void UART2_DeInit(void);

/**
 * @brief  UART2 interrupt handler
 * @param  None
 * @retval None
 */
void UART2_IRQHandler(void);

/**
 * @brief  Process received data from UART2
 * @param  type: pointer to device type
 * @param  buffer: pointer to data buffer pointer
 * @param  len: pointer to data length
 * @retval 0 if data processed, -1 if no data
 */
int uart2_rx_process(int *type, unsigned char **buffer, int *len);

/**
 * @brief  Transmit data via UART2
 * @param  bLenth: data length
 * @param  bData: pointer to data buffer
 * @retval true if successful, false if failed
 */
bool uart2_com_tx_data(int bLenth, uint8_t *bData);

/* UART4 functions (Debug communication) */
/**
 * @brief  Initialize UART4 for debug communication
 * @param  None
 * @retval None
 */
void UART4_Init(void);

/**
 * @brief  Deinitialize UART4
 * @param  None
 * @retval None
 */
void UART4_DeInit(void);

/**
 * @brief  UART4 interrupt handler
 * @param  None
 * @retval None
 */
void UART4_IRQHandler(void);

/**
 * @brief  Transmit data via UART4
 * @param  bLenth: data length
 * @param  bData: pointer to data buffer
 * @retval true if successful, false if failed
 */
bool uart4_com_tx_data(uint8_t bLenth, uint8_t *bData);

/* Data processing functions */
/**
 * @brief  Find packet header and tail in buffer
 * @param  buf: data buffer
 * @param  buf_width: buffer size
 * @param  pos_start: start position
 * @param  pos_end: end position
 * @param  head_pos: pointer to header position
 * @param  tail_pos: pointer to tail position
 * @retval 0 if found, -1 if not found
 */
int uart2_find_head_and_tail(unsigned char *buf, int buf_width, 
                              long long pos_start, long long pos_end, 
                              long long *head_pos, long long *tail_pos);

/**
 * @brief  Unpack received data
 * @param  dev_type: pointer to device type
 * @param  pack_type: pointer to packet type
 * @param  total_packages: pointer to total packages
 * @param  seq: pointer to sequence number
 * @param  data: input data buffer
 * @param  data_len: input data length
 * @param  unpacked_data: output unpacked data buffer
 * @param  unpacked_len: pointer to unpacked data length
 * @retval 0 if successful, -1 if failed
 */
int uart2_unpack_data(int *dev_type, int *pack_type, int *total_packages, int *seq,
                      unsigned char* data, int data_len, 
                      unsigned char* unpacked_data, unsigned int *unpacked_len);

/**
 * @brief  Pack data for transmission
 * @param  dev_type: device type
 * @param  pack_type: packet type
 * @param  total_packages: total packages
 * @param  seq: sequence number
 * @param  data: input data buffer
 * @param  data_len: input data length
 * @param  packed_data: output packed data buffer
 * @param  packed_len: pointer to packed data length
 * @retval 0 if successful, -1 if failed
 */
int pack_data(int dev_type, int pack_type, int total_packages, int seq,
              unsigned char* data, int data_len, 
              unsigned char* packed_data, int *packed_len);

/**
 * @brief  Pack radar data for transmission
 * @param  dev_type: device type
 * @param  pack_type: packet type
 * @param  total_packages: total packages
 * @param  seq: sequence number
 * @param  usEnerge: energy value
 * @param  usDistance: distance value
 * @param  packed_data: output packed data buffer
 * @param  packed_len: pointer to packed data length
 * @retval 0 if successful, -1 if failed
 */
int pack_luna_data(int dev_type, int pack_type, int total_packages, int seq,
                   unsigned short usEnerge, unsigned short usDistance,
                   unsigned char* packed_data, int *packed_len);

/**
 * @brief  Pack print data for transmission
 * @param  dev_type: device type
 * @param  pack_type: packet type
 * @param  total_packages: total packages
 * @param  seq: sequence number
 * @param  packed_data: output packed data buffer
 * @param  data_len: input data length
 * @param  packed_len: pointer to packed data length
 * @retval 0 if successful, -1 if failed
 */
int pack_print_data(int dev_type, int pack_type, int total_packages, int seq,
                    unsigned char* packed_data, int data_len, int *packed_len);

/* Utility functions */
/**
 * @brief  System printf function
 * @param  fmt: format string
 * @param  ...: variable arguments
 * @retval None
 */
void Sys_Printf(char *fmt, ...);

#endif /* _UART_OPTIMIZED_H_ */
