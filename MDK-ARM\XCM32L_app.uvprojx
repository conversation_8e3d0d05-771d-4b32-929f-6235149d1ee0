<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>XCM32L_app</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060061::V5.06 update 1 (build 61)::ARMCC</pCCUsed>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0P</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.5.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0P$Device\ARM\ARMCM0plus\Include\ARMCM0plus.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0P$Device\ARM\SVD\ARMCM0P.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>xcm32l_app</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --bin -o "$<EMAIL>" "#L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x5000</StartAddress>
                <Size>0x3b000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>..\xcm32lxx_lib\core;..\xcm32lxx_lib\inc;..\User\cJSON;..\User</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Device</GroupName>
          <Files>
            <File>
              <FileName>core_cm0plus.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\xcm32lxx_lib\core\core_cm0plus.h</FilePath>
            </File>
            <File>
              <FileName>startup_XCM32L.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\xcm32lxx_lib\core\startup_XCM32L.s</FilePath>
            </File>
            <File>
              <FileName>system_XCM32L.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\core\system_XCM32L.c</FilePath>
            </File>
            <File>
              <FileName>system_XCM32L.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\xcm32lxx_lib\core\system_XCM32L.h</FilePath>
            </File>
            <File>
              <FileName>XCM32L.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\xcm32lxx_lib\core\XCM32L.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>cJSON.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\cJSON\cJSON.c</FilePath>
            </File>
            <File>
              <FileName>protol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\cJSON\protol.c</FilePath>
            </File>
            <File>
              <FileName>crc32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\crc32.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\flash.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gpio.c</FilePath>
            </File>
            <File>
              <FileName>i2cUart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\i2cUart.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\rtc.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\uart.c</FilePath>
            </File>
            <File>
              <FileName>user_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\user_main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>XCM32Lxx_StdPeriph</GroupName>
          <Files>
            <File>
              <FileName>XCM32Lxx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_adc.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_buzzer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_buzzer.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_cmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_cmu.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_crc.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_des.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_des.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_dma.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_flash.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_i2c.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_lcd.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_lvd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_lvd.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_misc.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_pca.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_pca.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_pmu.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_port.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_ram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_ram.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_rmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_rmu.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_rng.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_rtc.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_spi.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_systick.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_timer.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_uart.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_vc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_vc.c</FilePath>
            </File>
            <File>
              <FileName>XCM32Lxx_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\xcm32lxx_lib\src\XCM32Lxx_wdt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.0.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.0.0"/>
        <targetInfos>
          <targetInfo name="XCM32L_app"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>
