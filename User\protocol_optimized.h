/**
 * @file    protocol_optimized.h
 * @brief   Optimized protocol processing module header file
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _PROTOCOL_OPTIMIZED_H_
#define _PROTOCOL_OPTIMIZED_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"
#include "cJSON.h"

/* Exported constants --------------------------------------------------------*/
#define PROTOCOL_MAX_BUFFER_SIZE    1024
#define PROTOCOL_MAX_CMD_LENGTH     32
#define PROTOCOL_MAX_VERSION_LENGTH 64
#define PROTOCOL_MAX_TIME_STRING    32

/* Protocol command definitions */
#define CMD_GET_BATTERY_VOLTAGE     "getbatteryvoltage"
#define CMD_GET_BOOTUP_TYPE         "getbootuptype"
#define CMD_TRANSPARENT             "transparent"
#define CMD_SET_BOOTUP_PERIOD       "setbootupperiod"
#define CMD_GET_BOOTUP_PERIOD       "getbootupperiod"
#define CMD_GET_VERSION             "getversion"
#define CMD_SNAP_FINISHED           "snapfinished"
#define CMD_INIT_FINISHED           "initfinished"
#define CMD_SET_LED_STATUS          "setledstatus"
#define CMD_GET_LED_STATUS          "getledstatus"
#define CMD_SET_RTC_TIME            "setrtctime"
#define CMD_GET_RTC_TIME            "getrtctime"

/* Response types */
#define RESPONSE_TYPE_QUERY         "query"
#define RESPONSE_TYPE_RESPONSE      "response"

/* Response states */
#define RESPONSE_STATE_OK           "OK"
#define RESPONSE_STATE_ERROR        "ERROR"

/* Bootup types */
#define BOOTUP_TYPE_DRIVE_IN        "drivein"
#define BOOTUP_TYPE_DRIVE_OUT       "driveout"
#define BOOTUP_TYPE_ALARM           "alarm"
#define BOOTUP_TYPE_PERIOD          "period"

/* Device types */
#define DEVICE_JSON                 1
#define DEVICE_RADAR                2

/* Exported types ------------------------------------------------------------*/
/**
 * @brief Protocol command structure
 */
typedef struct {
    char cmd[PROTOCOL_MAX_CMD_LENGTH];
    int id;
    char type[16];
    char state[16];
} protocol_cmd_t;

/**
 * @brief Battery voltage response structure
 */
typedef struct {
    protocol_cmd_t base;
    float voltage;
} protocol_battery_response_t;

/**
 * @brief Bootup type response structure
 */
typedef struct {
    protocol_cmd_t base;
    char time_string[PROTOCOL_MAX_TIME_STRING];
    char bootup_type[16];
    int radar_distance;
    float voltage;
} protocol_bootup_response_t;

/**
 * @brief Period setting structure
 */
typedef struct {
    protocol_cmd_t base;
    int period;
    int have_car;
} protocol_period_setting_t;

/**
 * @brief Version response structure
 */
typedef struct {
    protocol_cmd_t base;
    char version[PROTOCOL_MAX_VERSION_LENGTH];
} protocol_version_response_t;

/**
 * @brief Snap finished structure
 */
typedef struct {
    protocol_cmd_t base;
    int result;
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} protocol_snap_finished_t;

/**
 * @brief LED status structure
 */
typedef struct {
    protocol_cmd_t base;
    int red_led_status;
    int green_led_status;
    int blue_led_status;
} protocol_led_status_t;

/**
 * @brief Transparent info structure
 */
typedef struct {
    protocol_cmd_t base;
    int have_car;
} protocol_transparent_info_t;

/**
 * @brief Protocol statistics structure
 */
typedef struct {
    uint32_t send_success_count;
    uint32_t send_failed_count;
    uint32_t parse_error_count;
    uint32_t memory_error_count;
} protocol_statistics_t;

/* Exported variables --------------------------------------------------------*/
extern protocol_statistics_t g_protocol_stats;
extern char g_firmware_version[PROTOCOL_MAX_VERSION_LENGTH];

/* Exported function prototypes ----------------------------------------------*/

/* Main protocol processing functions */
/**
 * @brief  Parse and process JSON protocol
 * @param  buf: JSON string buffer
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Parse_JSON(char *buf);

/**
 * @brief  Send initialization finished message
 * @param  None
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_Init_Finished(void);

/**
 * @brief  Send transparent information
 * @param  None
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_Transparent_Info(void);

/* Command handler functions */
/**
 * @brief  Handle get battery voltage command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Battery_Voltage(cJSON *cjson);

/**
 * @brief  Handle get bootup type command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Bootup_Type(cJSON *cjson);

/**
 * @brief  Handle transparent command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Transparent(cJSON *cjson);

/**
 * @brief  Handle set bootup period command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Set_Bootup_Period(cJSON *cjson);

/**
 * @brief  Handle get bootup period command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Bootup_Period(cJSON *cjson);

/**
 * @brief  Handle get version command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Version(cJSON *cjson);

/**
 * @brief  Handle snap finished command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Snap_Finished(cJSON *cjson);

/**
 * @brief  Handle set LED status command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Set_LED_Status(cJSON *cjson);

/**
 * @brief  Handle get LED status command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_LED_Status(cJSON *cjson);

/* Utility functions */
/**
 * @brief  Create JSON response object
 * @param  cmd: command string
 * @param  id: command ID
 * @param  state: response state
 * @retval JSON object pointer or NULL if failed
 */
cJSON* Protocol_Create_Response(const char* cmd, int id, const char* state);

/**
 * @brief  Send JSON response
 * @param  json_obj: JSON object to send
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_JSON_Response(cJSON* json_obj);

/**
 * @brief  Get command string from JSON
 * @param  cjson: JSON object
 * @retval Command string or NULL if not found
 */
const char* Protocol_Get_Command(cJSON *cjson);

/**
 * @brief  Get ID from JSON
 * @param  cjson: JSON object
 * @retval ID value or 0 if not found
 */
int Protocol_Get_ID(cJSON *cjson);

/**
 * @brief  Format time string
 * @param  time_str: output time string buffer
 * @param  max_len: maximum buffer length
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Format_Time_String(char* time_str, int max_len);

/**
 * @brief  Get bootup type string
 * @param  trigger_type: trigger type value
 * @retval Bootup type string
 */
const char* Protocol_Get_Bootup_Type_String(int trigger_type);

/**
 * @brief  Initialize protocol module
 * @param  None
 * @retval None
 */
void Protocol_Init(void);

/**
 * @brief  Get protocol statistics
 * @param  None
 * @retval Pointer to statistics structure
 */
protocol_statistics_t* Protocol_Get_Statistics(void);

#endif /* _PROTOCOL_OPTIMIZED_H_ */
