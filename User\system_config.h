/**
 * @file    system_config.h
 * @brief   System configuration and common definitions for XCM32L project
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _SYSTEM_CONFIG_H_
#define _SYSTEM_CONFIG_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stddef.h>
#include <limits.h>

/* Forward declarations for hardware types */
#ifndef __XCM32LXX_CONF_H
/* Basic hardware type definitions */
typedef enum {
    RESET = 0,
    SET = !RESET
} FlagStatus, ITStatus;

typedef enum {
    DISABLE = 0,
    ENABLE = !DISABLE
} FunctionalState;

typedef enum {
    ERROR = 0,
    SUCCESS = !ERROR
} ErrorStatus;
#endif

/* Application configuration -------------------------------------------------*/
#define APP_START_ADDR          0x5000      /* Application start address */
#define APP_START_ADDR1         0x15000     /* Application backup start address */
#define APP_LOG_ADDR            0x14000     /* Application log storage address (80KB) */

/* Version information */
#define FIRMWARE_VERSION        "fwm_MM_0.0.30_" __DATE__

/* System status definitions */
#define SYSTEM_STATUS_IDLE                      1   /* System idle status */
#define SYSTEM_STATUS_FIRST_ON                  2   /* First power on status */
#define SYSTEM_STATUS_PERIOD_TRIG_4G_ON         3   /* Period trigger 4G on */
#define SYSTEM_STATUS_PERIOD_TRIG_MAINBOARD_ON  4   /* Period trigger mainboard on */
#define SYSTEM_STATUS_RADAR_TRIG_SUPERWAVE_ON   5   /* Radar trigger superwave on */
#define SYSTEM_STATUS_RADAR_TRIG_4G_ON          6   /* Radar trigger 4G on */
#define SYSTEM_STATUS_RADAR_TRIG_MAINBOARD_ON   7   /* Radar trigger mainboard on */
#define SYSTEM_STATUS_CHECK_SUPERWAVE_DISTANCE  8   /* Check superwave distance */
#define SYSTEM_STATUS_RADAR_TRIG_BLOCK_ON       9   /* Radar trigger block on */
#define SYSTEM_STATUS_RETRY                     10  /* Retry status */

/* Radar status definitions */
#define RADAR_STATUS_STOP           0   /* Radar stopped */
#define RADAR_STATUS_QUERY          1   /* Radar query mode */
#define RADAR_STATUS_TRIG           2   /* Radar trigger mode */
#define RADAR_STATUS_TRIG_FINISHED  3   /* Radar trigger finished */

/* Trigger type definitions */
#define TRIGGER_TYPE_DRIVE_IN       0   /* Vehicle drive in */
#define TRIGGER_TYPE_DRIVE_OUT      1   /* Vehicle drive out */
#define TRIGGER_TYPE_ALARM          2   /* Alarm trigger */
#define TRIGGER_TYPE_PERIOD         3   /* Period trigger */

/* Distance thresholds (in millimeters) */
#define DISTANCE_CHANGE_THRESHOLD   500     /* Distance change threshold */
#define DISTANCE_TRIGGER_THRESHOLD  3000    /* Distance trigger threshold */
#define DISTANCE_STABLE_THRESHOLD   250     /* Distance stable threshold */
#define DISTANCE_MAX_VALID          4500    /* Maximum valid distance */

/* Timing constants (in seconds) */
#define DEFAULT_PERIOD_INTERVAL     14400   /* Default period interval (4 hours) */
#define PRINT_INTERVAL              20      /* Print status interval */
#define RADAR_QUERY_INTERVAL        10      /* Radar query interval */
#define RADAR_ON_TIMEOUT            3       /* Radar on timeout */
#define RADAR_OFF_DELAY_DRIVE_IN    2       /* Radar off delay for drive in */
#define RADAR_OFF_DELAY_DRIVE_OUT   5       /* Radar off delay for drive out */
#define SYSTEM_TIMEOUT              180     /* System operation timeout */

/* Counter thresholds */
#define RADAR_STABLE_COUNT_THRESHOLD    5   /* Radar stable count threshold */
#define IDLE_COUNT_THRESHOLD            3   /* Idle count threshold */
#define MAX_RETRY_COUNT                 1   /* Maximum retry count */

/* Buffer sizes */
#define RADAR_INFO_BUFFER_SIZE      8       /* Radar information buffer size */
#define TEMP_BUFFER_SIZE            400     /* Temporary buffer size */

/* LED control macros */
#define LED_ON                      1
#define LED_OFF                     0
#define LED_TOGGLE                  2

/* Power control states */
#define POWER_ON                    1
#define POWER_OFF                   0

/* Boolean definitions for compatibility */
#ifndef TRUE
#define TRUE                        1
#endif

#ifndef FALSE
#define FALSE                       0
#endif

#ifndef ON
#define ON                          1
#endif

#ifndef OFF
#define OFF                         0
#endif

/* Safe string operation macros */
#define SAFE_STRCPY(dest, src, size) \
    do { \
        strncpy((dest), (src), (size) - 1); \
        (dest)[(size) - 1] = '\0'; \
    } while(0)

#define SAFE_STRCAT(dest, src, size) \
    do { \
        size_t dest_len = strlen(dest); \
        if (dest_len < (size) - 1) { \
            strncpy((dest) + dest_len, (src), (size) - dest_len - 1); \
            (dest)[(size) - 1] = '\0'; \
        } \
    } while(0)

/* Memory safety macros */
#define SAFE_FREE(ptr) \
    do { \
        if ((ptr) != NULL) { \
            free(ptr); \
            (ptr) = NULL; \
        } \
    } while(0)

#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

/* Exported types ------------------------------------------------------------*/
/**
 * @brief Log information structure
 */
typedef struct {
    uint8_t  id;                /* Log ID */
    uint8_t  have_car;          /* Car presence flag */
    uint16_t radar_distance;    /* Radar distance */
    uint8_t  date[20];          /* Date string: 2021-07-09#14:10:13 */
    uint8_t  trigger_type;      /* Trigger type */
    uint8_t  reserve[5];        /* Reserved bytes */
} log_info_t;

/**
 * @brief Radar information structure
 */
typedef struct {
    long long time;             /* Timestamp */
    uint16_t  energy;           /* Energy value */
    uint16_t  distance;         /* Distance value */
} radar_info_t;

/**
 * @brief System state structure
 */
typedef struct {
    int      current_status;        /* Current system status */
    int      radar_status;          /* Current radar status */
    int      trigger_type;          /* Current trigger type */
    uint8_t  mcu_first_boot;        /* MCU first boot flag */
    uint8_t  test_mode_enabled;     /* Test mode flag */
    uint8_t  boost_stopped;         /* Boost stop flag */
} system_state_t;

/**
 * @brief Device power state structure
 */
typedef struct {
    int      radar_power_on;        /* Radar power state */
    int      module_4g_power_on;    /* 4G module power state */
    int      hi3516_power_on;       /* HI3516 module power state */
} device_power_state_t;

/**
 * @brief Timing information structure
 */
typedef struct {
    long long current_time;         /* Current RTC time */
    long long period_set_time;      /* Period set time */
    long long power_off_time;       /* Power off time */
    long long radar_on_time;        /* Radar on time */
    long long radar_off_time;       /* Radar off time */
    long long module_4g_on_time;    /* 4G module on time */
    long long hi3516_on_time;       /* HI3516 module on time */
    long long radar_stop_time;      /* Radar stop time */
    long long print_time;           /* Last print time */
} timing_info_t;

/**
 * @brief Statistics information structure
 */
typedef struct {
    uint32_t period_count;          /* Period trigger count */
    uint32_t trigger_count;         /* Total trigger count */
    uint32_t over_3min_count;       /* Over 3 minutes count */
    uint32_t send_failed_count;     /* Send failed count */
    uint32_t send_success_count;    /* Send success count */
    uint8_t  retry_count;           /* Retry count */
} statistics_info_t;

#endif /* _SYSTEM_CONFIG_H_ */
