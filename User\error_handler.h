/**
 * @file    error_handler.h
 * @brief   Error handling and runtime error prevention module
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _ERROR_HANDLER_H_
#define _ERROR_HANDLER_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* Error codes ---------------------------------------------------------------*/
typedef enum {
    ERROR_NONE = 0,
    ERROR_NULL_POINTER,
    ERROR_INVALID_PARAMETER,
    ERROR_BUFFER_OVERFLOW,
    ERROR_MEMORY_ALLOCATION,
    ERROR_TIMEOUT,
    ERROR_COMMUNICATION,
    ERROR_HARDWARE_FAULT,
    ERROR_SYSTEM_OVERLOAD,
    ERROR_UNKNOWN = 0xFF
} error_code_t;

/* Error severity levels */
typedef enum {
    ERROR_SEVERITY_INFO = 0,
    ERROR_SEVERITY_WARNING,
    ERROR_SEVERITY_ERROR,
    ERROR_SEVERITY_CRITICAL
} error_severity_t;

/* Error information structure */
typedef struct {
    error_code_t code;
    error_severity_t severity;
    uint32_t timestamp;
    const char* file;
    int line;
    const char* function;
    char message[128];
} error_info_t;

/* Error statistics structure */
typedef struct {
    uint32_t total_errors;
    uint32_t critical_errors;
    uint32_t warnings;
    uint32_t info_messages;
    uint32_t last_error_time;
    error_code_t last_error_code;
} error_statistics_t;

/* Runtime check macros ------------------------------------------------------*/
#if FEATURE_DEBUG_ENABLED

#define ASSERT(condition) \
    do { \
        if (!(condition)) { \
            Error_Handler_Report(ERROR_UNKNOWN, ERROR_SEVERITY_CRITICAL, \
                               __FILE__, __LINE__, __FUNCTION__, \
                               "Assertion failed: " #condition); \
            while(1); \
        } \
    } while(0)

#define CHECK_NULL_POINTER(ptr) \
    do { \
        if ((ptr) == NULL) { \
            Error_Handler_Report(ERROR_NULL_POINTER, ERROR_SEVERITY_ERROR, \
                               __FILE__, __LINE__, __FUNCTION__, \
                               "NULL pointer: " #ptr); \
            return ERROR_NULL_POINTER; \
        } \
    } while(0)

#define CHECK_BOUNDS(index, max_size) \
    do { \
        if ((index) >= (max_size)) { \
            Error_Handler_Report(ERROR_BUFFER_OVERFLOW, ERROR_SEVERITY_ERROR, \
                               __FILE__, __LINE__, __FUNCTION__, \
                               "Buffer overflow: index=%d, max=%d", (int)(index), (int)(max_size)); \
            return ERROR_BUFFER_OVERFLOW; \
        } \
    } while(0)

#define CHECK_PARAMETER(condition) \
    do { \
        if (!(condition)) { \
            Error_Handler_Report(ERROR_INVALID_PARAMETER, ERROR_SEVERITY_ERROR, \
                               __FILE__, __LINE__, __FUNCTION__, \
                               "Invalid parameter: " #condition); \
            return ERROR_INVALID_PARAMETER; \
        } \
    } while(0)

#define LOG_WARNING(message, ...) \
    Error_Handler_Report(ERROR_NONE, ERROR_SEVERITY_WARNING, \
                       __FILE__, __LINE__, __FUNCTION__, message, ##__VA_ARGS__)

#define LOG_INFO(message, ...) \
    Error_Handler_Report(ERROR_NONE, ERROR_SEVERITY_INFO, \
                       __FILE__, __LINE__, __FUNCTION__, message, ##__VA_ARGS__)

#else
#define ASSERT(condition) ((void)0)
#define CHECK_NULL_POINTER(ptr) ((void)0)
#define CHECK_BOUNDS(index, max_size) ((void)0)
#define CHECK_PARAMETER(condition) ((void)0)
#define LOG_WARNING(message, ...) ((void)0)
#define LOG_INFO(message, ...) ((void)0)
#endif

/* Watchdog and system monitoring macros */
#define WATCHDOG_FEED() \
    do { \
        if (FEATURE_WATCHDOG_ENABLED) { \
            WDT_RestartCmd(ENABLE); \
        } \
    } while(0)

#define SYSTEM_HEALTH_CHECK() \
    do { \
        Error_Handler_System_Health_Check(); \
    } while(0)

/* Memory monitoring macros */
#define SAFE_MALLOC(size) Error_Handler_Safe_Malloc(size, __FILE__, __LINE__)
#define SAFE_FREE(ptr) Error_Handler_Safe_Free((void**)&(ptr), __FILE__, __LINE__)

/* Function prototypes -------------------------------------------------------*/

/**
 * @brief  Initialize error handler module
 * @param  None
 * @retval None
 */
void Error_Handler_Init(void);

/**
 * @brief  Report an error
 * @param  code: error code
 * @param  severity: error severity
 * @param  file: source file name
 * @param  line: source line number
 * @param  function: function name
 * @param  format: message format string
 * @param  ...: variable arguments
 * @retval None
 */
void Error_Handler_Report(error_code_t code, error_severity_t severity,
                         const char* file, int line, const char* function,
                         const char* format, ...);

/**
 * @brief  Get error statistics
 * @param  None
 * @retval Pointer to error statistics structure
 */
error_statistics_t* Error_Handler_Get_Statistics(void);

/**
 * @brief  Clear error statistics
 * @param  None
 * @retval None
 */
void Error_Handler_Clear_Statistics(void);

/**
 * @brief  System health check
 * @param  None
 * @retval None
 */
void Error_Handler_System_Health_Check(void);

/**
 * @brief  Safe memory allocation
 * @param  size: memory size to allocate
 * @param  file: source file name
 * @param  line: source line number
 * @retval Pointer to allocated memory or NULL if failed
 */
void* Error_Handler_Safe_Malloc(size_t size, const char* file, int line);

/**
 * @brief  Safe memory deallocation
 * @param  ptr: pointer to memory pointer
 * @param  file: source file name
 * @param  line: source line number
 * @retval None
 */
void Error_Handler_Safe_Free(void** ptr, const char* file, int line);

/**
 * @brief  Check buffer bounds
 * @param  buffer: buffer pointer
 * @param  index: access index
 * @param  size: buffer size
 * @retval true if bounds are valid, false otherwise
 */
bool Error_Handler_Check_Buffer_Bounds(void* buffer, size_t index, size_t size);

/**
 * @brief  Validate pointer
 * @param  ptr: pointer to validate
 * @retval true if pointer is valid, false otherwise
 */
bool Error_Handler_Validate_Pointer(void* ptr);

/**
 * @brief  Check system resources
 * @param  None
 * @retval true if resources are sufficient, false otherwise
 */
bool Error_Handler_Check_System_Resources(void);

/**
 * @brief  Handle critical error
 * @param  error_info: error information
 * @retval None
 */
void Error_Handler_Critical_Error(const error_info_t* error_info);

/**
 * @brief  Get error code string
 * @param  code: error code
 * @retval Error code string
 */
const char* Error_Handler_Get_Error_String(error_code_t code);

/**
 * @brief  Get severity string
 * @param  severity: error severity
 * @retval Severity string
 */
const char* Error_Handler_Get_Severity_String(error_severity_t severity);

#endif /* _ERROR_HANDLER_H_ */
