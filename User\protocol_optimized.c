/**
 * @file    protocol_optimized.c
 * @brief   Optimized protocol processing module implementation
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "protocol_optimized.h"
#include "system_config.h"
#include "main_optimized.h"
#include "uart_optimized.h"
#include "user_main.h"
#include "rtc.h"
#include "i2cUart.h"

/* Private variables ---------------------------------------------------------*/
protocol_statistics_t g_protocol_stats = {0};
char g_firmware_version[PROTOCOL_MAX_VERSION_LENGTH] = FIRMWARE_VERSION;

/* External variables */
extern unsigned char uart_proc_buf[UART_BUF_LEN];
extern long long g_Time;
extern int g_trigger_distance;
extern int g_trigger_type;
extern int g_last_distance;
extern float g_battery_voltage;
extern int startUpPeriod;
extern long long g_period_set_time;
extern long long g_4g_on_time;
extern uint8_t g_upload_server_flag;
extern uint32_t g_send_success_count;
extern uint32_t g_send_failed_count;
extern int g_current_car_status;
extern uint8_t g_need_retry;
extern int g_red_led_status;
extern int g_green_led_status;
extern int g_blue_led_status;

/* Missing function declarations */
int pack_data(int dev_type, int pack_type, int total_packages, int seq,
              unsigned char* data, int data_len,
              unsigned char* packed_data, int *packed_len);
bool uart2_com_tx_data(int bLenth, uint8_t *bData);
float get_voltage(void);
uint8_t t_HEX2BCD(uint8_t hex_val);
void Delay_ms(uint32_t nTime);

/* Private function prototypes -----------------------------------------------*/
static int Protocol_Validate_JSON(cJSON *cjson);
static void Protocol_Update_Statistics(bool success, bool parse_error, bool memory_error);

/* Private functions ---------------------------------------------------------*/
/**
 * @brief  Validate JSON object
 * @param  cjson: JSON object to validate
 * @retval 0 if valid, -1 if invalid
 */
static int Protocol_Validate_JSON(cJSON *cjson)
{
    if (cjson == NULL) {
        printf("Protocol: Invalid JSON object\r\n");
        return -1;
    }
    return 0;
}

/**
 * @brief  Update protocol statistics
 * @param  success: operation success flag
 * @param  parse_error: parse error flag
 * @param  memory_error: memory error flag
 * @retval None
 */
static void Protocol_Update_Statistics(bool success, bool parse_error, bool memory_error)
{
    if (success) {
        g_protocol_stats.send_success_count++;
    } else {
        g_protocol_stats.send_failed_count++;
    }
    
    if (parse_error) {
        g_protocol_stats.parse_error_count++;
    }
    
    if (memory_error) {
        g_protocol_stats.memory_error_count++;
    }
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  Initialize protocol module
 * @param  None
 * @retval None
 */
void Protocol_Init(void)
{
    memset(&g_protocol_stats, 0, sizeof(protocol_statistics_t));
    strncpy(g_firmware_version, FIRMWARE_VERSION, PROTOCOL_MAX_VERSION_LENGTH - 1);
    g_firmware_version[PROTOCOL_MAX_VERSION_LENGTH - 1] = '\0';
}

/**
 * @brief  Get command string from JSON
 * @param  cjson: JSON object
 * @retval Command string or NULL if not found
 */
const char* Protocol_Get_Command(cJSON *cjson)
{
    cJSON *cmd_obj = cJSON_GetObjectItem(cjson, "cmd");
    if (cmd_obj != NULL && cmd_obj->valuestring != NULL) {
        return cmd_obj->valuestring;
    }
    return NULL;
}

/**
 * @brief  Get ID from JSON
 * @param  cjson: JSON object
 * @retval ID value or 0 if not found
 */
int Protocol_Get_ID(cJSON *cjson)
{
    cJSON *id_obj = cJSON_GetObjectItem(cjson, "id");
    if (id_obj != NULL) {
        return id_obj->valueint;
    }
    return 0;
}

/**
 * @brief  Create JSON response object
 * @param  cmd: command string
 * @param  id: command ID
 * @param  state: response state
 * @retval JSON object pointer or NULL if failed
 */
cJSON* Protocol_Create_Response(const char* cmd, int id, const char* state)
{
    cJSON *root = cJSON_CreateObject();
    if (root == NULL) {
        Protocol_Update_Statistics(false, false, true);
        return NULL;
    }
    
    cJSON_AddItemToObject(root, "cmd", cJSON_CreateString(cmd));
    cJSON_AddItemToObject(root, "id", cJSON_CreateNumber(id));
    cJSON_AddItemToObject(root, "type", cJSON_CreateString(RESPONSE_TYPE_RESPONSE));
    cJSON_AddItemToObject(root, "state", cJSON_CreateString(state));
    
    return root;
}

/**
 * @brief  Send JSON response
 * @param  json_obj: JSON object to send
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_JSON_Response(cJSON* json_obj)
{
    char *str_json = NULL;
    int packed_len = 0;
    bool success = false;
    size_t json_len = 0;

    if (json_obj == NULL) {
        Protocol_Update_Statistics(false, true, false);
        return -1;
    }

    /* Use compact printing for better performance */
    str_json = cJSON_PrintUnformatted(json_obj);
    if (str_json != NULL) {
        json_len = strlen(str_json);

        /* Check buffer size before processing */
        if (json_len < UART_BUF_LEN - 100) {  /* Leave margin for packing overhead */
            memset(uart_proc_buf, 0x00, sizeof(uart_proc_buf));

            if (pack_data(DEVICE_JSON, 0, 1, 0, (unsigned char*)str_json,
                         json_len, uart_proc_buf, &packed_len) == 0) {

                if (uart2_com_tx_data(packed_len, uart_proc_buf)) {
                    success = true;
                } else {
                    printf("Protocol: UART2 send error\r\n");
                }
            } else {
                printf("Protocol: Data packing failed\r\n");
            }
        } else {
            printf("Protocol: JSON too large (%d bytes)\r\n", (int)json_len);
        }

        free(str_json);
        str_json = NULL;
    } else {
        printf("Protocol: JSON print failed\r\n");
        Protocol_Update_Statistics(false, false, true);
    }

    cJSON_Delete(json_obj);
    Protocol_Update_Statistics(success, false, false);

    return success ? 0 : -1;
}

/**
 * @brief  Format time string
 * @param  time_str: output time string buffer
 * @param  max_len: maximum buffer length
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Format_Time_String(char* time_str, int max_len)
{
    if (time_str == NULL || max_len < PROTOCOL_MAX_TIME_STRING) {
        return -1;
    }

    /* Clear buffer first */
    memset(time_str, 0, max_len);

    /* For now, use a fixed time format. In real implementation,
       this would read from RTC */
    int result = snprintf(time_str, max_len, "%04d-%02d-%02d-%02d-%02d-%02d",
                         2024, 1, 1, 12, 0, 0);

    /* Check for truncation */
    if (result >= max_len) {
        time_str[max_len - 1] = '\0';
        return -1;  /* String was truncated */
    }

    return 0;
}

/**
 * @brief  Get bootup type string
 * @param  trigger_type: trigger type value
 * @retval Bootup type string
 */
const char* Protocol_Get_Bootup_Type_String(int trigger_type)
{
    switch (trigger_type) {
        case TRIGGER_TYPE_DRIVE_IN:
            return BOOTUP_TYPE_DRIVE_IN;
        case TRIGGER_TYPE_DRIVE_OUT:
            return BOOTUP_TYPE_DRIVE_OUT;
        case TRIGGER_TYPE_ALARM:
            return BOOTUP_TYPE_ALARM;
        case TRIGGER_TYPE_PERIOD:
            return BOOTUP_TYPE_PERIOD;
        default:
            return BOOTUP_TYPE_DRIVE_IN;
    }
}

/**
 * @brief  Handle get battery voltage command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Battery_Voltage(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    cJSON *root = Protocol_Create_Response(CMD_GET_BATTERY_VOLTAGE, id, RESPONSE_STATE_OK);
    
    if (root == NULL) {
        return -1;
    }
    
    /* Update battery voltage */
    g_battery_voltage = get_voltage();
    
    cJSON_AddItemToObject(root, "voltage", cJSON_CreateNumber(g_battery_voltage));
    
    printf("Protocol: Get battery voltage response, voltage=%.3f\r\n", g_battery_voltage);
    
    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Handle get bootup type command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Bootup_Type(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    char time_str[PROTOCOL_MAX_TIME_STRING];
    cJSON *root = Protocol_Create_Response(CMD_GET_BOOTUP_TYPE, id, RESPONSE_STATE_OK);
    
    if (root == NULL) {
        return -1;
    }
    
    /* Format time string */
    Protocol_Format_Time_String(time_str, sizeof(time_str));
    
    cJSON_AddItemToObject(root, "tm", cJSON_CreateString(time_str));
    cJSON_AddItemToObject(root, "radarDis", cJSON_CreateNumber(g_trigger_distance));
    cJSON_AddItemToObject(root, "bootuptype", 
                         cJSON_CreateString(Protocol_Get_Bootup_Type_String(g_trigger_type)));
    cJSON_AddItemToObject(root, "voltage", cJSON_CreateNumber(g_battery_voltage));
    
    printf("Protocol: Get bootup type response, type=%s\r\n", 
           Protocol_Get_Bootup_Type_String(g_trigger_type));
    
    /* Send response first */
    int result = Protocol_Send_JSON_Response(root);
    
    /* Send transparent info after delay */
    Delay_ms(50);
    Protocol_Send_Transparent_Info();
    
    return result;
}

/**
 * @brief  Handle set bootup period command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Set_Bootup_Period(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    cJSON *period_obj = cJSON_GetObjectItem(cjson, "period");
    cJSON *have_car_obj = cJSON_GetObjectItem(cjson, "havecar");
    cJSON *root = Protocol_Create_Response(CMD_SET_BOOTUP_PERIOD, id, RESPONSE_STATE_OK);
    
    if (root == NULL) {
        return -1;
    }
    
    /* Set period */
    if (period_obj != NULL) {
        int period = period_obj->valueint;
        if (period == 0 || period >= DEFAULT_PERIOD_INTERVAL) {
            startUpPeriod = DEFAULT_PERIOD_INTERVAL;
        } else {
            startUpPeriod = period;
        }
        printf("Protocol: Set period time: %d\r\n", period);
    }
    
    /* Set have car status */
    if (have_car_obj != NULL) {
        g_current_car_status = have_car_obj->valueint;
    }
    
    /* Update period set time */
    g_period_set_time = g_Time;
    
    printf("Protocol: Set bootup period, have_car=%d, interval=%d, set_time=%lld\r\n",
           g_current_car_status, startUpPeriod, g_period_set_time);

    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Handle get bootup period command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Bootup_Period(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    cJSON *root = Protocol_Create_Response(CMD_GET_BOOTUP_PERIOD, id, RESPONSE_STATE_OK);

    if (root == NULL) {
        return -1;
    }

    cJSON_AddItemToObject(root, "period", cJSON_CreateNumber(startUpPeriod));

    printf("Protocol: Get bootup period response, period=%d\r\n", startUpPeriod);

    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Handle get version command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Get_Version(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    cJSON *root = Protocol_Create_Response(CMD_GET_VERSION, id, RESPONSE_STATE_OK);

    if (root == NULL) {
        return -1;
    }

    cJSON_AddItemToObject(root, "version", cJSON_CreateString(g_firmware_version));

    printf("Protocol: Get version response, version=%s\r\n", g_firmware_version);

    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Handle transparent command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Transparent(cJSON *cjson)
{
    cJSON *type_obj = cJSON_GetObjectItem(cjson, "type");

    if (type_obj == NULL || type_obj->valuestring == NULL) {
        return -1;
    }

    if (strncmp(type_obj->valuestring, RESPONSE_TYPE_QUERY, strlen(RESPONSE_TYPE_QUERY)) == 0) {
        /* Handle query type */
        int id = Protocol_Get_ID(cjson);
        cJSON *info_obj = cJSON_GetObjectItem(cjson, "info");
        cJSON *root = Protocol_Create_Response(CMD_TRANSPARENT, id, RESPONSE_STATE_OK);

        if (root == NULL) {
            return -1;
        }

        /* Process info if available */
        if (info_obj != NULL) {
            cJSON *have_car_obj = cJSON_GetObjectItem(info_obj, "haveCar");
            if (have_car_obj != NULL) {
                g_current_car_status = have_car_obj->valueint;
                printf("Protocol: Received transparent server info, have_car=%d\r\n",
                       g_current_car_status);
            }
        }

        return Protocol_Send_JSON_Response(root);
    }
    else if (strncmp(type_obj->valuestring, RESPONSE_TYPE_RESPONSE, strlen(RESPONSE_TYPE_RESPONSE)) == 0) {
        /* Handle response type */
        printf("Protocol: Transparent response received\r\n");
        return 0;
    }

    return -1;
}

/**
 * @brief  Handle snap finished command
 * @param  cjson: JSON object
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Handle_Snap_Finished(cJSON *cjson)
{
    int id = Protocol_Get_ID(cjson);
    cJSON *result_obj = cJSON_GetObjectItem(cjson, "result");
    cJSON *root = Protocol_Create_Response(CMD_SNAP_FINISHED, id, RESPONSE_STATE_OK);

    if (root == NULL) {
        return -1;
    }

    /* Process upload result */
    if (result_obj != NULL) {
        g_upload_server_flag = result_obj->valueint;

        if (g_upload_server_flag) {
            g_send_success_count++;
            printf("Protocol: Upload server OK, 4G on time: %lld\r\n", g_Time - g_4g_on_time);
        } else {
            g_send_failed_count++;
            printf("Protocol: Upload server failed, 4G on time: %lld\r\n", g_Time - g_4g_on_time);
        }
    }

    /* Process time update if available */
    cJSON *year_obj = cJSON_GetObjectItem(cjson, "year");
    cJSON *mon_obj = cJSON_GetObjectItem(cjson, "mon");
    cJSON *day_obj = cJSON_GetObjectItem(cjson, "day");
    cJSON *hour_obj = cJSON_GetObjectItem(cjson, "hour");
    cJSON *min_obj = cJSON_GetObjectItem(cjson, "min");
    cJSON *sec_obj = cJSON_GetObjectItem(cjson, "sec");

    if (year_obj && mon_obj && day_obj && hour_obj && min_obj && sec_obj) {
        uint16_t year = (uint16_t)(year_obj->valueint & 0xFFFF);
        uint8_t month = (uint8_t)(mon_obj->valueint & 0xFF);
        uint8_t day = (uint8_t)(day_obj->valueint & 0xFF);
        uint8_t hour = (uint8_t)(hour_obj->valueint & 0xFF);
        uint8_t minute = (uint8_t)(min_obj->valueint & 0xFF);
        uint8_t second = (uint8_t)(sec_obj->valueint & 0xFF);

        /* Update RTC time */
        long long old_rtc_time = g_Time;

        /* Convert to BCD format for RTC */
        uint8_t year_bcd = t_HEX2BCD(year % 100);
        uint8_t month_bcd = t_HEX2BCD(month);
        uint8_t day_bcd = t_HEX2BCD(day);
        uint8_t hour_bcd = t_HEX2BCD(hour);
        uint8_t minute_bcd = t_HEX2BCD(minute);
        uint8_t second_bcd = t_HEX2BCD(second);

        RTC_SetYear(year_bcd);
        RTC_SetMonth(month_bcd);
        RTC_SetDay(day_bcd);
        RTC_SetHour(hour_bcd);
        RTC_SetMinute(minute_bcd);
        RTC_SetSecond(second_bcd);

        long long new_rtc_time = GetRtcSecond();

        printf("Protocol: RTC time updated from %lld to %lld\r\n", old_rtc_time, new_rtc_time);
    } else {
        printf("Protocol: Snap finished - incomplete time data\r\n");
    }

    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Send initialization finished message
 * @param  None
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_Init_Finished(void)
{
    cJSON *root = cJSON_CreateObject();
    if (root == NULL) {
        Protocol_Update_Statistics(false, false, true);
        return -1;
    }

    cJSON_AddItemToObject(root, "cmd", cJSON_CreateString(CMD_INIT_FINISHED));

    return Protocol_Send_JSON_Response(root);
}

/**
 * @brief  Send transparent information
 * @param  None
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Send_Transparent_Info(void)
{
    /* Implementation would depend on specific transparent info requirements */
    printf("Protocol: Send transparent info\r\n");
    return 0;
}

/**
 * @brief  Parse and process JSON protocol
 * @param  buf: JSON string buffer
 * @retval 0 if successful, -1 if failed
 */
int Protocol_Parse_JSON(char *buf)
{
    cJSON *cjson = NULL;
    const char *cmd = NULL;
    int result = -1;

    if (buf == NULL) {
        Protocol_Update_Statistics(false, true, false);
        return -1;
    }

    cjson = cJSON_Parse(buf);
    if (Protocol_Validate_JSON(cjson) != 0) {
        Protocol_Update_Statistics(false, true, false);
        return -1;
    }

    cmd = Protocol_Get_Command(cjson);
    if (cmd == NULL) {
        printf("Protocol: No command found in JSON\r\n");
        Protocol_Update_Statistics(false, true, false);
        cJSON_Delete(cjson);
        return -1;
    }

    /* Dispatch to appropriate handler */
    if (strcmp(cmd, CMD_GET_BATTERY_VOLTAGE) == 0) {
        result = Protocol_Handle_Get_Battery_Voltage(cjson);
    }
    else if (strcmp(cmd, CMD_GET_BOOTUP_TYPE) == 0) {
        result = Protocol_Handle_Get_Bootup_Type(cjson);
    }
    else if (strncmp(cmd, CMD_TRANSPARENT, strlen(CMD_TRANSPARENT)) == 0) {
        result = Protocol_Handle_Transparent(cjson);
    }
    else if (strcmp(cmd, CMD_SET_BOOTUP_PERIOD) == 0) {
        result = Protocol_Handle_Set_Bootup_Period(cjson);
    }
    else if (strcmp(cmd, CMD_GET_BOOTUP_PERIOD) == 0) {
        result = Protocol_Handle_Get_Bootup_Period(cjson);
    }
    else if (strcmp(cmd, CMD_GET_VERSION) == 0) {
        result = Protocol_Handle_Get_Version(cjson);
    }
    else if (strcmp(cmd, CMD_SNAP_FINISHED) == 0) {
        result = Protocol_Handle_Snap_Finished(cjson);
    }
    else {
        printf("Protocol: Unknown command: %s\r\n", cmd);
        Protocol_Update_Statistics(false, true, false);
    }

    cJSON_Delete(cjson);
    return result;
}

/**
 * @brief  Get protocol statistics
 * @param  None
 * @retval Pointer to statistics structure
 */
protocol_statistics_t* Protocol_Get_Statistics(void)
{
    return &g_protocol_stats;
}
