.\objects\protol.o: ..\User\cJSON\protol.c
.\objects\protol.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\protol.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\protol.o: ..\User\cJSON\cJSON.h
.\objects\protol.o: ..\User\i2cUart.h
.\objects\protol.o: ..\User\cJSON\protol.h
.\objects\protol.o: ..\User\user_main.h
.\objects\protol.o: ..\User\rtc.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_port.h
.\objects\protol.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\protol.o: ..\xcm32lxx_lib\core\core_cm0plus.h
.\objects\protol.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\protol.o: ..\xcm32lxx_lib\core\core_cmInstr.h
.\objects\protol.o: ..\xcm32lxx_lib\core\core_cmFunc.h
.\objects\protol.o: ..\xcm32lxx_lib\core\system_XCM32L.h
.\objects\protol.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_uart.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_timer.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pca.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_flash.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_systick.h
.\objects\protol.o: ..\xcm32lxx_lib\inc\XCM32Lxx_misc.h
.\objects\protol.o: ..\User\uart.h
.\objects\protol.o: ..\User\main.h
