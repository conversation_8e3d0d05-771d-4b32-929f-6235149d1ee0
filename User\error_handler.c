/**
 * @file    error_handler.c
 * @brief   Error handling and runtime error prevention module implementation
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include "error_handler.h"
#include "system_config.h"
#include "main_optimized.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>

/* Private constants ---------------------------------------------------------*/
#define ERROR_LOG_BUFFER_SIZE       512
#define MAX_ERROR_HISTORY           10
#define MEMORY_ALLOCATION_LIMIT     8192
#define SYSTEM_STACK_LIMIT          512

/* Private variables ---------------------------------------------------------*/
static error_statistics_t g_error_stats = {0};
static error_info_t g_error_history[MAX_ERROR_HISTORY];
static uint8_t g_error_history_index = 0;
static uint32_t g_total_allocated_memory = 0;
static bool g_error_handler_initialized = false;

/* Error code strings */
static const char* error_code_strings[] = {
    "ERROR_NONE",
    "ERROR_NULL_POINTER",
    "ERROR_INVALID_PARAMETER",
    "ERROR_BUFFER_OVERFLOW",
    "ERROR_MEMORY_ALLOCATION",
    "ERROR_TIMEOUT",
    "ERROR_COMMUNICATION",
    "ERROR_HARDWARE_FAULT",
    "ERROR_SYSTEM_OVERLOAD",
    "ERROR_UNKNOWN"
};

/* Severity strings */
static const char* severity_strings[] = {
    "INFO",
    "WARNING",
    "ERROR",
    "CRITICAL"
};

/* Private function prototypes -----------------------------------------------*/
static void Error_Handler_Log_Error(const error_info_t* error_info);
static void Error_Handler_Update_Statistics(error_severity_t severity, error_code_t code);
static uint32_t Error_Handler_Get_Timestamp(void);
static void Error_Handler_Store_Error_History(const error_info_t* error_info);

/* Private functions ---------------------------------------------------------*/
/**
 * @brief  Get current timestamp
 * @param  None
 * @retval Current timestamp in milliseconds
 */
static uint32_t Error_Handler_Get_Timestamp(void)
{
    return GetSysTick();
}

/**
 * @brief  Update error statistics
 * @param  severity: error severity
 * @param  code: error code
 * @retval None
 */
static void Error_Handler_Update_Statistics(error_severity_t severity, error_code_t code)
{
    g_error_stats.total_errors++;
    g_error_stats.last_error_time = Error_Handler_Get_Timestamp();
    g_error_stats.last_error_code = code;
    
    switch (severity) {
        case ERROR_SEVERITY_INFO:
            g_error_stats.info_messages++;
            break;
        case ERROR_SEVERITY_WARNING:
            g_error_stats.warnings++;
            break;
        case ERROR_SEVERITY_ERROR:
            /* Fall through */
        case ERROR_SEVERITY_CRITICAL:
            g_error_stats.critical_errors++;
            break;
        default:
            break;
    }
}

/**
 * @brief  Store error in history
 * @param  error_info: error information
 * @retval None
 */
static void Error_Handler_Store_Error_History(const error_info_t* error_info)
{
    if (error_info == NULL) {
        return;
    }
    
    memcpy(&g_error_history[g_error_history_index], error_info, sizeof(error_info_t));
    g_error_history_index = (g_error_history_index + 1) % MAX_ERROR_HISTORY;
}

/**
 * @brief  Log error to output
 * @param  error_info: error information
 * @retval None
 */
static void Error_Handler_Log_Error(const error_info_t* error_info)
{
    if (error_info == NULL) {
        return;
    }
    
    printf("[%s] %s:%d in %s() - %s (Code: %s)\r\n",
           Error_Handler_Get_Severity_String(error_info->severity),
           error_info->file,
           error_info->line,
           error_info->function,
           error_info->message,
           Error_Handler_Get_Error_String(error_info->code));
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  Initialize error handler module
 * @param  None
 * @retval None
 */
void Error_Handler_Init(void)
{
    memset(&g_error_stats, 0, sizeof(error_statistics_t));
    memset(g_error_history, 0, sizeof(g_error_history));
    g_error_history_index = 0;
    g_total_allocated_memory = 0;
    g_error_handler_initialized = true;
    
    LOG_INFO("Error handler initialized");
}

/**
 * @brief  Report an error
 * @param  code: error code
 * @param  severity: error severity
 * @param  file: source file name
 * @param  line: source line number
 * @param  function: function name
 * @param  format: message format string
 * @param  ...: variable arguments
 * @retval None
 */
void Error_Handler_Report(error_code_t code, error_severity_t severity,
                         const char* file, int line, const char* function,
                         const char* format, ...)
{
    error_info_t error_info;
    va_list args;
    
    if (!g_error_handler_initialized) {
        return;
    }
    
    /* Fill error information */
    error_info.code = code;
    error_info.severity = severity;
    error_info.timestamp = Error_Handler_Get_Timestamp();
    error_info.file = file;
    error_info.line = line;
    error_info.function = function;
    
    /* Format message */
    va_start(args, format);
    vsnprintf(error_info.message, sizeof(error_info.message), format, args);
    va_end(args);
    
    /* Update statistics */
    Error_Handler_Update_Statistics(severity, code);
    
    /* Store in history */
    Error_Handler_Store_Error_History(&error_info);
    
    /* Log error */
    Error_Handler_Log_Error(&error_info);
    
    /* Handle critical errors */
    if (severity == ERROR_SEVERITY_CRITICAL) {
        Error_Handler_Critical_Error(&error_info);
    }
}

/**
 * @brief  Get error statistics
 * @param  None
 * @retval Pointer to error statistics structure
 */
error_statistics_t* Error_Handler_Get_Statistics(void)
{
    return &g_error_stats;
}

/**
 * @brief  Clear error statistics
 * @param  None
 * @retval None
 */
void Error_Handler_Clear_Statistics(void)
{
    memset(&g_error_stats, 0, sizeof(error_statistics_t));
    memset(g_error_history, 0, sizeof(g_error_history));
    g_error_history_index = 0;
}

/**
 * @brief  System health check
 * @param  None
 * @retval None
 */
void Error_Handler_System_Health_Check(void)
{
    /* Check memory usage */
    if (g_total_allocated_memory > MEMORY_ALLOCATION_LIMIT) {
        LOG_WARNING("High memory usage: %d bytes", g_total_allocated_memory);
    }
    
    /* Check error rate */
    if (g_error_stats.critical_errors > 10) {
        LOG_WARNING("High critical error count: %d", g_error_stats.critical_errors);
    }
    
    /* Feed watchdog */
    WATCHDOG_FEED();
}

/**
 * @brief  Safe memory allocation
 * @param  size: memory size to allocate
 * @param  file: source file name
 * @param  line: source line number
 * @retval Pointer to allocated memory or NULL if failed
 */
void* Error_Handler_Safe_Malloc(size_t size, const char* file, int line)
{
    void* ptr = NULL;
    
    if (size == 0) {
        Error_Handler_Report(ERROR_INVALID_PARAMETER, ERROR_SEVERITY_WARNING,
                           file, line, "malloc", "Zero size allocation");
        return NULL;
    }
    
    if (g_total_allocated_memory + size > MEMORY_ALLOCATION_LIMIT) {
        Error_Handler_Report(ERROR_MEMORY_ALLOCATION, ERROR_SEVERITY_ERROR,
                           file, line, "malloc", "Memory limit exceeded");
        return NULL;
    }
    
    ptr = malloc(size);
    if (ptr == NULL) {
        Error_Handler_Report(ERROR_MEMORY_ALLOCATION, ERROR_SEVERITY_CRITICAL,
                           file, line, "malloc", "Memory allocation failed");
        return NULL;
    }
    
    g_total_allocated_memory += size;
    return ptr;
}

/**
 * @brief  Safe memory deallocation
 * @param  ptr: pointer to memory pointer
 * @param  file: source file name
 * @param  line: source line number
 * @retval None
 */
void Error_Handler_Safe_Free(void** ptr, const char* file, int line)
{
    if (ptr == NULL || *ptr == NULL) {
        Error_Handler_Report(ERROR_NULL_POINTER, ERROR_SEVERITY_WARNING,
                           file, line, "free", "Attempting to free NULL pointer");
        return;
    }
    
    free(*ptr);
    *ptr = NULL;
}

/**
 * @brief  Check buffer bounds
 * @param  buffer: buffer pointer
 * @param  index: access index
 * @param  size: buffer size
 * @retval true if bounds are valid, false otherwise
 */
bool Error_Handler_Check_Buffer_Bounds(void* buffer, size_t index, size_t size)
{
    if (buffer == NULL) {
        return false;
    }
    
    return (index < size);
}

/**
 * @brief  Validate pointer
 * @param  ptr: pointer to validate
 * @retval true if pointer is valid, false otherwise
 */
bool Error_Handler_Validate_Pointer(void* ptr)
{
    return (ptr != NULL);
}

/**
 * @brief  Check system resources
 * @param  None
 * @retval true if resources are sufficient, false otherwise
 */
bool Error_Handler_Check_System_Resources(void)
{
    /* Check memory usage */
    if (g_total_allocated_memory > MEMORY_ALLOCATION_LIMIT * 0.8) {
        return false;
    }
    
    /* Check error rate */
    if (g_error_stats.critical_errors > 5) {
        return false;
    }
    
    return true;
}

/**
 * @brief  Handle critical error
 * @param  error_info: error information
 * @retval None
 */
void Error_Handler_Critical_Error(const error_info_t* error_info)
{
    if (error_info == NULL) {
        return;
    }
    
    /* Disable interrupts */
    __disable_irq();
    
    /* Log critical error */
    printf("CRITICAL ERROR: %s\r\n", error_info->message);
    
    /* System reset or safe mode */
    /* In a real implementation, this might trigger a system reset */
    /* For now, just enable interrupts and continue */
    __enable_irq();
}

/**
 * @brief  Get error code string
 * @param  code: error code
 * @retval Error code string
 */
const char* Error_Handler_Get_Error_String(error_code_t code)
{
    if (code < ARRAY_SIZE(error_code_strings)) {
        return error_code_strings[code];
    }
    return "UNKNOWN_ERROR";
}

/**
 * @brief  Get severity string
 * @param  severity: error severity
 * @retval Severity string
 */
const char* Error_Handler_Get_Severity_String(error_severity_t severity)
{
    if (severity < ARRAY_SIZE(severity_strings)) {
        return severity_strings[severity];
    }
    return "UNKNOWN_SEVERITY";
}
