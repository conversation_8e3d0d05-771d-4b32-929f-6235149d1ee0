/**
  ******************************************************************************
  * @file    XCM32Lxx_misc.h
  * <AUTHOR>
  * @version V1.0.5
  * @date    2018-11-25
  * @brief   This file contains all the functions prototypes for the MISC
  *          firmware library.
  ******************************************************************************
  * @attention
  *
  * COPYRIGHT 2013-2018; Hangzhou GreenWhale technology co., LTD.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __XCM32LXX_MISC_H
#define __XCM32LXX_MISC_H

#ifdef __cplusplus
 extern "C" {
#endif

#include "XCM32L.h"

void NVIC_DeInit(void);

#ifdef __cplusplus
}
#endif

#endif /* __XCM32LXX_MISC_H */

/**
  * @}
  */

/**
  * @}
  */ 

/************************ (C) COPYRIGHT XCMTRI *****END OF FILE****/
