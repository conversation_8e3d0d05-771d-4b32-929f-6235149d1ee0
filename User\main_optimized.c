/**
 * @file    main_optimized.c
 * @brief   Optimized main application file with improved structure
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include "XCM32Lxx_conf.h"
#include "system_config.h"
#include "main_optimized.h"
#include "user_main_optimized.h"
#include "gpio_optimized.h"
#include "uart_optimized.h"
#include "error_handler.h"
#include "rtc.h"
#include "i2cUart.h"
#include <stdio.h>

/* Private variables ---------------------------------------------------------*/
extern long long g_Time;
extern uint8_t g_stopBoost;

/* System timing variables */
static volatile uint32_t g_sys_tick_counter = 0;
static unsigned long timing_delay = 0;

/* Private function prototypes -----------------------------------------------*/
static void System_Clock_Config(void);
static void System_Interrupt_Config(void);
static void System_Peripheral_Init(void);
static void System_GPIO_Init(void);
static void System_Timer_Init(void);
static void System_Communication_Init(void);

/**
 * @brief  System clock configuration
 * @param  None
 * @retval None
 */
static void System_Clock_Config(void)
{
    /* Configure HSI clock */
    CMU_HSIConfig(CMU_IRC16M_Trim_16M, CMU_HSI_ON);
    CMU_SysClkConfig(CMU_SysClkSource_HSI);
    CMU_HCLKConfig(CMU_SYSCLK_Div1);
    CMU_PCLKConfig(CMU_HCLK_Div1);
}

/**
 * @brief  System interrupt configuration
 * @param  None
 * @retval None
 */
static void System_Interrupt_Config(void)
{
    /* Set interrupt vector table offset for bootloader */
    SCB->VTOR = FLASH_BASE | APP_START_ADDR;
}

/**
 * @brief  System GPIO initialization
 * @param  None
 * @retval None
 */
static void System_GPIO_Init(void)
{
    uf_GPIO_Init();
}

/**
 * @brief  System timer initialization
 * @param  None
 * @retval None
 */
static void System_Timer_Init(void)
{
    Timer2_PwmOut_Init();
    Timer1_PwmOut_Init();
}

/**
 * @brief  System communication initialization
 * @param  None
 * @retval None
 */
static void System_Communication_Init(void)
{
    UART4_Init();   /* Debug print interface */
    UART2_Init();   /* Communication with main module */
    UART1_Init(9600, 0);  /* Communication with radar */
}

/**
 * @brief  System peripheral initialization
 * @param  None
 * @retval None
 */
static void System_Peripheral_Init(void)
{
    RTC_Run_Init();
    SysTickConfigure();
    WDT_Configure();
}

/**
 * @brief  Complete system initialization
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    /* Initialize error handler first */
    Error_Handler_Init();

    /* Configure system clock */
    System_Clock_Config();

    /* Configure interrupt vector table */
    System_Interrupt_Config();

    /* Initialize GPIO */
    System_GPIO_Init();

    /* Initialize communication interfaces */
    System_Communication_Init();

    /* Initialize RTC and system tick */
    System_Peripheral_Init();

    /* Initialize timers */
    System_Timer_Init();

    LOG_INFO("System initialization completed");
}

/**
 * @brief  Hardware initialization sequence
 * @param  None
 * @retval None
 */
void  Hardware_Init(void)
{
    /* Initialize external variables */
    g_Time = 0;
    g_stopBoost = 0;

    /* Initial delay for system stabilization */
    Delay_ms(1000);

    /* Start booster */
    Booster_Start();
    Delay_ms(1000);

    /* Get initial RTC time */
    g_Time = GetRtcSecond();
}

/**
 * @brief  Enter deep sleep mode
 * @param  None
 * @retval None
 */
void PMU_EnterDeepSleep5Mode(void)
{
    /* Enter Cortex deep sleep mode */
    /* After entering deep sleep, HSF clock will automatically close */
    SCB->SCR |= SCB_SCR_SLEEPDEEP_Msk;
    SCB->SCR &= ~SCB_SCR_SLEEPONEXIT_Msk;
    __WFI();
}

/**
 * @brief  Simple delay function (busy wait)
 * @param  ms: delay time in milliseconds
 * @retval None
 */
void DelayMs(uint16_t ms)
{
    /* Use system tick based delay for better performance */
    Delay_ms((uint32_t)ms);
}

/**
 * @brief  Get current system tick count
 * @param  None
 * @retval Current system tick count
 */
uint32_t GetSysTick(void)
{
    return g_sys_tick_counter;
}

/**
 * @brief  System tick interrupt handler
 * @param  None
 * @retval None
 */
void SysTick_Handler(void)
{
    g_sys_tick_counter++;
}

/**
 * @brief  Non-blocking delay function using system tick
 * @param  nTime: delay time in milliseconds
 * @retval None
 */
void Delay_ms(uint32_t nTime)
{
    uint32_t tick_start = GetSysTick();
    uint32_t tick_current;

    if (nTime == 0) {
        return;  /* No delay needed */
    }

    while (1) {
        tick_current = GetSysTick();

        /* Handle timer overflow */
        if (tick_current >= tick_start) {
            if ((tick_current - tick_start) >= nTime) {
                break;
            }
        } else {
            /* Timer overflow occurred */
            if ((UINT32_MAX - tick_start + tick_current) >= nTime) {
                break;
            }
        }

        /* Allow other tasks to run during delay */
        __WFI();  /* Wait for interrupt - saves power */
    }
}

/**
 * @brief  Configure system tick timer
 * @param  None
 * @retval None
 */
void SysTickConfigure(void)
{
    /* Clear current value register */
    SysTick->VAL = 0;
    
    /* Set reload value for 1ms interrupt with 16MHz clock */
    SysTick_SetReloadValue(16000);
    
    /* Select HSI clock source */
    SysTick_CLKSourceConfig(SysTick_ClkSource_ExtRefClk_HSI);
    
    /* Enable SysTick interrupt */
    SysTick_ITConfig(ENABLE);
    SysTick_Cmd(DISABLE);
    
    /* Configure NVIC for SysTick */
    __disable_irq();
    NVIC_DisableIRQ(SysTick_IRQn);
    NVIC_ClearPendingIRQ(SysTick_IRQn);
    NVIC_SetPriority(SysTick_IRQn, 0x3);
    NVIC_EnableIRQ(SysTick_IRQn);
    __enable_irq();
    
    /* Enable SysTick timer */
    SysTick_Cmd(ENABLE);
}

/**
 * @brief  Printf redirection to UART4
 * @param  ch: character to send
 * @param  stream: file stream (unused)
 * @retval Character sent
 */
int fputc(int ch, FILE *stream)
{
    uint32_t timeout_counter = 0;
    
    UART_SendData(UART_4, ch);
    
    while (UART_GetLineStatus(UART_4, UART_LSFLAG_TXEmpty) == RESET) {
        timeout_counter++;
        if (timeout_counter >= 1000) {
            break;  /* Prevent infinite loop */
        }
    }
    
    return ch;
}

/**
 * @brief  Configure GPIO for booster control
 * @param  mode: GPIO mode (PORT_Mode_OUT or PORT_Mode_IN)
 * @retval None
 */
static void Booster_GPIO_Config(PORT_Mode_TypeDef mode)
{
    PORT_InitTypeDef PORT_InitStruct;

    PORT_StructInit(&PORT_InitStruct);
    PORT_InitStruct.PORT_Pin = POWER_BOOST_PIN;
    PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
    PORT_InitStruct.PORT_Mode = mode;
    PORT_InitStruct.PORT_OutType = PORT_OutType_PP;
    PORT_InitStruct.PORT_PullHigh = PORT_PH_NoPullHigh;
    PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
    PORT_Init(POWER_BOOST_PORT, &PORT_InitStruct);
}

/**
 * @brief  Initialize booster
 * @param  None
 * @retval None
 */
void Booster_Init(void)
{
    Booster_GPIO_Config(PORT_Mode_OUT);
    PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET);
    Delay_ms(120);
    printf("\r\n-----[%s]-------[%lld]\r\n", __FUNCTION__, g_Time);

    Booster_GPIO_Config(PORT_Mode_IN);
    g_stopBoost = 0;
    Delay_ms(250);
}

/**
 * @brief  Start booster
 * @param  None
 * @retval None
 */
void Booster_Start(void)
{
    Booster_GPIO_Config(PORT_Mode_OUT);
    PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET);
    Delay_ms(140);
    printf("\r\n----[%s]-------------[%lld]\r\n", __FUNCTION__, g_Time);

    Booster_GPIO_Config(PORT_Mode_IN);
    Delay_ms(250);
}

/**
 * @brief  Stop booster (double short press)
 * @param  None
 * @retval None
 */
void Booster_Stop(void)
{
    /* First short press */
    Booster_GPIO_Config(PORT_Mode_OUT);
    PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET);
    Delay_ms(80);
    Booster_GPIO_Config(PORT_Mode_IN);

    /* Delay between presses */
    Delay_ms(100);

    /* Second short press */
    Booster_GPIO_Config(PORT_Mode_OUT);
    PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET);
    Delay_ms(80);
    printf("\r\n-----[%s]-------[%lld]\r\n", __FUNCTION__, g_Time);
    Booster_GPIO_Config(PORT_Mode_IN);
}

/**
 * @brief  Deinitialize booster (turn off)
 * @param  None
 * @retval None
 */
void Booster_DeInit(void)
{
    /* Same as Booster_Stop but set stop flag */
    Booster_Stop();
    g_stopBoost = 1;
}

/**
 * @brief  Configure watchdog timer
 * @param  None
 * @retval None
 */
void WDT_Configure(void)
{
    WDT_InitTypeDef WDT_InitStruct;

    /* Configure LSI clock for watchdog */
    CMU_LSIConfig(CMU_CLKStartUpTime3, CMU_LSI_ON);
    while (CMU_WaitForSysClkStartUp(CMU_SysClkSource_LSI) != SET);
    CMU_WDTCLKConfig(CMU_WDTCLK_IRC38K);

    /* Enable watchdog peripheral clock */
    CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_WDT, ENABLE);

    /* Initialize watchdog */
    WDT_DeInit();
    WDT_StructInit(&WDT_InitStruct);
    WDT_InitStruct.WDT_TimeOverCycle = 0x7;
    WDT_InitStruct.WDT_RespMode = WDT_RespMode_Interrupt;
    WDT_InitStruct.WDT_RstPulseLen = WDT_RstPulseLen_8PClkCycles;
    WDT_Init(&WDT_InitStruct);

    /* Configure watchdog interrupt */
    __disable_irq();
    NVIC_DisableIRQ(WDT_IRQn);
    NVIC_ClearPendingIRQ(WDT_IRQn);
    NVIC_SetPriority(WDT_IRQn, 0x0);
    NVIC_EnableIRQ(WDT_IRQn);
    __enable_irq();

    /* Enable watchdog */
    WDT_Cmd(ENABLE);
    WDT_RestartCmd(ENABLE);
}

/**
 * @brief  Watchdog interrupt handler
 * @param  None
 * @retval None
 */
void WDT_IRQHandler(void)
{
    if (WDT_GetITStatus() == SET) {
        WDT_ClearITFlag();
        WDT_RestartCmd(ENABLE);  /* Feed watchdog */
    }
}

/**
 * @brief  Disable watchdog timer
 * @param  None
 * @retval None
 */
void WDT_DeConfigure(void)
{
    WDT_Cmd(DISABLE);

    __disable_irq();
    NVIC_DisableIRQ(WDT_IRQn);
    NVIC_ClearPendingIRQ(WDT_IRQn);
    __enable_irq();

    CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_WDT, DISABLE);
    WDT_DeInit();
}

/**
 * @brief  Initialize Timer1 for PWM output (P5.1 indicator LED)
 * @param  None
 * @retval None
 */
void Timer1_PwmOut_Init(void)
{
    TIMER_InitTypeDef TIMER_InitStruct;

    TIMER_DeInit(TIMER_1);
    CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_TIMER1, ENABLE);

    TIMER_StructInit(&TIMER_InitStruct);
    TIMER_InitStruct.TIMER_Mode = TIMER_Mode_CustomizeCounter;
    TIMER_InitStruct.TIMER_CntClkSel = TIMER_CntClkSel_APBusClk;
    TIMER_InitStruct.TIMER_PWMOutput = TIMER_PWMOutput_Enable;
    TIMER_InitStruct.TIMER_GateCtrl = TIMER_GateCtrl_Disable;
    TIMER_Init(TIMER_1, &TIMER_InitStruct);

    TIMER_SetLoadCounter0(TIMER_1, 10000);
    TIMER_SetLoadCounter1(TIMER_1, 500);

    TIMER_Cmd(TIMER_1, ENABLE);
}

/**
 * @brief  Initialize Timer2 for PWM output (P5.0 fill light)
 * @param  None
 * @retval None
 */
void Timer2_PwmOut_Init(void)
{
    TIMER_InitTypeDef TIMER_InitStruct;

    TIMER_DeInit(TIMER_2);
    CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_TIMER2, ENABLE);

    TIMER_StructInit(&TIMER_InitStruct);
    TIMER_InitStruct.TIMER_Mode = TIMER_Mode_CustomizeCounter;
    TIMER_InitStruct.TIMER_CntClkSel = TIMER_CntClkSel_APBusClk;
    TIMER_InitStruct.TIMER_PWMOutput = TIMER_PWMOutput_Enable;
    TIMER_InitStruct.TIMER_GateCtrl = TIMER_GateCtrl_Disable;
    TIMER_Init(TIMER_2, &TIMER_InitStruct);

    TIMER_SetLoadCounter0(TIMER_2, 80000);
    TIMER_SetLoadCounter1(TIMER_2, 0);

    TIMER_Cmd(TIMER_2, ENABLE);
}

/**
 * @brief  Main function
 * @param  None
 * @retval None
 */
int main(void)
{
    /* Initialize system */
    System_Init();

    /* Initialize hardware */
    Hardware_Init();

    /* Enter main application loop */
    while (1) {
        user_main();
        Delay_ms(2000);
    }
}
