/**
 * @file    performance_monitor.h
 * @brief   Performance monitoring and optimization module
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _PERFORMANCE_MONITOR_H_
#define _PERFORMANCE_MONITOR_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* Performance metrics structure */
typedef struct {
    uint32_t cpu_usage_percent;
    uint32_t memory_usage_bytes;
    uint32_t memory_peak_bytes;
    uint32_t stack_usage_bytes;
    uint32_t heap_usage_bytes;
    uint32_t interrupt_count;
    uint32_t context_switches;
    uint32_t idle_time_ms;
    uint32_t active_time_ms;
} performance_metrics_t;

/* Function execution profiling */
typedef struct {
    const char* function_name;
    uint32_t call_count;
    uint32_t total_time_us;
    uint32_t max_time_us;
    uint32_t min_time_us;
    uint32_t avg_time_us;
} function_profile_t;

/* System resource monitoring */
typedef struct {
    uint32_t uart1_rx_buffer_usage;
    uint32_t uart2_rx_buffer_usage;
    uint32_t json_memory_pool_usage;
    uint32_t radar_data_buffer_usage;
    uint32_t communication_errors;
    uint32_t timeout_events;
} resource_monitor_t;

/* Performance optimization flags */
typedef struct {
    bool enable_profiling;
    bool enable_memory_tracking;
    bool enable_stack_monitoring;
    bool enable_interrupt_monitoring;
    bool enable_resource_monitoring;
} performance_config_t;

/* Profiling macros ----------------------------------------------------------*/
#if FEATURE_STATISTICS

#define PROFILE_FUNCTION_START(name) \
    uint32_t profile_start_##name = GetSysTick()

#define PROFILE_FUNCTION_END(name) \
    do { \
        uint32_t profile_end_##name = GetSysTick(); \
        Performance_Monitor_Record_Function(#name, profile_end_##name - profile_start_##name); \
    } while(0)

#define PROFILE_BLOCK_START() \
    uint32_t profile_block_start = GetSysTick()

#define PROFILE_BLOCK_END(description) \
    do { \
        uint32_t profile_block_end = GetSysTick(); \
        Performance_Monitor_Record_Block(description, profile_block_end - profile_block_start); \
    } while(0)

#define MEMORY_USAGE_CHECK() \
    Performance_Monitor_Check_Memory_Usage()

#define STACK_USAGE_CHECK() \
    Performance_Monitor_Check_Stack_Usage()

#else
#define PROFILE_FUNCTION_START(name) ((void)0)
#define PROFILE_FUNCTION_END(name) ((void)0)
#define PROFILE_BLOCK_START() ((void)0)
#define PROFILE_BLOCK_END(description) ((void)0)
#define MEMORY_USAGE_CHECK() ((void)0)
#define STACK_USAGE_CHECK() ((void)0)
#endif

/* Function prototypes -------------------------------------------------------*/

/**
 * @brief  Initialize performance monitor
 * @param  config: performance monitoring configuration
 * @retval None
 */
void Performance_Monitor_Init(const performance_config_t* config);

/**
 * @brief  Update performance metrics
 * @param  None
 * @retval None
 */
void Performance_Monitor_Update(void);

/**
 * @brief  Get current performance metrics
 * @param  None
 * @retval Pointer to performance metrics structure
 */
performance_metrics_t* Performance_Monitor_Get_Metrics(void);

/**
 * @brief  Get resource monitoring data
 * @param  None
 * @retval Pointer to resource monitor structure
 */
resource_monitor_t* Performance_Monitor_Get_Resources(void);

/**
 * @brief  Record function execution time
 * @param  function_name: name of the function
 * @param  execution_time_us: execution time in microseconds
 * @retval None
 */
void Performance_Monitor_Record_Function(const char* function_name, uint32_t execution_time_us);

/**
 * @brief  Record code block execution time
 * @param  description: description of the code block
 * @param  execution_time_us: execution time in microseconds
 * @retval None
 */
void Performance_Monitor_Record_Block(const char* description, uint32_t execution_time_us);

/**
 * @brief  Check memory usage
 * @param  None
 * @retval Current memory usage in bytes
 */
uint32_t Performance_Monitor_Check_Memory_Usage(void);

/**
 * @brief  Check stack usage
 * @param  None
 * @retval Current stack usage in bytes
 */
uint32_t Performance_Monitor_Check_Stack_Usage(void);

/**
 * @brief  Monitor UART buffer usage
 * @param  uart_num: UART number (1 or 2)
 * @retval Buffer usage percentage (0-100)
 */
uint32_t Performance_Monitor_UART_Buffer_Usage(uint8_t uart_num);

/**
 * @brief  Monitor JSON memory pool usage
 * @param  None
 * @retval Memory pool usage percentage (0-100)
 */
uint32_t Performance_Monitor_JSON_Pool_Usage(void);

/**
 * @brief  Calculate CPU usage
 * @param  None
 * @retval CPU usage percentage (0-100)
 */
uint32_t Performance_Monitor_Calculate_CPU_Usage(void);

/**
 * @brief  Reset performance statistics
 * @param  None
 * @retval None
 */
void Performance_Monitor_Reset_Statistics(void);

/**
 * @brief  Print performance report
 * @param  None
 * @retval None
 */
void Performance_Monitor_Print_Report(void);

/**
 * @brief  Check for performance bottlenecks
 * @param  None
 * @retval true if bottlenecks detected, false otherwise
 */
bool Performance_Monitor_Check_Bottlenecks(void);

/**
 * @brief  Optimize system performance
 * @param  None
 * @retval None
 */
void Performance_Monitor_Optimize_System(void);

/**
 * @brief  Get function profile by name
 * @param  function_name: name of the function
 * @retval Pointer to function profile or NULL if not found
 */
function_profile_t* Performance_Monitor_Get_Function_Profile(const char* function_name);

/**
 * @brief  Enable/disable performance monitoring
 * @param  enable: enable flag
 * @retval None
 */
void Performance_Monitor_Enable(bool enable);

/**
 * @brief  Set monitoring interval
 * @param  interval_ms: monitoring interval in milliseconds
 * @retval None
 */
void Performance_Monitor_Set_Interval(uint32_t interval_ms);

/**
 * @brief  Check system health from performance perspective
 * @param  None
 * @retval true if system is healthy, false otherwise
 */
bool Performance_Monitor_System_Health_Check(void);

/* Optimization helper functions */
/**
 * @brief  Optimize memory usage
 * @param  None
 * @retval None
 */
void Performance_Optimize_Memory(void);

/**
 * @brief  Optimize CPU usage
 * @param  None
 * @retval None
 */
void Performance_Optimize_CPU(void);

/**
 * @brief  Optimize communication buffers
 * @param  None
 * @retval None
 */
void Performance_Optimize_Buffers(void);

/**
 * @brief  Optimize interrupt handling
 * @param  None
 * @retval None
 */
void Performance_Optimize_Interrupts(void);

#endif /* _PERFORMANCE_MONITOR_H_ */
