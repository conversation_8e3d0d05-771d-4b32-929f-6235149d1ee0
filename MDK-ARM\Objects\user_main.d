.\objects\user_main.o: ..\User\user_main.c
.\objects\user_main.o: ..\User\gpio.h
.\objects\user_main.o: ..\User\cJSON\protol.h
.\objects\user_main.o: ..\User\rtc.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_port.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\core_cm0plus.h
.\objects\user_main.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\core_cmInstr.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\core_cmFunc.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\system_XCM32L.h
.\objects\user_main.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_uart.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_timer.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pca.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_flash.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_systick.h
.\objects\user_main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_misc.h
.\objects\user_main.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\user_main.o: ..\User\uart.h
.\objects\user_main.o: ..\User\user_main.h
.\objects\user_main.o: ..\User\i2cUart.h
.\objects\user_main.o: ..\User\main.h
