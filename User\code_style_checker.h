/**
 * @file    code_style_checker.h
 * @brief   Code style checking and consistency validation
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _CODE_STYLE_CHECKER_H_
#define _CODE_STYLE_CHECKER_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Code style validation results */
typedef enum {
    STYLE_CHECK_PASS = 0,
    STYLE_CHECK_WARNING,
    STYLE_CHECK_ERROR
} style_check_result_t;

/* Naming convention types */
typedef enum {
    NAMING_GLOBAL_VARIABLE,     /* g_module_variable_name */
    NAMING_STATIC_VARIABLE,     /* s_variable_name */
    NAMING_LOCAL_VARIABLE,      /* variable_name */
    NAMING_FUNCTION,            /* Modu<PERSON>_Function_Name */
    NAMING_CONSTANT,            /* MODULE_CONSTANT_NAME */
    NAMING_TYPE,                /* module_type_t */
    NAMING_ENUM                 /* MODULE_ENUM_VALUE */
} naming_convention_t;

/* Code style metrics */
typedef struct {
    uint32_t total_functions;
    uint32_t functions_too_long;
    uint32_t lines_too_long;
    uint32_t naming_violations;
    uint32_t comment_violations;
    uint32_t indentation_errors;
    uint32_t magic_numbers;
} style_metrics_t;

/* Style checking configuration */
typedef struct {
    uint32_t max_function_length;
    uint32_t max_line_length;
    bool check_naming_conventions;
    bool check_comments;
    bool check_indentation;
    bool check_magic_numbers;
} style_config_t;

/* Standard style configuration */
#define STANDARD_STYLE_CONFIG { \
    .max_function_length = 50, \
    .max_line_length = 100, \
    .check_naming_conventions = true, \
    .check_comments = true, \
    .check_indentation = true, \
    .check_magic_numbers = true \
}

/* Function prototypes -------------------------------------------------------*/

/**
 * @brief  Initialize code style checker
 * @param  config: style checking configuration
 * @retval None
 */
void Code_Style_Checker_Init(const style_config_t* config);

/**
 * @brief  Check naming convention
 * @param  name: name to check
 * @param  type: naming convention type
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Naming(const char* name, naming_convention_t type);

/**
 * @brief  Check function length
 * @param  function_name: name of the function
 * @param  line_count: number of lines in function
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Function_Length(const char* function_name, uint32_t line_count);

/**
 * @brief  Check line length
 * @param  line: line content
 * @param  line_number: line number
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Line_Length(const char* line, uint32_t line_number);

/**
 * @brief  Check for magic numbers
 * @param  line: line content
 * @param  line_number: line number
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Magic_Numbers(const char* line, uint32_t line_number);

/**
 * @brief  Check comment quality
 * @param  comment: comment content
 * @param  line_number: line number
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Comment(const char* comment, uint32_t line_number);

/**
 * @brief  Check indentation
 * @param  line: line content
 * @param  line_number: line number
 * @retval Style check result
 */
style_check_result_t Code_Style_Check_Indentation(const char* line, uint32_t line_number);

/**
 * @brief  Get style metrics
 * @param  None
 * @retval Pointer to style metrics structure
 */
style_metrics_t* Code_Style_Get_Metrics(void);

/**
 * @brief  Reset style metrics
 * @param  None
 * @retval None
 */
void Code_Style_Reset_Metrics(void);

/**
 * @brief  Print style report
 * @param  None
 * @retval None
 */
void Code_Style_Print_Report(void);

/**
 * @brief  Validate naming convention
 * @param  name: name to validate
 * @param  type: expected naming type
 * @retval true if valid, false otherwise
 */
bool Code_Style_Validate_Naming(const char* name, naming_convention_t type);

/**
 * @brief  Suggest naming correction
 * @param  name: original name
 * @param  type: naming convention type
 * @param  suggestion: output buffer for suggestion
 * @param  max_len: maximum suggestion length
 * @retval true if suggestion generated, false otherwise
 */
bool Code_Style_Suggest_Naming(const char* name, naming_convention_t type, 
                               char* suggestion, size_t max_len);

/* Utility functions for style checking */
/**
 * @brief  Check if character is valid for identifier
 * @param  c: character to check
 * @retval true if valid, false otherwise
 */
bool Code_Style_Is_Valid_Identifier_Char(char c);

/**
 * @brief  Check if string is camelCase
 * @param  str: string to check
 * @retval true if camelCase, false otherwise
 */
bool Code_Style_Is_CamelCase(const char* str);

/**
 * @brief  Check if string is snake_case
 * @param  str: string to check
 * @retval true if snake_case, false otherwise
 */
bool Code_Style_Is_Snake_Case(const char* str);

/**
 * @brief  Check if string is UPPER_CASE
 * @param  str: string to check
 * @retval true if UPPER_CASE, false otherwise
 */
bool Code_Style_Is_Upper_Case(const char* str);

/**
 * @brief  Convert string to snake_case
 * @param  input: input string
 * @param  output: output buffer
 * @param  max_len: maximum output length
 * @retval true if conversion successful, false otherwise
 */
bool Code_Style_To_Snake_Case(const char* input, char* output, size_t max_len);

/**
 * @brief  Convert string to UPPER_CASE
 * @param  input: input string
 * @param  output: output buffer
 * @param  max_len: maximum output length
 * @retval true if conversion successful, false otherwise
 */
bool Code_Style_To_Upper_Case(const char* input, char* output, size_t max_len);

/**
 * @brief  Convert string to PascalCase
 * @param  input: input string
 * @param  output: output buffer
 * @param  max_len: maximum output length
 * @retval true if conversion successful, false otherwise
 */
bool Code_Style_To_Pascal_Case(const char* input, char* output, size_t max_len);

#endif /* _CODE_STYLE_CHECKER_H_ */
