/**
 * @file    main_optimized.h
 * @brief   Optimized main application header file with GPIO control macros
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef __MAIN_OPTIMIZED_H
#define __MAIN_OPTIMIZED_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "system_config.h"
#include "XCM32Lxx_port.h"

/* GPIO Pin Definitions ------------------------------------------------------*/
/* LED pins */
#define LED_RED_PORT            PORT_0
#define LED_RED_PIN             PORT_Pin_9
#define LED_GREEN_PORT          PORT_0
#define LED_GREEN_PIN           PORT_Pin_10
#define LED_BLUE_PORT           PORT_0
#define LED_BLUE_PIN            PORT_Pin_12
#define LED_SYS_RUN_PORT        PORT_0
#define LED_SYS_RUN_PIN         PORT_Pin_4

/* PWM control pins */
#define PWM_STATUS_LED_PORT     PORT_5
#define PWM_STATUS_LED_PIN      PORT_Pin_1
#define PWM_FILL_LIGHT_PORT     PORT_5
#define PWM_FILL_LIGHT_PIN      PORT_Pin_0

/* Power control pins */
#define POWER_RADAR_PORT        PORT_0
#define POWER_RADAR_PIN         PORT_Pin_3
#define POWER_4G_PORT           PORT_1
#define POWER_4G_PIN            PORT_Pin_9
#define POWER_MAIN_PORT         PORT_2
#define POWER_MAIN_PIN          PORT_Pin_7
#define POWER_BOOST_PORT        PORT_1
#define POWER_BOOST_PIN         PORT_Pin_10

/* LED Control Macros --------------------------------------------------------*/
/* Red LED (vehicle present) */
#define red_led_ON              PORT_WriteBit(LED_RED_PORT, LED_RED_PIN, Bit_SET)
#define red_led_OFF             PORT_WriteBit(LED_RED_PORT, LED_RED_PIN, Bit_RESET)
#define red_led_Toggle          PORT_ToggleBit(LED_RED_PORT, LED_RED_PIN)

/* Green LED (no vehicle) */
#define green_led_ON            PORT_WriteBit(LED_GREEN_PORT, LED_GREEN_PIN, Bit_SET)
#define green_led_OFF           PORT_WriteBit(LED_GREEN_PORT, LED_GREEN_PIN, Bit_RESET)
#define green_led_Toggle        PORT_ToggleBit(LED_GREEN_PORT, LED_GREEN_PIN)

/* Blue LED (abnormal status) */
#define blue_led_ON             PORT_WriteBit(LED_BLUE_PORT, LED_BLUE_PIN, Bit_SET)
#define blue_led_OFF            PORT_WriteBit(LED_BLUE_PORT, LED_BLUE_PIN, Bit_RESET)
#define blue_led_Toggle         PORT_ToggleBit(LED_BLUE_PORT, LED_BLUE_PIN)

/* System run LED */
#define sys_run_led_ON          PORT_WriteBit(LED_SYS_RUN_PORT, LED_SYS_RUN_PIN, Bit_SET)
#define sys_run_led_OFF         PORT_WriteBit(LED_SYS_RUN_PORT, LED_SYS_RUN_PIN, Bit_RESET)
#define sys_run_led_Toggle      PORT_ToggleBit(LED_SYS_RUN_PORT, LED_SYS_RUN_PIN)

/* PWM Control Macros --------------------------------------------------------*/
/* Status LED brightness control */
#define ST_StatusLED_PWM_ON     PORT_WriteBit(PWM_STATUS_LED_PORT, PWM_STATUS_LED_PIN, Bit_SET)
#define ST_StatusLED_PWM_OFF    PORT_WriteBit(PWM_STATUS_LED_PORT, PWM_STATUS_LED_PIN, Bit_RESET)

/* Fill light brightness control */
#define ST_FillLightLED_PWM_ON  PORT_WriteBit(PWM_FILL_LIGHT_PORT, PWM_FILL_LIGHT_PIN, Bit_SET)
#define ST_FillLightLED_PWM_OFF PORT_WriteBit(PWM_FILL_LIGHT_PORT, PWM_FILL_LIGHT_PIN, Bit_RESET)

/* Power Control Macros ------------------------------------------------------*/
/* Ultrasonic radar power control */
#define ST_ULT_PWRCTL_ON        PORT_WriteBit(POWER_RADAR_PORT, POWER_RADAR_PIN, Bit_SET)
#define ST_ULT_PWRCTL_OFF       PORT_WriteBit(POWER_RADAR_PORT, POWER_RADAR_PIN, Bit_RESET)

/* 4G module power control */
#define ST_4G_PWRCTL_ON         PORT_WriteBit(POWER_4G_PORT, POWER_4G_PIN, Bit_SET)
#define ST_4G_PWRCTL_OFF        PORT_WriteBit(POWER_4G_PORT, POWER_4G_PIN, Bit_RESET)

/* Main module power control (high level on, low level off) */
#define ST_MAINPOW_CTRL_ON      PORT_WriteBit(POWER_MAIN_PORT, POWER_MAIN_PIN, Bit_SET)
#define ST_MAINPOW_CTRL_OFF     PORT_WriteBit(POWER_MAIN_PORT, POWER_MAIN_PIN, Bit_RESET)

/* 3.7V to 5V boost chip control */
#define ST_IP5V_KEYEN_ON        PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_SET)
#define ST_IP5V_KEYEN_OFF       PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET)

/* Function Prototypes -------------------------------------------------------*/
void System_Init(void);
void Hardware_Init(void);
void Peripheral_Init(void);

/* Delay Functions */
void DelayMs(uint16_t ms);
void Delay_ms(uint32_t nTime);
uint32_t GetSysTick(void);

/* Power Management Functions */
void PMU_EnterDeepSleep5Mode(void);

/* Booster Control Functions */
void Booster_Init(void);
void Booster_DeInit(void);
void Booster_Start(void);
void Booster_Stop(void);

/* Timer Functions */
void Timer1_PwmOut_Init(void);
void Timer2_PwmOut_Init(void);

/* Watchdog Functions */
void WDT_Configure(void);
void WDT_DeConfigure(void);

/* System Tick Functions */
void SysTickConfigure(void);

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_OPTIMIZED_H */
