Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    startup_xcm32l.o(RESET) refers to startup_xcm32l.o(STACK) for __initial_sp
    startup_xcm32l.o(RESET) refers to startup_xcm32l.o(.text) for Reset_Handler
    startup_xcm32l.o(RESET) refers to main.o(i.SysTick_Handler) for SysTick_Handler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART1_IRQHandler) for UART1_IRQHandler
    startup_xcm32l.o(RESET) refers to rtc.o(i.RTC_IRQHandler) for RTC_IRQHandler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART2_IRQHandler) for UART2_IRQHandler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_xcm32l.o(RESET) refers to main.o(i.WDT_IRQHandler) for WDT_IRQHandler
    startup_xcm32l.o(.text) refers to system_xcm32l.o(i.SystemInit) for SystemInit
    startup_xcm32l.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_xcm32l.o(i.SystemCoreClockUpdate) refers to system_xcm32l.o(.data) for SystemCoreClock
    system_xcm32l.o(i.SystemInit) refers to system_xcm32l.o(i.SetSysClock) for SetSysClock
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    cjson.o(i.cJSON_AddItemToArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_CreateArray) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateBool) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateFalse) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateFloatArray) refers to f2d.o(.text) for __aeabi_f2d
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateIntArray) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateNull) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNumber) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNumber) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateObject) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateTrue) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_Delete) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_DetachItemFromArray) for cJSON_DetachItemFromArray
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_DetachItemFromObject) for cJSON_DetachItemFromObject
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_DetachItemFromArray) for cJSON_DetachItemFromArray
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_GetErrorPtr) refers to cjson.o(.data) for ep
    cjson.o(i.cJSON_GetObjectItem) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_InitHooks) refers to malloc.o(i.malloc) for malloc
    cjson.o(i.cJSON_InitHooks) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_InitHooks) refers to malloc.o(i.free) for free
    cjson.o(i.cJSON_InsertItemInArray) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_New_Item) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_New_Item) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_Parse) refers to cjson.o(i.cJSON_ParseWithOpts) for cJSON_ParseWithOpts
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.skip) for skip
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(.data) for ep
    cjson.o(i.cJSON_Print) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_ReplaceItemInArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_ReplaceItemInArray) for cJSON_ReplaceItemInArray
    cjson.o(i.cJSON_strcasecmp) refers to tolower.o(.text) for tolower
    cjson.o(i.cJSON_strdup) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_strdup) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_strdup) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.create_reference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.create_reference) refers to memcpya.o(.text) for __aeabi_memcpy4
    cjson.o(i.ensure) refers to cjson.o(i.pow2gt) for pow2gt
    cjson.o(i.ensure) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.ensure) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.parse_array) refers to cjson.o(i.skip) for skip
    cjson.o(i.parse_array) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_array) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_array) refers to cjson.o(.data) for ep
    cjson.o(i.parse_number) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.parse_number) refers to dmul.o(.text) for __aeabi_dmul
    cjson.o(i.parse_number) refers to dadd.o(.text) for __aeabi_dadd
    cjson.o(i.parse_number) refers to pow.o(i.pow) for pow
    cjson.o(i.parse_number) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.parse_object) refers to cjson.o(i.skip) for skip
    cjson.o(i.parse_object) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_object) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_object) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_object) refers to cjson.o(.data) for ep
    cjson.o(i.parse_string) refers to cjson.o(i.parse_hex4) for parse_hex4
    cjson.o(i.parse_string) refers to cjson.o(.data) for ep
    cjson.o(i.parse_string) refers to cjson.o(.constdata) for firstByteMark
    cjson.o(i.parse_value) refers to strncmp.o(.text) for strncmp
    cjson.o(i.parse_value) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_value) refers to cjson.o(i.parse_number) for parse_number
    cjson.o(i.parse_value) refers to cjson.o(i.parse_array) for parse_array
    cjson.o(i.parse_value) refers to cjson.o(i.parse_object) for parse_object
    cjson.o(i.parse_value) refers to cjson.o(.data) for ep
    cjson.o(i.print_array) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_array) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_array) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_array) refers to cjson.o(i.update) for update
    cjson.o(i.print_array) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print_array) refers to strlen.o(.text) for strlen
    cjson.o(i.print_array) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_array) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_number) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    cjson.o(i.print_number) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_number) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.print_number) refers to dadd.o(.text) for __aeabi_dsub
    cjson.o(i.print_number) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.print_number) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_number) refers to floor.o(i.floor) for floor
    cjson.o(i.print_number) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_object) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_object) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_object) refers to cjson.o(i.update) for update
    cjson.o(i.print_object) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_object) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print_object) refers to strlen.o(.text) for strlen
    cjson.o(i.print_object) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_object) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_object) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_string) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_string_ptr) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_string_ptr) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_string_ptr) refers to strchr.o(.text) for strchr
    cjson.o(i.print_string_ptr) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_string_ptr) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_value) refers to cjson.o(i.__ARM_common_switch8) for __ARM_common_switch8
    cjson.o(i.print_value) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_value) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_value) refers to cjson.o(i.print_number) for print_number
    cjson.o(i.print_value) refers to cjson.o(i.print_string) for print_string
    cjson.o(i.print_value) refers to cjson.o(i.print_array) for print_array
    cjson.o(i.print_value) refers to cjson.o(i.print_object) for print_object
    cjson.o(i.print_value) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.update) refers to strlen.o(.text) for strlen
    cjson.o(.data) refers to malloc.o(i.malloc) for malloc
    cjson.o(.data) refers to malloc.o(i.free) for free
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Parse) for cJSON_Parse
    protol.o(i.cJsonParseProtocol) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_GetObjectItem) for cJSON_GetObjectItem
    protol.o(i.cJsonParseProtocol) refers to strcmp.o(.text) for strcmp
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.cJsonParseProtocol) refers to i2cuart.o(i.get_voltage) for get_voltage
    protol.o(i.cJsonParseProtocol) refers to dflti.o(.text) for __aeabi_i2d
    protol.o(i.cJsonParseProtocol) refers to dmul.o(.text) for __aeabi_dmul
    protol.o(i.cJsonParseProtocol) refers to ddiv.o(.text) for __aeabi_ddiv
    protol.o(i.cJsonParseProtocol) refers to dadd.o(.text) for __aeabi_dadd
    protol.o(i.cJsonParseProtocol) refers to cdcmple.o(.text) for __aeabi_cdcmple
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.cJsonParseProtocol) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.cJsonParseProtocol) refers to strlen.o(.text) for strlen
    protol.o(i.cJsonParseProtocol) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.cJsonParseProtocol) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.cJsonParseProtocol) refers to malloc.o(i.free) for free
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.cJsonParseProtocol) refers to user_main.o(.data) for g_iVoltage
    protol.o(i.cJsonParseProtocol) refers to uart.o(.bss) for uart_proc_buf
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetSecond) for RTC_GetSecond
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetMinute) for RTC_GetMinute
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetHour) for RTC_GetHour
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetDay) for RTC_GetDay
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetMonth) for RTC_GetMonth
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_rtc.o(i.RTC_GetYear) for RTC_GetYear
    protol.o(i.cJsonParseProtocol) refers to printfa.o(i.__0snprintf) for __2snprintf
    protol.o(i.cJsonParseProtocol) refers to uart.o(.data) for g_UpdateTime
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.RTC_GetWeek) for RTC_GetWeek
    protol.o(i.cJsonParseProtocol) refers to protol.o(.data) for g_trigtype
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    protol.o(i.cJsonParseProtocol) refers to main.o(i.Delay_250ms) for Delay_250ms
    protol.o(i.cJsonParseProtocol) refers to user_main.o(i.enter_stop_mode) for enter_stop_mode
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    protol.o(i.create_objects) refers to memcpya.o(.text) for __aeabi_memcpy4
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateFalse) for cJSON_CreateFalse
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.create_objects) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.create_objects) refers to malloc.o(i.free) for free
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateStringArray) for cJSON_CreateStringArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateIntArray) for cJSON_CreateIntArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    protol.o(i.create_objects) refers to protol.o(.constdata) for .constdata
    protol.o(i.dofile) refers to malloc.o(i.malloc) for malloc
    protol.o(i.dofile) refers to fread.o(i.fread) for fread
    protol.o(i.dofile) refers to protol.o(i.doit) for doit
    protol.o(i.dofile) refers to malloc.o(i.free) for free
    protol.o(i.doit) refers to cjson.o(i.cJSON_Parse) for cJSON_Parse
    protol.o(i.doit) refers to cjson.o(i.cJSON_GetErrorPtr) for cJSON_GetErrorPtr
    protol.o(i.doit) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.doit) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.doit) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.doit) refers to malloc.o(i.free) for free
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.json_create_voltage) refers to f2d.o(.text) for __aeabi_f2d
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.json_create_voltage_new) refers to f2d.o(.text) for __aeabi_f2d
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.json_create_voltage_new) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.json_create_voltage_new) refers to strlen.o(.text) for strlen
    protol.o(i.json_create_voltage_new) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.json_create_voltage_new) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.json_create_voltage_new) refers to malloc.o(i.free) for free
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.json_create_voltage_new) refers to uart.o(.bss) for uart_proc_buf
    protol.o(i.main_cjson) refers to memcpya.o(.text) for __aeabi_memcpy4
    protol.o(i.main_cjson) refers to protol.o(i.doit) for doit
    protol.o(i.main_cjson) refers to protol.o(i.create_objects) for create_objects
    protol.o(i.main_cjson) refers to protol.o(.conststring) for .conststring
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.send_init_finish_info) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.send_init_finish_info) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.send_init_finish_info) refers to strlen.o(.text) for strlen
    protol.o(i.send_init_finish_info) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.send_init_finish_info) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.send_init_finish_info) refers to malloc.o(i.free) for free
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.send_init_finish_info) refers to uart.o(.bss) for uart_proc_buf
    protol.o(.constdata) refers to protol.o(.conststring) for .conststring
    crc32.o(i.crc32) refers to crc32.o(.constdata) for crc_table
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_flash.o(i.FLASH_ReadWaitCycleCmd) for FLASH_ReadWaitCycleCmd
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_HSECmd) for CMU_HSECmd
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_SysClkConfig) for CMU_SysClkConfig
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_HSIConfig) for CMU_HSIConfig
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVS) for FLASH_SetTNVS
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTPGS) for FLASH_SetTPGS
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTPROG) for FLASH_SetTPROG
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVH) for FLASH_SetTNVH
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTRCV) for FLASH_SetTRCV
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTERASE) for FLASH_SetTERASE
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTME) for FLASH_SetTME
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVH1) for FLASH_SetTNVH1
    flash.o(i.Flash_test_main) refers to printfa.o(i.__0printf) for __2printf
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_UnLockPage) for FLASH_UnLockPage
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_ReadWord) for FLASH_ReadWord
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_WriteWord) for FLASH_WriteWord
    flash.o(i.Flash_test_main) refers to flash.o(.conststring) for .conststring
    flash.o(i.Flash_test_main) refers to flash.o(.bss) for rBuffer
    gpio.o(i.GPIO_test_main) refers to gpio.o(i.uf_GPIO_Init) for uf_GPIO_Init
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    gpio.o(i.GPIO_test_main) refers to main.o(i.DelayMs) for DelayMs
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_ToggleBit) for PORT_ToggleBit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_DeInit) for PORT_DeInit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_Start) for I2C_Start
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_send_byte) for I2C_send_byte
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.Test_ACK) for Test_ACK
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.Master_ACK) for Master_ACK
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_read_byte) for I2C_read_byte
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_Start) for I2C_Start
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_send_byte) for I2C_send_byte
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.Test_ACK) for Test_ACK
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.I2C_Start) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_Start) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_Start) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_Stop) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_Stop) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_Stop) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_init) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_read_byte) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_read_byte) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_read_byte) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_read_byte) refers to xcm32lxx_port.o(i.PORT_ReadInputDataBit) for PORT_ReadInputDataBit
    i2cuart.o(i.I2C_send_byte) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_send_byte) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_send_byte) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Master_ACK) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.Master_ACK) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.Master_ACK) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.Test_ACK) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.Test_ACK) refers to xcm32lxx_port.o(i.PORT_ReadInputDataBit) for PORT_ReadInputDataBit
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    i2cuart.o(i.get_voltage) refers to dfltui.o(.text) for __aeabi_ui2d
    i2cuart.o(i.get_voltage) refers to dmul.o(.text) for __aeabi_dmul
    i2cuart.o(i.get_voltage) refers to ddiv.o(.text) for __aeabi_ddiv
    i2cuart.o(i.get_voltage) refers to dadd.o(.text) for __aeabi_dadd
    i2cuart.o(i.get_voltage) refers to d2f.o(.text) for __aeabi_d2f
    i2cuart.o(i.get_voltage) refers to f2d.o(.text) for __aeabi_f2d
    i2cuart.o(i.get_voltage) refers to printfa.o(i.__0printf) for __2printf
    i2cuart.o(i.get_voltage) refers to i2cuart.o(.data) for P1
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    i2cuart.o(i.i2c_get_Voltage_test) refers to dfltui.o(.text) for __aeabi_ui2d
    i2cuart.o(i.i2c_get_Voltage_test) refers to dmul.o(.text) for __aeabi_dmul
    i2cuart.o(i.i2c_get_Voltage_test) refers to ddiv.o(.text) for __aeabi_ddiv
    i2cuart.o(i.i2c_get_Voltage_test) refers to dadd.o(.text) for __aeabi_dadd
    i2cuart.o(i.i2c_get_Voltage_test) refers to d2f.o(.text) for __aeabi_d2f
    i2cuart.o(i.i2c_get_Voltage_test) refers to f2d.o(.text) for __aeabi_f2d
    i2cuart.o(i.i2c_get_Voltage_test) refers to printfa.o(i.__0printf) for __2printf
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(.data) for P1
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.test111) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.Booster_Init) refers to main.o(i.DelayMs) for DelayMs
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    main.o(i.Delay_250ms) refers to xcm32lxx_systick.o(i.SysTick_Cmd) for SysTick_Cmd
    main.o(i.Delay_250ms) refers to main.o(.data) for TimingDelay
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_SetReloadValue) for SysTick_SetReloadValue
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_ITConfig) for SysTick_ITConfig
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_Cmd) for SysTick_Cmd
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_SetPriority) for NVIC_SetPriority
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    main.o(i.SysTick_Handler) refers to main.o(.data) for TimingDelay
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_LSIConfig) for CMU_LSIConfig
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_WDTCLKConfig) for CMU_WDTCLKConfig
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_DeInit) for WDT_DeInit
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_StructInit) for WDT_StructInit
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_Init) for WDT_Init
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_SetPriority) for NVIC_SetPriority
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_Cmd) for WDT_Cmd
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_RestartCmd) for WDT_RestartCmd
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_GetITStatus) for WDT_GetITStatus
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_ClearITFlag) for WDT_ClearITFlag
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_RestartCmd) for WDT_RestartCmd
    main.o(i.fputc) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    main.o(i.fputc) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_HSIConfig) for CMU_HSIConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_SysClkConfig) for CMU_SysClkConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_HCLKConfig) for CMU_HCLKConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_PCLKConfig) for CMU_PCLKConfig
    main.o(i.main) refers to gpio.o(i.uf_GPIO_Init) for uf_GPIO_Init
    main.o(i.main) refers to main.o(i.Booster_Init) for Booster_Init
    main.o(i.main) refers to uart.o(i.UART4_Init) for UART4_Init
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to uart.o(i.UART2_Init) for UART2_Init
    main.o(i.main) refers to uart.o(i.UART1_Init) for UART1_Init
    main.o(i.main) refers to rtc.o(i.RTC_Run_Init) for RTC_Run_Init
    main.o(i.main) refers to main.o(i.SysTickConfigure) for SysTickConfigure
    main.o(i.main) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.main) refers to main.o(i.Delay_250ms) for Delay_250ms
    main.o(i.main) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    main.o(i.main) refers to user_main.o(i.user_main) for user_main
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetSecond) for RTC_GetSecond
    rtc.o(i.GetRtcSecond) refers to rtc.o(i.t_BCD2HEX) for t_BCD2HEX
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetMinute) for RTC_GetMinute
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetHour) for RTC_GetHour
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetDay) for RTC_GetDay
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetMonth) for RTC_GetMonth
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetYear) for RTC_GetYear
    rtc.o(i.GetRtcSecond) refers to rtc.o(i.RTC_To_Sec) for RTC_To_Sec
    rtc.o(i.Is_Leap_Year) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.NVIC_Init_rtc) refers to rtc.o(i.NVIC_SetPriority) for NVIC_SetPriority
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetYearLSB) for RTC_GetYearLSB
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetMonth) for RTC_GetMonth
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetDay) for RTC_GetDay
    rtc.o(i.RTC_GetWeek) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.RTC_IRQHandler) refers to xcm32lxx_rtc.o(i.RTC_GetITStatus) for RTC_GetITStatus
    rtc.o(i.RTC_IRQHandler) refers to xcm32lxx_rtc.o(i.RTC_ClearITFlag) for RTC_ClearITFlag
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_GPIO_CMU_Init) for uf_GPIO_CMU_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_GPIO_RTC_Init) for uf_GPIO_RTC_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_RTC_Init) for uf_RTC_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.NVIC_Init_rtc) for NVIC_Init_rtc
    rtc.o(i.RTC_Run_Init) refers to xcm32lxx_rtc.o(i.RTC_Cmd) for RTC_Cmd
    rtc.o(i.RTC_Run_Init) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.RTC_Run_Init) refers to main.o(i.DelayMs) for DelayMs
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_DeInit) for RTC_DeInit
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_StructInit) for RTC_StructInit
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_Init) for RTC_Init
    rtc.o(i.RTC_SetTime) refers to rtc.o(i.NVIC_Init_rtc) for NVIC_Init_rtc
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_ITConfig) for RTC_ITConfig
    rtc.o(i.RTC_To_Sec) refers to rtc.o(i.Is_Leap_Year) for Is_Leap_Year
    rtc.o(i.RTC_To_Sec) refers to rtc.o(.constdata) for mon_table
    rtc.o(i.t_HEX2BCD) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.uf_GPIO_CMU_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_CMU_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_LSEConfig) for CMU_LSEConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_RTCCLKConfig) for CMU_RTCCLKConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_DeInit) for RTC_DeInit
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_StructInit) for RTC_StructInit
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_Init) for RTC_Init
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_ITConfig) for RTC_ITConfig
    rtc.o(i.uf_RTC_Init) refers to rtc.o(.data) for RTCTime
    uart.o(i.UART1_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART1_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART1_IRQHandler) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.UART1_IRQHandler) refers to uart.o(.bss) for RxWritePos
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_StructInit) for UART_StructInit
    uart.o(i.UART1_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART1_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART1_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART1_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_FIFOModeConfig) for UART_FIFOModeConfig
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_PTXREModeConfig) for UART_PTXREModeConfig
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART2_IRQHandler) refers to uart.o(i.uart2_tx_buffer_out) for uart2_tx_buffer_out
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART2_IRQHandler) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.UART2_IRQHandler) refers to printfa.o(i.__0printf) for __2printf
    uart.o(i.UART2_IRQHandler) refers to uart.o(.data) for _fgUart2TxBusy
    uart.o(i.UART2_IRQHandler) refers to uart.o(.bss) for RxWritePos
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_StructInit) for UART_StructInit
    uart.o(i.UART2_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART2_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART2_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART2_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_FIFOModeConfig) for UART_FIFOModeConfig
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_PTXREModeConfig) for UART_PTXREModeConfig
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    uart.o(i.UART2_Init) refers to uart.o(.bss) for _rUart2Tx
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.UART4_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART4_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART4_Init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    uart.o(i.UART4_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART4_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_StructInit) for UART_StructInit
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART4_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART4_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART4_Init) refers to uart.o(.bss) for _rUart4Tx
    uart.o(i.log_printf) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.log_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart.o(i.log_printf) refers to strlen.o(.text) for strlen
    uart.o(i.log_printf) refers to uart.o(i.uart4_com_tx_data) for uart4_com_tx_data
    uart.o(i.log_printf) refers to uart.o(.bss) for s_buf_print
    uart.o(i.pack_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_data) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.pack_data) refers to uart.o(.data) for s_seq
    uart.o(i.pack_luna_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_luna_data) refers to uart.o(.data) for s_seq
    uart.o(i.pack_print_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_print_data) refers to uart.o(.data) for s_seq
    uart.o(i.uart1_rx_mengmu_process) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.uart1_rx_mengmu_process) refers to uart.o(.bss) for RxReadPos
    uart.o(i.uart2_com_tx) refers to uart.o(i.uart2_tx_buffer_out) for uart2_tx_buffer_out
    uart.o(i.uart2_com_tx) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.uart2_com_tx) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.uart2_com_tx) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.uart2_com_tx) refers to uart.o(.data) for _fgUart2TxBusy
    uart.o(i.uart2_com_tx_data) refers to uart.o(i.uart2_get_tx_buffer_size) for uart2_get_tx_buffer_size
    uart.o(i.uart2_com_tx_data) refers to uart.o(i.uart2_tx_buffer_in) for uart2_tx_buffer_in
    uart.o(i.uart2_find_head_and_tail) refers to ldiv.o(.text) for __aeabi_ldivmod
    uart.o(i.uart2_get_tx_buffer_size) refers to uart.o(.bss) for _rUart2Tx
    uart.o(i.uart2_rx_process) refers to uart.o(i.uart2_find_head_and_tail) for uart2_find_head_and_tail
    uart.o(i.uart2_rx_process) refers to ldiv.o(.text) for __aeabi_ldivmod
    uart.o(i.uart2_rx_process) refers to uart.o(i.uart2_unpack_data) for uart2_unpack_data
    uart.o(i.uart2_rx_process) refers to uart.o(.bss) for RxReadPos
    uart.o(i.uart2_tx_buffer_in) refers to uart.o(.bss) for _rUart2Tx
    uart.o(i.uart2_tx_buffer_out) refers to uart.o(.bss) for _rUart2Tx
    uart.o(i.uart2_unpack_data) refers to printfa.o(i.__0printf) for __2printf
    uart.o(i.uart2_unpack_data) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.uart4_com_tx) refers to uart.o(i.uart4_tx_buffer_out) for uart4_tx_buffer_out
    uart.o(i.uart4_com_tx) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.uart4_com_tx) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.uart4_com_tx) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.uart4_com_tx) refers to uart.o(.data) for _fgUart4TxBusy
    uart.o(i.uart4_com_tx_data) refers to uart.o(i.uart4_get_tx_buffer_size) for uart4_get_tx_buffer_size
    uart.o(i.uart4_com_tx_data) refers to uart.o(i.uart4_tx_buffer_in) for uart4_tx_buffer_in
    uart.o(i.uart4_get_tx_buffer_size) refers to uart.o(.bss) for _rUart4Tx
    uart.o(i.uart4_tx_buffer_in) refers to uart.o(.bss) for _rUart4Tx
    uart.o(i.uart4_tx_buffer_out) refers to uart.o(.bss) for _rUart4Tx
    user_main.o(i.enter_stop_mode) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.enter_stop_mode) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.enter_stop_mode) refers to user_main.o(.data) for g_Status
    user_main.o(i.user_main) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.user_main) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    user_main.o(i.user_main) refers to i2cuart.o(i.get_voltage) for get_voltage
    user_main.o(i.user_main) refers to memseta.o(.text) for __aeabi_memclr4
    user_main.o(i.user_main) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.user_main) refers to main.o(i.Delay_250ms) for Delay_250ms
    user_main.o(i.user_main) refers to uart.o(i.uart2_rx_process) for uart2_rx_process
    user_main.o(i.user_main) refers to protol.o(i.cJsonParseProtocol) for cJsonParseProtocol
    user_main.o(i.user_main) refers to uart.o(i.uart1_rx_mengmu_process) for uart1_rx_mengmu_process
    user_main.o(i.user_main) refers to uart.o(i.pack_luna_data) for pack_luna_data
    user_main.o(i.user_main) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    user_main.o(i.user_main) refers to idiv.o(.text) for __aeabi_idivmod
    user_main.o(i.user_main) refers to user_main.o(.data) for g_LastRadarStatus
    user_main.o(i.user_main) refers to protol.o(.data) for startUpPeriod
    user_main.o(i.user_main) refers to user_main.o(.bss) for g_XijieRadarInfo
    user_main.o(i.user_main) refers to uart.o(.bss) for uart_proc_buf
    user_main.o(i.user_main) refers to user_main.o(i.enter_stop_mode) for enter_stop_mode
    user_main.o(i.user_main) refers to uart.o(i.uart2_com_tx) for uart2_com_tx
    user_main.o(.data) refers to user_main.o(.conststring) for .conststring
    xcm32lxx_port.o(i.PORT_ReadOutputData) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ReadOutputDataBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ResetBits) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_SetBits) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ToggleBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_Write) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_WriteBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_uart.o(i.UART_Init) refers to ffltui.o(.text) for __aeabi_ui2f
    xcm32lxx_uart.o(i.UART_Init) refers to fdiv.o(.text) for __aeabi_fdiv
    xcm32lxx_uart.o(i.UART_Init) refers to f2d.o(.text) for __aeabi_f2d
    xcm32lxx_uart.o(i.UART_Init) refers to dadd.o(.text) for __aeabi_dadd
    xcm32lxx_uart.o(i.UART_Init) refers to dfixui.o(.text) for __aeabi_d2uiz
    xcm32lxx_uart.o(i.UART_Init) refers to ffixui.o(.text) for __aeabi_f2uiz
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.floor) for floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    fread.o(i.fread) refers to fgetc.o(.text) for fgetc
    fread.o(i.fread) refers to uidiv.o(.text) for __aeabi_uidivmod
    idiv.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_xcm32l.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_xcm32l.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fgetc.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_u.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_u.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_u.o(.text) refers to fgetc_u.o(.data) for .data
    fgetc_u.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_u.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_b.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_b.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_b.o(.text) refers to fgetc_b.o(.data) for .data
    fgetc_b.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_b.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_ub.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_ub.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_ub.o(.text) refers to fgetc_ub.o(.data) for .data
    fgetc_ub.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_ub.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing system_xcm32l.o(.rev16_text), (4 bytes).
    Removing system_xcm32l.o(.revsh_text), (4 bytes).
    Removing system_xcm32l.o(i.SystemCoreClockUpdate), (92 bytes).
    Removing system_xcm32l.o(.data), (4 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToArray), (24 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToObject), (28 bytes).
    Removing cjson.o(i.cJSON_AddItemToObjectCS), (68 bytes).
    Removing cjson.o(i.cJSON_CreateArray), (20 bytes).
    Removing cjson.o(i.cJSON_CreateBool), (30 bytes).
    Removing cjson.o(i.cJSON_CreateDoubleArray), (72 bytes).
    Removing cjson.o(i.cJSON_CreateFalse), (20 bytes).
    Removing cjson.o(i.cJSON_CreateFloatArray), (76 bytes).
    Removing cjson.o(i.cJSON_CreateIntArray), (76 bytes).
    Removing cjson.o(i.cJSON_CreateNull), (20 bytes).
    Removing cjson.o(i.cJSON_CreateStringArray), (66 bytes).
    Removing cjson.o(i.cJSON_CreateTrue), (20 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromArray), (22 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObject), (22 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromArray), (74 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObject), (50 bytes).
    Removing cjson.o(i.cJSON_Duplicate), (180 bytes).
    Removing cjson.o(i.cJSON_GetArrayItem), (20 bytes).
    Removing cjson.o(i.cJSON_GetArraySize), (18 bytes).
    Removing cjson.o(i.cJSON_GetErrorPtr), (12 bytes).
    Removing cjson.o(i.cJSON_InitHooks), (72 bytes).
    Removing cjson.o(i.cJSON_InsertItemInArray), (64 bytes).
    Removing cjson.o(i.cJSON_Minify), (190 bytes).
    Removing cjson.o(i.cJSON_PrintBuffered), (44 bytes).
    Removing cjson.o(i.cJSON_PrintUnformatted), (18 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInArray), (78 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObject), (56 bytes).
    Removing cjson.o(i.create_reference), (50 bytes).
    Removing protol.o(.rev16_text), (4 bytes).
    Removing protol.o(.revsh_text), (4 bytes).
    Removing protol.o(i.create_objects), (1116 bytes).
    Removing protol.o(i.dofile), (88 bytes).
    Removing protol.o(i.doit), (88 bytes).
    Removing protol.o(i.json_create_voltage), (184 bytes).
    Removing protol.o(i.json_create_voltage_new), (276 bytes).
    Removing protol.o(i.main_cjson), (40 bytes).
    Removing protol.o(i.send_init_finish_info), (176 bytes).
    Removing protol.o(i.uart_printf2), (8 bytes).
    Removing protol.o(.constdata), (176 bytes).
    Removing protol.o(.conststring), (527 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(i.CMU_HSI2HSE), (50 bytes).
    Removing flash.o(i.Flash_test_main), (672 bytes).
    Removing flash.o(.bss), (2048 bytes).
    Removing flash.o(.conststring), (140 bytes).
    Removing flash.o(.data), (8 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(i.GPIO_test_main), (144 bytes).
    Removing i2cuart.o(.rev16_text), (4 bytes).
    Removing i2cuart.o(.revsh_text), (4 bytes).
    Removing i2cuart.o(i.i2c_get_Voltage_test), (212 bytes).
    Removing i2cuart.o(i.test111), (38 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(i.WDT_Configure), (108 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.RTC_SetTime), (86 bytes).
    Removing rtc.o(i.t_HEX2BCD), (184 bytes).
    Removing rtc.o(i.uf_GPIO_UART_Init), (88 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(i.Sys_Printf), (2 bytes).
    Removing uart.o(i.log_printf), (68 bytes).
    Removing uart.o(i.pack_print_data), (136 bytes).
    Removing uart.o(i.uart4_com_tx), (92 bytes).
    Removing uart.o(i.uart4_com_tx_data), (48 bytes).
    Removing uart.o(i.uart4_get_tx_buffer_size), (20 bytes).
    Removing uart.o(i.uart4_tx_buffer_in), (76 bytes).
    Removing uart.o(i.uart4_tx_buffer_out), (80 bytes).
    Removing user_main.o(.rev16_text), (4 bytes).
    Removing user_main.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_adc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_adc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_adc.o(i.ADC_BGRCmd), (76 bytes).
    Removing xcm32lxx_adc.o(i.ADC_ClearITFlag), (20 bytes).
    Removing xcm32lxx_adc.o(i.ADC_ClearResultAcc), (40 bytes).
    Removing xcm32lxx_adc.o(i.ADC_Cmd), (76 bytes).
    Removing xcm32lxx_adc.o(i.ADC_DeInit), (48 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetConvCompletedFlagStatus), (24 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetResult), (36 bytes).
    Removing xcm32lxx_adc.o(i.ADC_Init), (268 bytes).
    Removing xcm32lxx_adc.o(i.ADC_InterSignalSourceVCCConfig), (112 bytes).
    Removing xcm32lxx_adc.o(i.ADC_PeriphReflectTrigConvCmd), (40 bytes).
    Removing xcm32lxx_adc.o(i.ADC_SetContinuousSampleCounter), (32 bytes).
    Removing xcm32lxx_adc.o(i.ADC_SoftwareStartConvCmd), (36 bytes).
    Removing xcm32lxx_adc.o(i.ADC_StructInit), (24 bytes).
    Removing xcm32lxx_adc.o(i.ADC_TempSensorCmd), (76 bytes).
    Removing xcm32lxx_buzzer.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_buzzer.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_Cmd), (52 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_DeInit), (48 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_Init), (76 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_StructInit), (8 bytes).
    Removing xcm32lxx_cmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_cmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_BASETMCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetHCLKFreq), (88 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetPCLKFreq), (104 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetSysClkFreq), (68 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetSysClkSource), (64 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_HSECmd), (96 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_IRC40KConfig), (68 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LCDCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LPUARTCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LSIConfig), (100 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LockUpCmd), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_RAM_PARCmd), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_WDTCLKConfig), (52 bytes).
    Removing xcm32lxx_crc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_crc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_crc.o(i.CRC_DeInit), (48 bytes).
    Removing xcm32lxx_crc.o(i.CRC_GetCRCFlagStatus), (20 bytes).
    Removing xcm32lxx_crc.o(i.CRC_GetCRCResult), (32 bytes).
    Removing xcm32lxx_crc.o(i.CRC_Init), (96 bytes).
    Removing xcm32lxx_crc.o(i.CRC_InitResult), (28 bytes).
    Removing xcm32lxx_crc.o(i.CRC_SendCRCData), (20 bytes).
    Removing xcm32lxx_crc.o(i.CRC_StructInit), (14 bytes).
    Removing xcm32lxx_des.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_des.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_des.o(i.DES_Cmd), (52 bytes).
    Removing xcm32lxx_des.o(i.DES_DeInit), (48 bytes).
    Removing xcm32lxx_des.o(i.DES_GetCompletedFlagStatus), (20 bytes).
    Removing xcm32lxx_des.o(i.DES_GetData), (28 bytes).
    Removing xcm32lxx_des.o(i.DES_Init), (128 bytes).
    Removing xcm32lxx_des.o(i.DES_SetData), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_SetIV), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_SetKey), (24 bytes).
    Removing xcm32lxx_des.o(i.DES_SetRandomNumber), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_StructInit), (16 bytes).
    Removing xcm32lxx_dma.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_dma.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_dma.o(i.DMA_ClearITFlag), (20 bytes).
    Removing xcm32lxx_dma.o(i.DMA_Cmd), (32 bytes).
    Removing xcm32lxx_dma.o(i.DMA_DeInit), (48 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetCombineITStatus), (32 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetCompletedFlagStatus), (32 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetITStatus), (36 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetRawITStatus), (36 bytes).
    Removing xcm32lxx_dma.o(i.DMA_ITConfig), (44 bytes).
    Removing xcm32lxx_dma.o(i.DMA_Init), (708 bytes).
    Removing xcm32lxx_dma.o(i.DMA_StructInit), (74 bytes).
    Removing xcm32lxx_flash.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_flash.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ClearITFlag), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_EraseMass), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_GetITStatus), (24 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_LockPage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadByte), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadCmd), (60 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadHalfWord), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadWaitCycleCmd), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadWord), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTERASE), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTME), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVH), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVH1), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVS), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTPGS), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTPROG), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTRCV), (12 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_UnLockPage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteByte), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteCmd), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteHalfWord), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteWord), (68 bytes).
    Removing xcm32lxx_i2c.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_i2c.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_AbrtConfig), (24 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ClearITFlag), (64 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_Cmd), (34 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_DMAModeConfig), (74 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_DeInit), (60 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetEnabledFlagStatus), (20 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetFlagStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetITStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetRawITStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_Init), (280 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRead), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithReadStop), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRestartRead), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRestartReadStop), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithStop), (10 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_StructInit), (42 bytes).
    Removing xcm32lxx_lcd.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_lcd.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_ClearITFlag), (20 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_Cmd), (52 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_DeInit), (44 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_GetITStatus), (28 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_ITConfig), (40 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_Init), (208 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_RAMHInit), (16 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_RAMLInit), (16 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_SetRAMData), (148 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_StructInit), (22 bytes).
    Removing xcm32lxx_lvd.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_lvd.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_ClearITFlag), (20 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_Cmd), (76 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_FilterCmd), (36 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_GetITStatus), (28 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_HysteresisCmd), (76 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_ITConfig), (40 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_Init), (212 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_StructInit), (16 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_WarmUpCmd), (76 bytes).
    Removing xcm32lxx_misc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_misc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_misc.o(i.NVIC_DeInit), (88 bytes).
    Removing xcm32lxx_pca.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_pca.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_CCAPOPolarityConfig), (34 bytes).
    Removing xcm32lxx_pca.o(i.PCA_ClearITFlag), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_Cmd), (44 bytes).
    Removing xcm32lxx_pca.o(i.PCA_DeInit), (88 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturer), (76 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturerHSB), (64 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturerLSB), (64 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounter), (16 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounterHSB), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounterLSB), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetITStatus), (18 bytes).
    Removing xcm32lxx_pca.o(i.PCA_ITConfig), (282 bytes).
    Removing xcm32lxx_pca.o(i.PCA_Init), (400 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCapturerHSB), (56 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCapturerLSB), (56 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCounterHSB), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCounterLSB), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_StructInit), (18 bytes).
    Removing xcm32lxx_pmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_pmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterDeepSleep3Mode), (52 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterDeepSleep4Mode), (68 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterPowerDown3Mode), (48 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterPowerDown4Mode), (60 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterSleepMode), (52 bytes).
    Removing xcm32lxx_port.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_port.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_ClearITFlag), (8 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_GetITStatus), (18 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_ITConfig), (40 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_Init), (78 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_StructInit), (10 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadInputData), (8 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadOutputData), (28 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadOutputDataBit), (40 bytes).
    Removing xcm32lxx_port.o(i.PORT_ResetBits), (44 bytes).
    Removing xcm32lxx_port.o(i.PORT_SetBits), (44 bytes).
    Removing xcm32lxx_port.o(i.PORT_ToggleBit), (44 bytes).
    Removing xcm32lxx_port.o(i.PORT_Write), (32 bytes).
    Removing xcm32lxx_ram.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_ram.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadByte), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadHalfWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteByte), (56 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteHalfWord), (60 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAM_DeInit), (44 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ClearITFlag), (20 bytes).
    Removing xcm32lxx_ram.o(i.RAM_GetITStatus), (20 bytes).
    Removing xcm32lxx_ram.o(i.RAM_GetODCErrAddress), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadByte), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadHalfWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteByte), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteHalfWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteWord), (24 bytes).
    Removing xcm32lxx_rmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_ClearResetFlag), (16 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_GetResetFlag), (24 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_Periph0ResetCmd), (32 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_Periph1ResetCmd), (32 bytes).
    Removing xcm32lxx_rng.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rng.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rng.o(i.RNG_CircuitCmd), (52 bytes).
    Removing xcm32lxx_rng.o(i.RNG_Cmd), (52 bytes).
    Removing xcm32lxx_rng.o(i.RNG_DeInit), (44 bytes).
    Removing xcm32lxx_rng.o(i.RNG_GetCompletedFlagStatus), (24 bytes).
    Removing xcm32lxx_rng.o(i.RNG_GetRandomNumber), (28 bytes).
    Removing xcm32lxx_rng.o(i.RNG_Init), (100 bytes).
    Removing xcm32lxx_rng.o(i.RNG_StructInit), (12 bytes).
    Removing xcm32lxx_rtc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rtc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_AdjustConfig), (28 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_GetSixteenOfSecond), (16 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_GetYearHSB), (12 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_ResetCmd), (36 bytes).
    Removing xcm32lxx_spi.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_spi.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ClearITFlag), (52 bytes).
    Removing xcm32lxx_spi.o(i.SPI_Cmd), (34 bytes).
    Removing xcm32lxx_spi.o(i.SPI_DMAModeConfig), (54 bytes).
    Removing xcm32lxx_spi.o(i.SPI_DeInit), (140 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetFlagStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetITStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetRawITStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetRxFIFOAvaDataCounter), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetTxFIFOAvaDataCounter), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ITConfig), (20 bytes).
    Removing xcm32lxx_spi.o(i.SPI_Init), (300 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ReceiveData), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_SendData), (4 bytes).
    Removing xcm32lxx_spi.o(i.SPI_StructInit), (36 bytes).
    Removing xcm32lxx_systick.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_systick.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_ClearITFlag), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetCurrentValue), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetITStatus), (20 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetReloadValue), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_SetCurrentValue), (16 bytes).
    Removing xcm32lxx_timer.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_timer.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ClearAllITFlag), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ClearITFlag), (6 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_Cmd), (34 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_DeInit), (128 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetAllITStatus), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetAllRawITStatus), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetCurrentCounter), (6 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetITStatus), (14 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ITConfig), (24 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_Init), (192 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_SetLoadCounter0), (4 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_SetLoadCounter1), (80 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_StructInit), (12 bytes).
    Removing xcm32lxx_uart.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_uart.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_uart.o(i.UART_GetMDMStatus), (18 bytes).
    Removing xcm32lxx_vc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_vc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_vc.o(i.VC_BGRCmd), (76 bytes).
    Removing xcm32lxx_vc.o(i.VC_ClearITFlag), (20 bytes).
    Removing xcm32lxx_vc.o(i.VC_Cmd), (76 bytes).
    Removing xcm32lxx_vc.o(i.VC_FilterCmd), (36 bytes).
    Removing xcm32lxx_vc.o(i.VC_GetITStatus), (28 bytes).
    Removing xcm32lxx_vc.o(i.VC_ITConfig), (40 bytes).
    Removing xcm32lxx_vc.o(i.VC_Init), (204 bytes).
    Removing xcm32lxx_vc.o(i.VC_StructInit), (14 bytes).
    Removing xcm32lxx_vc.o(i.VC_VCCScalerCmd), (76 bytes).
    Removing xcm32lxx_wdt.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_wdt.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_Cmd), (52 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_DeInit), (44 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_GetCurrentWDTimer), (12 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_Init), (80 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_StructInit), (14 bytes).

360 unused section(s) (total 20519 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/tolower.c         0x00000000   Number         0  tolower.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fclose.c          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_ub.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_b.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_u.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc.o ABSOLUTE
    ../clib/microlib/stdio/fopen.c           0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/microlib/stdio/fread.c           0x00000000   Number         0  fread.o ABSOLUTE
    ../clib/microlib/stdio/fseek.c           0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/microlib/stdio/ftell.c           0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemig.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\User\cJSON\cJSON.c                    0x00000000   Number         0  cjson.o ABSOLUTE
    ..\User\cJSON\protol.c                   0x00000000   Number         0  protol.o ABSOLUTE
    ..\User\crc32.c                          0x00000000   Number         0  crc32.o ABSOLUTE
    ..\User\flash.c                          0x00000000   Number         0  flash.o ABSOLUTE
    ..\User\gpio.c                           0x00000000   Number         0  gpio.o ABSOLUTE
    ..\User\i2cUart.c                        0x00000000   Number         0  i2cuart.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\rtc.c                            0x00000000   Number         0  rtc.o ABSOLUTE
    ..\User\uart.c                           0x00000000   Number         0  uart.o ABSOLUTE
    ..\User\user_main.c                      0x00000000   Number         0  user_main.o ABSOLUTE
    ..\\User\\cJSON\\protol.c                0x00000000   Number         0  protol.o ABSOLUTE
    ..\\User\\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    ..\\User\\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\User\\i2cUart.c                      0x00000000   Number         0  i2cuart.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\User\\uart.c                         0x00000000   Number         0  uart.o ABSOLUTE
    ..\\User\\user_main.c                    0x00000000   Number         0  user_main.o ABSOLUTE
    ..\\xcm32lxx_lib\\core\\system_XCM32L.c  0x00000000   Number         0  system_xcm32l.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_adc.c    0x00000000   Number         0  xcm32lxx_adc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_buzzer.c 0x00000000   Number         0  xcm32lxx_buzzer.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_cmu.c    0x00000000   Number         0  xcm32lxx_cmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_crc.c    0x00000000   Number         0  xcm32lxx_crc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_des.c    0x00000000   Number         0  xcm32lxx_des.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_dma.c    0x00000000   Number         0  xcm32lxx_dma.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_flash.c  0x00000000   Number         0  xcm32lxx_flash.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_i2c.c    0x00000000   Number         0  xcm32lxx_i2c.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_lcd.c    0x00000000   Number         0  xcm32lxx_lcd.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_lvd.c    0x00000000   Number         0  xcm32lxx_lvd.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_misc.c   0x00000000   Number         0  xcm32lxx_misc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_pca.c    0x00000000   Number         0  xcm32lxx_pca.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_pmu.c    0x00000000   Number         0  xcm32lxx_pmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_port.c   0x00000000   Number         0  xcm32lxx_port.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_ram.c    0x00000000   Number         0  xcm32lxx_ram.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rmu.c    0x00000000   Number         0  xcm32lxx_rmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rng.c    0x00000000   Number         0  xcm32lxx_rng.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rtc.c    0x00000000   Number         0  xcm32lxx_rtc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_spi.c    0x00000000   Number         0  xcm32lxx_spi.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_systick.c 0x00000000   Number         0  xcm32lxx_systick.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_timer.c  0x00000000   Number         0  xcm32lxx_timer.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_uart.c   0x00000000   Number         0  xcm32lxx_uart.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_vc.c     0x00000000   Number         0  xcm32lxx_vc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_wdt.c    0x00000000   Number         0  xcm32lxx_wdt.o ABSOLUTE
    ..\xcm32lxx_lib\core\startup_XCM32L.s    0x00000000   Number         0  startup_xcm32l.o ABSOLUTE
    ..\xcm32lxx_lib\core\system_XCM32L.c     0x00000000   Number         0  system_xcm32l.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_adc.c       0x00000000   Number         0  xcm32lxx_adc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_buzzer.c    0x00000000   Number         0  xcm32lxx_buzzer.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_cmu.c       0x00000000   Number         0  xcm32lxx_cmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_crc.c       0x00000000   Number         0  xcm32lxx_crc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_des.c       0x00000000   Number         0  xcm32lxx_des.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_dma.c       0x00000000   Number         0  xcm32lxx_dma.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_flash.c     0x00000000   Number         0  xcm32lxx_flash.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_i2c.c       0x00000000   Number         0  xcm32lxx_i2c.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_lcd.c       0x00000000   Number         0  xcm32lxx_lcd.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_lvd.c       0x00000000   Number         0  xcm32lxx_lvd.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_misc.c      0x00000000   Number         0  xcm32lxx_misc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_pca.c       0x00000000   Number         0  xcm32lxx_pca.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_pmu.c       0x00000000   Number         0  xcm32lxx_pmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_port.c      0x00000000   Number         0  xcm32lxx_port.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_ram.c       0x00000000   Number         0  xcm32lxx_ram.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rmu.c       0x00000000   Number         0  xcm32lxx_rmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rng.c       0x00000000   Number         0  xcm32lxx_rng.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rtc.c       0x00000000   Number         0  xcm32lxx_rtc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_spi.c       0x00000000   Number         0  xcm32lxx_spi.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_systick.c   0x00000000   Number         0  xcm32lxx_systick.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_timer.c     0x00000000   Number         0  xcm32lxx_timer.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_uart.c      0x00000000   Number         0  xcm32lxx_uart.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_vc.c        0x00000000   Number         0  xcm32lxx_vc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_wdt.c       0x00000000   Number         0  xcm32lxx_wdt.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00005000   Section      192  startup_xcm32l.o(RESET)
    .ARM.Collect$$$$00000000                 0x000050c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000050c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000050c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000050c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000050c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000050c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x000050d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000050d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000050d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x000050d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000050d4   Section       28  startup_xcm32l.o(.text)
    .text                                    0x000050f0   Section        0  idiv.o(.text)
    .text                                    0x00005118   Section        0  uldiv.o(.text)
    .text                                    0x00005178   Section        0  ldiv.o(.text)
    .text                                    0x000051c4   Section        0  tolower.o(.text)
    .text                                    0x000051d0   Section        0  memcpya.o(.text)
    .text                                    0x000051f4   Section        0  memseta.o(.text)
    .text                                    0x00005218   Section        0  strchr.o(.text)
    .text                                    0x0000522c   Section        0  strlen.o(.text)
    .text                                    0x0000523a   Section        0  strcmp.o(.text)
    .text                                    0x00005256   Section        0  strcpy.o(.text)
    .text                                    0x00005268   Section        0  strncmp.o(.text)
    .text                                    0x00005286   Section        0  fdiv.o(.text)
    .text                                    0x00005304   Section        0  dadd.o(.text)
    .text                                    0x00005468   Section        0  dmul.o(.text)
    .text                                    0x00005538   Section        0  ddiv.o(.text)
    .text                                    0x00005628   Section        0  ffltui.o(.text)
    .text                                    0x00005638   Section        0  dflti.o(.text)
    .text                                    0x00005660   Section        0  dfltui.o(.text)
    .text                                    0x0000567c   Section        0  ffixui.o(.text)
    .text                                    0x000056a4   Section        0  dfixi.o(.text)
    .text                                    0x000056ec   Section        0  dfixui.o(.text)
    .text                                    0x00005728   Section        0  f2d.o(.text)
    .text                                    0x00005750   Section       40  cdcmple.o(.text)
    .text                                    0x00005778   Section       40  cdrcmple.o(.text)
    .text                                    0x000057a0   Section        0  d2f.o(.text)
    .text                                    0x000057d8   Section        0  uidiv.o(.text)
    .text                                    0x00005804   Section        0  llshl.o(.text)
    .text                                    0x00005824   Section        0  llushr.o(.text)
    .text                                    0x00005846   Section        0  llsshr.o(.text)
    .text                                    0x0000586c   Section        0  fepilogue.o(.text)
    .text                                    0x0000586c   Section        0  iusefp.o(.text)
    .text                                    0x000058ee   Section        0  depilogue.o(.text)
    .text                                    0x000059ac   Section        0  dscalb.o(.text)
    .text                                    0x000059d8   Section        0  dfixul.o(.text)
    .text                                    0x00005a18   Section       36  init.o(.text)
    .text                                    0x00005a3c   Section        0  dsqrt.o(.text)
    .text                                    0x00005ade   Section        0  __dczerorl2.o(.text)
    i.Booster_Init                           0x00005b34   Section        0  main.o(i.Booster_Init)
    i.CMU_APBPeriph0ClockCmd                 0x00005b78   Section        0  xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd)
    i.CMU_APBPeriph1ClockCmd                 0x00005b98   Section        0  xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd)
    i.CMU_HCLKConfig                         0x00005bb8   Section        0  xcm32lxx_cmu.o(i.CMU_HCLKConfig)
    i.CMU_HSIConfig                          0x00005bec   Section        0  xcm32lxx_cmu.o(i.CMU_HSIConfig)
    i.CMU_LSEConfig                          0x00005c6c   Section        0  xcm32lxx_cmu.o(i.CMU_LSEConfig)
    i.CMU_PCLKConfig                         0x00005ccc   Section        0  xcm32lxx_cmu.o(i.CMU_PCLKConfig)
    i.CMU_RTCCLKConfig                       0x00005d00   Section        0  xcm32lxx_cmu.o(i.CMU_RTCCLKConfig)
    i.CMU_SysClkConfig                       0x00005d2c   Section        0  xcm32lxx_cmu.o(i.CMU_SysClkConfig)
    i.CMU_WaitForSysClkStartUp               0x00005d5c   Section        0  xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp)
    i.DelayMs                                0x00005dec   Section        0  main.o(i.DelayMs)
    i.Delay_250ms                            0x00005e0c   Section        0  main.o(i.Delay_250ms)
    i.GetRtcSecond                           0x00005e30   Section        0  rtc.o(i.GetRtcSecond)
    i.I2C1_ReceiveData                       0x00005e9a   Section        0  i2cuart.o(i.I2C1_ReceiveData)
    i.I2C1_TransmitData                      0x00005ee6   Section        0  i2cuart.o(i.I2C1_TransmitData)
    i.I2C_Start                              0x00005f28   Section        0  i2cuart.o(i.I2C_Start)
    i.I2C_Stop                               0x00005f64   Section        0  i2cuart.o(i.I2C_Stop)
    i.I2C_init                               0x00005fa0   Section        0  i2cuart.o(i.I2C_init)
    i.I2C_read_byte                          0x00005fc4   Section        0  i2cuart.o(i.I2C_read_byte)
    i.I2C_send_byte                          0x00006040   Section        0  i2cuart.o(i.I2C_send_byte)
    i.Is_Leap_Year                           0x000060c4   Section        0  rtc.o(i.Is_Leap_Year)
    i.Master_ACK                             0x00006100   Section        0  i2cuart.o(i.Master_ACK)
    i.NVIC_ClearPendingIRQ                   0x0000616c   Section        0  main.o(i.NVIC_ClearPendingIRQ)
    NVIC_ClearPendingIRQ                     0x0000616d   Thumb Code    14  main.o(i.NVIC_ClearPendingIRQ)
    i.NVIC_ClearPendingIRQ                   0x00006180   Section        0  uart.o(i.NVIC_ClearPendingIRQ)
    NVIC_ClearPendingIRQ                     0x00006181   Thumb Code    14  uart.o(i.NVIC_ClearPendingIRQ)
    i.NVIC_DisableIRQ                        0x00006194   Section        0  main.o(i.NVIC_DisableIRQ)
    NVIC_DisableIRQ                          0x00006195   Thumb Code    14  main.o(i.NVIC_DisableIRQ)
    i.NVIC_DisableIRQ                        0x000061a8   Section        0  uart.o(i.NVIC_DisableIRQ)
    NVIC_DisableIRQ                          0x000061a9   Thumb Code    14  uart.o(i.NVIC_DisableIRQ)
    i.NVIC_EnableIRQ                         0x000061bc   Section        0  main.o(i.NVIC_EnableIRQ)
    NVIC_EnableIRQ                           0x000061bd   Thumb Code    14  main.o(i.NVIC_EnableIRQ)
    i.NVIC_EnableIRQ                         0x000061d0   Section        0  uart.o(i.NVIC_EnableIRQ)
    NVIC_EnableIRQ                           0x000061d1   Thumb Code    14  uart.o(i.NVIC_EnableIRQ)
    i.NVIC_Init_rtc                          0x000061e4   Section        0  rtc.o(i.NVIC_Init_rtc)
    NVIC_Init_rtc                            0x000061e5   Thumb Code    52  rtc.o(i.NVIC_Init_rtc)
    i.NVIC_SetPriority                       0x00006220   Section        0  main.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006221   Thumb Code   110  main.o(i.NVIC_SetPriority)
    i.NVIC_SetPriority                       0x00006298   Section        0  rtc.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006299   Thumb Code   110  rtc.o(i.NVIC_SetPriority)
    i.NVIC_SetPriority                       0x00006310   Section        0  uart.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006311   Thumb Code   110  uart.o(i.NVIC_SetPriority)
    i.PORT_DeInit                            0x00006388   Section        0  xcm32lxx_port.o(i.PORT_DeInit)
    i.PORT_DirSet                            0x000063b4   Section        0  i2cuart.o(i.PORT_DirSet)
    i.PORT_Init                              0x000063d0   Section        0  xcm32lxx_port.o(i.PORT_Init)
    i.PORT_PinAFConfig                       0x00006440   Section        0  xcm32lxx_port.o(i.PORT_PinAFConfig)
    i.PORT_ReadInputDataBit                  0x0000648a   Section        0  xcm32lxx_port.o(i.PORT_ReadInputDataBit)
    i.PORT_StructInit                        0x0000649c   Section        0  xcm32lxx_port.o(i.PORT_StructInit)
    i.PORT_WriteBit                          0x000064b8   Section        0  xcm32lxx_port.o(i.PORT_WriteBit)
    i.RTC_ClearITFlag                        0x000064f8   Section        0  xcm32lxx_rtc.o(i.RTC_ClearITFlag)
    i.RTC_Cmd                                0x0000650c   Section        0  xcm32lxx_rtc.o(i.RTC_Cmd)
    i.RTC_DeInit                             0x00006530   Section        0  xcm32lxx_rtc.o(i.RTC_DeInit)
    i.RTC_GetDay                             0x0000655c   Section        0  xcm32lxx_rtc.o(i.RTC_GetDay)
    i.RTC_GetHour                            0x0000656c   Section        0  xcm32lxx_rtc.o(i.RTC_GetHour)
    i.RTC_GetITStatus                        0x0000657c   Section        0  xcm32lxx_rtc.o(i.RTC_GetITStatus)
    i.RTC_GetMinute                          0x00006590   Section        0  xcm32lxx_rtc.o(i.RTC_GetMinute)
    i.RTC_GetMonth                           0x000065a0   Section        0  xcm32lxx_rtc.o(i.RTC_GetMonth)
    i.RTC_GetSecond                          0x000065b0   Section        0  xcm32lxx_rtc.o(i.RTC_GetSecond)
    i.RTC_GetWeek                            0x000065c0   Section        0  rtc.o(i.RTC_GetWeek)
    i.RTC_GetYear                            0x00006628   Section        0  xcm32lxx_rtc.o(i.RTC_GetYear)
    i.RTC_GetYearLSB                         0x0000663c   Section        0  xcm32lxx_rtc.o(i.RTC_GetYearLSB)
    i.RTC_IRQHandler                         0x00006648   Section        0  rtc.o(i.RTC_IRQHandler)
    i.RTC_ITConfig                           0x00006658   Section        0  xcm32lxx_rtc.o(i.RTC_ITConfig)
    i.RTC_Init                               0x000066d8   Section        0  xcm32lxx_rtc.o(i.RTC_Init)
    i.RTC_Run_Init                           0x000067bc   Section        0  rtc.o(i.RTC_Run_Init)
    i.RTC_StructInit                         0x00006810   Section        0  xcm32lxx_rtc.o(i.RTC_StructInit)
    i.RTC_To_Sec                             0x00006834   Section        0  rtc.o(i.RTC_To_Sec)
    i.SetSysClock                            0x00006914   Section        0  system_xcm32l.o(i.SetSysClock)
    SetSysClock                              0x00006915   Thumb Code     2  system_xcm32l.o(i.SetSysClock)
    i.SysTickConfigure                       0x00006918   Section        0  main.o(i.SysTickConfigure)
    i.SysTick_CLKSourceConfig                0x00006964   Section        0  xcm32lxx_systick.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Cmd                            0x000069a8   Section        0  xcm32lxx_systick.o(i.SysTick_Cmd)
    i.SysTick_Handler                        0x000069cc   Section        0  main.o(i.SysTick_Handler)
    i.SysTick_ITConfig                       0x000069f0   Section        0  xcm32lxx_systick.o(i.SysTick_ITConfig)
    i.SysTick_SetReloadValue                 0x00006a14   Section        0  xcm32lxx_systick.o(i.SysTick_SetReloadValue)
    i.SystemInit                             0x00006a24   Section        0  system_xcm32l.o(i.SystemInit)
    i.Test_ACK                               0x00006b24   Section        0  i2cuart.o(i.Test_ACK)
    i.UART1_IRQHandler                       0x00006b90   Section        0  uart.o(i.UART1_IRQHandler)
    i.UART1_Init                             0x00006bec   Section        0  uart.o(i.UART1_Init)
    i.UART2_IRQHandler                       0x00006cd0   Section        0  uart.o(i.UART2_IRQHandler)
    i.UART2_Init                             0x00006d8c   Section        0  uart.o(i.UART2_Init)
    i.UART4_IRQHandler                       0x00006e94   Section        0  uart.o(i.UART4_IRQHandler)
    i.UART4_Init                             0x00006ed0   Section        0  uart.o(i.UART4_Init)
    i.UART_DeInit                            0x00006fbc   Section        0  xcm32lxx_uart.o(i.UART_DeInit)
    i.UART_FIFOModeConfig                    0x00007028   Section        0  xcm32lxx_uart.o(i.UART_FIFOModeConfig)
    i.UART_GetITStatus                       0x0000707c   Section        0  xcm32lxx_uart.o(i.UART_GetITStatus)
    i.UART_GetLineStatus                     0x00007090   Section        0  xcm32lxx_uart.o(i.UART_GetLineStatus)
    i.UART_ITConfig                          0x000070a2   Section        0  xcm32lxx_uart.o(i.UART_ITConfig)
    i.UART_Init                              0x000070b8   Section        0  xcm32lxx_uart.o(i.UART_Init)
    i.UART_PTXREModeConfig                   0x00007374   Section        0  xcm32lxx_uart.o(i.UART_PTXREModeConfig)
    i.UART_ReceiveData                       0x0000738c   Section        0  xcm32lxx_uart.o(i.UART_ReceiveData)
    i.UART_SendData                          0x00007394   Section        0  xcm32lxx_uart.o(i.UART_SendData)
    i.UART_StructInit                        0x0000739a   Section        0  xcm32lxx_uart.o(i.UART_StructInit)
    i.WDT_ClearITFlag                        0x000073b4   Section        0  xcm32lxx_wdt.o(i.WDT_ClearITFlag)
    i.WDT_GetITStatus                        0x000073c4   Section        0  xcm32lxx_wdt.o(i.WDT_GetITStatus)
    i.WDT_IRQHandler                         0x000073d8   Section        0  main.o(i.WDT_IRQHandler)
    i.WDT_RestartCmd                         0x000073f0   Section        0  xcm32lxx_wdt.o(i.WDT_RestartCmd)
    i.__0printf                              0x00007414   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x00007434   Section        0  printfa.o(i.__0snprintf)
    i.__0sprintf                             0x00007460   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x00007488   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_common_switch8                   0x000074b6   Section        0  cjson.o(i.__ARM_common_switch8)
    i.__ARM_fpclassify                       0x000074d4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x00007500   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x000075ac   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x000075c0   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x000075c8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x000075d8   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x000075ec   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x00007600   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0000760e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00007610   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x00007620   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0000762c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0000762d   Thumb Code   316  printfa.o(i._fp_digits)
    i._printf_core                           0x00007784   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x00007785   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_post_padding                   0x00007e70   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x00007e71   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x00007e90   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x00007e91   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._snputc                                0x00007ebc   Section        0  printfa.o(i._snputc)
    _snputc                                  0x00007ebd   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x00007ed2   Section        0  printfa.o(i._sputc)
    _sputc                                   0x00007ed3   Thumb Code    10  printfa.o(i._sputc)
    i.cJSON_AddItemToArray                   0x00007edc   Section        0  cjson.o(i.cJSON_AddItemToArray)
    i.cJSON_AddItemToObject                  0x00007f0c   Section        0  cjson.o(i.cJSON_AddItemToObject)
    i.cJSON_CreateNumber                     0x00007f40   Section        0  cjson.o(i.cJSON_CreateNumber)
    i.cJSON_CreateObject                     0x00007f66   Section        0  cjson.o(i.cJSON_CreateObject)
    i.cJSON_CreateString                     0x00007f7a   Section        0  cjson.o(i.cJSON_CreateString)
    i.cJSON_Delete                           0x00007f98   Section        0  cjson.o(i.cJSON_Delete)
    i.cJSON_GetObjectItem                    0x00008000   Section        0  cjson.o(i.cJSON_GetObjectItem)
    i.cJSON_New_Item                         0x00008020   Section        0  cjson.o(i.cJSON_New_Item)
    cJSON_New_Item                           0x00008021   Thumb Code    28  cjson.o(i.cJSON_New_Item)
    i.cJSON_Parse                            0x00008040   Section        0  cjson.o(i.cJSON_Parse)
    i.cJSON_ParseWithOpts                    0x00008050   Section        0  cjson.o(i.cJSON_ParseWithOpts)
    i.cJSON_Print                            0x000080bc   Section        0  cjson.o(i.cJSON_Print)
    i.cJSON_strcasecmp                       0x000080ce   Section        0  cjson.o(i.cJSON_strcasecmp)
    cJSON_strcasecmp                         0x000080cf   Thumb Code    80  cjson.o(i.cJSON_strcasecmp)
    i.cJSON_strdup                           0x00008120   Section        0  cjson.o(i.cJSON_strdup)
    cJSON_strdup                             0x00008121   Thumb Code    40  cjson.o(i.cJSON_strdup)
    i.cJsonParseProtocol                     0x0000814c   Section        0  protol.o(i.cJsonParseProtocol)
    i.crc32                                  0x00009bb0   Section        0  crc32.o(i.crc32)
    i.delay                                  0x00009c88   Section        0  i2cuart.o(i.delay)
    i.delay_5us                              0x00009c9e   Section        0  i2cuart.o(i.delay_5us)
    i.ensure                                 0x00009cb8   Section        0  cjson.o(i.ensure)
    ensure                                   0x00009cb9   Thumb Code   108  cjson.o(i.ensure)
    i.enter_stop_mode                        0x00009d2c   Section        0  user_main.o(i.enter_stop_mode)
    i.floor                                  0x00009dc0   Section        0  floor.o(i.floor)
    i.fputc                                  0x00009e88   Section        0  main.o(i.fputc)
    i.free                                   0x00009eac   Section        0  malloc.o(i.free)
    i.get_voltage                            0x00009f00   Section        0  i2cuart.o(i.get_voltage)
    i.main                                   0x00009fd8   Section        0  main.o(i.main)
    i.malloc                                 0x0000a158   Section        0  malloc.o(i.malloc)
    i.pack_data                              0x0000a1c4   Section        0  uart.o(i.pack_data)
    i.pack_luna_data                         0x0000a258   Section        0  uart.o(i.pack_luna_data)
    i.parse_array                            0x0000a2fc   Section        0  cjson.o(i.parse_array)
    parse_array                              0x0000a2fd   Thumb Code   162  cjson.o(i.parse_array)
    i.parse_hex4                             0x0000a3a4   Section        0  cjson.o(i.parse_hex4)
    parse_hex4                               0x0000a3a5   Thumb Code   276  cjson.o(i.parse_hex4)
    i.parse_number                           0x0000a4b8   Section        0  cjson.o(i.parse_number)
    parse_number                             0x0000a4b9   Thumb Code   384  cjson.o(i.parse_number)
    i.parse_object                           0x0000a644   Section        0  cjson.o(i.parse_object)
    parse_object                             0x0000a645   Thumb Code   266  cjson.o(i.parse_object)
    i.parse_string                           0x0000a754   Section        0  cjson.o(i.parse_string)
    parse_string                             0x0000a755   Thumb Code   472  cjson.o(i.parse_string)
    i.parse_value                            0x0000a940   Section        0  cjson.o(i.parse_value)
    parse_value                              0x0000a941   Thumb Code   164  cjson.o(i.parse_value)
    i.pow                                    0x0000aa00   Section        0  pow.o(i.pow)
    i.pow2gt                                 0x0000b3fc   Section        0  cjson.o(i.pow2gt)
    pow2gt                                   0x0000b3fd   Thumb Code    28  cjson.o(i.pow2gt)
    i.print_array                            0x0000b418   Section        0  cjson.o(i.print_array)
    print_array                              0x0000b419   Thumb Code   596  cjson.o(i.print_array)
    i.print_number                           0x0000b678   Section        0  cjson.o(i.print_number)
    print_number                             0x0000b679   Thumb Code   360  cjson.o(i.print_number)
    i.print_object                           0x0000b820   Section        0  cjson.o(i.print_object)
    print_object                             0x0000b821   Thumb Code  1160  cjson.o(i.print_object)
    i.print_string                           0x0000bca8   Section        0  cjson.o(i.print_string)
    print_string                             0x0000bca9   Thumb Code    16  cjson.o(i.print_string)
    i.print_string_ptr                       0x0000bcb8   Section        0  cjson.o(i.print_string_ptr)
    print_string_ptr                         0x0000bcb9   Thumb Code   448  cjson.o(i.print_string_ptr)
    i.print_value                            0x0000be90   Section        0  cjson.o(i.print_value)
    print_value                              0x0000be91   Thumb Code   282  cjson.o(i.print_value)
    i.skip                                   0x0000bfc4   Section        0  cjson.o(i.skip)
    skip                                     0x0000bfc5   Thumb Code    22  cjson.o(i.skip)
    i.sqrt                                   0x0000bfdc   Section        0  sqrt.o(i.sqrt)
    i.suffix_object                          0x0000c024   Section        0  cjson.o(i.suffix_object)
    suffix_object                            0x0000c025   Thumb Code     6  cjson.o(i.suffix_object)
    i.t_BCD2HEX                              0x0000c02a   Section        0  rtc.o(i.t_BCD2HEX)
    i.uart1_rx_mengmu_process                0x0000c078   Section        0  uart.o(i.uart1_rx_mengmu_process)
    i.uart2_com_tx                           0x0000c394   Section        0  uart.o(i.uart2_com_tx)
    i.uart2_com_tx_data                      0x0000c3f0   Section        0  uart.o(i.uart2_com_tx_data)
    i.uart2_find_head_and_tail               0x0000c424   Section        0  uart.o(i.uart2_find_head_and_tail)
    i.uart2_get_tx_buffer_size               0x0000c568   Section        0  uart.o(i.uart2_get_tx_buffer_size)
    i.uart2_rx_process                       0x0000c57c   Section        0  uart.o(i.uart2_rx_process)
    i.uart2_tx_buffer_in                     0x0000c77c   Section        0  uart.o(i.uart2_tx_buffer_in)
    i.uart2_tx_buffer_out                    0x0000c7c8   Section        0  uart.o(i.uart2_tx_buffer_out)
    i.uart2_unpack_data                      0x0000c818   Section        0  uart.o(i.uart2_unpack_data)
    i.uf_GPIO_CMU_Init                       0x0000c8a8   Section        0  rtc.o(i.uf_GPIO_CMU_Init)
    i.uf_GPIO_Init                           0x0000c8cc   Section        0  gpio.o(i.uf_GPIO_Init)
    i.uf_GPIO_RTC_Init                       0x0000c9b0   Section        0  rtc.o(i.uf_GPIO_RTC_Init)
    i.uf_RTC_Init                            0x0000c9f0   Section        0  rtc.o(i.uf_RTC_Init)
    i.update                                 0x0000ca88   Section        0  cjson.o(i.update)
    update                                   0x0000ca89   Thumb Code    36  cjson.o(i.update)
    i.user_main                              0x0000caac   Section        0  user_main.o(i.user_main)
    .constdata                               0x0000d2fc   Section        7  cjson.o(.constdata)
    firstByteMark                            0x0000d2fc   Data           7  cjson.o(.constdata)
    .constdata                               0x0000d304   Section     1024  crc32.o(.constdata)
    crc_table                                0x0000d304   Data        1024  crc32.o(.constdata)
    .constdata                               0x0000d704   Section       12  rtc.o(.constdata)
    .constdata                               0x0000d710   Section      136  pow.o(.constdata)
    bp                                       0x0000d710   Data          16  pow.o(.constdata)
    dp_h                                     0x0000d720   Data          16  pow.o(.constdata)
    dp_l                                     0x0000d730   Data          16  pow.o(.constdata)
    L                                        0x0000d740   Data          48  pow.o(.constdata)
    P                                        0x0000d770   Data          40  pow.o(.constdata)
    .constdata                               0x0000d798   Section        8  qnan.o(.constdata)
    .conststring                             0x0000d7a0   Section       43  user_main.o(.conststring)
    .data                                    0x20000000   Section       12  cjson.o(.data)
    ep                                       0x20000000   Data           4  cjson.o(.data)
    cJSON_malloc                             0x20000004   Data           4  cjson.o(.data)
    cJSON_free                               0x20000008   Data           4  cjson.o(.data)
    .data                                    0x20000010   Section       56  protol.o(.data)
    .data                                    0x20000048   Section       40  i2cuart.o(.data)
    P1                                       0x20000048   Data           4  i2cuart.o(.data)
    P2                                       0x2000004c   Data           4  i2cuart.o(.data)
    P3                                       0x20000050   Data           4  i2cuart.o(.data)
    iVoltage                                 0x20000054   Data           4  i2cuart.o(.data)
    g_Voltage                                0x20000058   Data           4  i2cuart.o(.data)
    P1                                       0x2000005c   Data           4  i2cuart.o(.data)
    P2                                       0x20000060   Data           4  i2cuart.o(.data)
    P3                                       0x20000064   Data           4  i2cuart.o(.data)
    iVoltage                                 0x20000068   Data           4  i2cuart.o(.data)
    g_Voltage                                0x2000006c   Data           4  i2cuart.o(.data)
    .data                                    0x20000070   Section        4  main.o(.data)
    .data                                    0x20000074   Section       20  rtc.o(.data)
    .data                                    0x20000088   Section       20  uart.o(.data)
    s_seq                                    0x20000096   Data           2  uart.o(.data)
    s_seq                                    0x20000098   Data           2  uart.o(.data)
    s_seq                                    0x2000009a   Data           2  uart.o(.data)
    .data                                    0x200000a0   Section      176  user_main.o(.data)
    .data                                    0x20000150   Section       28  xcm32lxx_port.o(.data)
    gvPortOut                                0x20000150   Data          28  xcm32lxx_port.o(.data)
    .data                                    0x2000016c   Section        4  stdout.o(.data)
    .data                                    0x20000170   Section        4  mvars.o(.data)
    .data                                    0x20000174   Section        4  mvars.o(.data)
    .data                                    0x20000178   Section        4  errno.o(.data)
    _errno                                   0x20000178   Data           4  errno.o(.data)
    .bss                                     0x20000180   Section     4428  uart.o(.bss)
    s_buf_print                              0x200011cc   Data         256  uart.o(.bss)
    .bss                                     0x200012d0   Section      224  user_main.o(.bss)
    HEAP                                     0x200013b0   Section     3072  startup_xcm32l.o(HEAP)
    STACK                                    0x20001fb0   Section     1024  startup_xcm32l.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000c0   Number         0  startup_xcm32l.o ABSOLUTE
    __Vectors                                0x00005000   Data           4  startup_xcm32l.o(RESET)
    __Vectors_End                            0x000050c0   Data           0  startup_xcm32l.o(RESET)
    __main                                   0x000050c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000050c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000050c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000050c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000050c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000050c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000050c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000050d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000050d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000050d5   Thumb Code     8  startup_xcm32l.o(.text)
    NMI_Handler                              0x000050dd   Thumb Code     2  startup_xcm32l.o(.text)
    HardFault_Handler                        0x000050df   Thumb Code     2  startup_xcm32l.o(.text)
    SVC_Handler                              0x000050e1   Thumb Code     2  startup_xcm32l.o(.text)
    PendSV_Handler                           0x000050e3   Thumb Code     2  startup_xcm32l.o(.text)
    ADC_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    BASETIMER_IRQHandler                     0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    DMAC_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    I2C1_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    I2C2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    LCD_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    LVD_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P0_IRQHandler                            0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P1P2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P3P4_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P5P6_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    PCA12_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    PCA34_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    RAM_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SCI7816_IRQHandler                       0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI1_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI3_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI4_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER1_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER2_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER3_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER4_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART3_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART5_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART6_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    VC_IRQHandler                            0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    __aeabi_idiv                             0x000050f1   Thumb Code     0  idiv.o(.text)
    __aeabi_idivmod                          0x000050f1   Thumb Code    40  idiv.o(.text)
    __aeabi_uldivmod                         0x00005119   Thumb Code    96  uldiv.o(.text)
    __aeabi_ldivmod                          0x00005179   Thumb Code    76  ldiv.o(.text)
    tolower                                  0x000051c5   Thumb Code    12  tolower.o(.text)
    __aeabi_memcpy                           0x000051d1   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x000051d1   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x000051d1   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000051f5   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000051f5   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000051f5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x00005203   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x00005203   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x00005203   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x00005207   Thumb Code    18  memseta.o(.text)
    strchr                                   0x00005219   Thumb Code    20  strchr.o(.text)
    strlen                                   0x0000522d   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x0000523b   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x00005257   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x00005269   Thumb Code    30  strncmp.o(.text)
    __aeabi_fdiv                             0x00005287   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x00005305   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x0000544d   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00005459   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00005469   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x00005539   Thumb Code   234  ddiv.o(.text)
    __aeabi_ui2f                             0x00005629   Thumb Code    14  ffltui.o(.text)
    __aeabi_i2d                              0x00005639   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x00005661   Thumb Code    24  dfltui.o(.text)
    __aeabi_f2uiz                            0x0000567d   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2iz                             0x000056a5   Thumb Code    62  dfixi.o(.text)
    __aeabi_d2uiz                            0x000056ed   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x00005729   Thumb Code    40  f2d.o(.text)
    __aeabi_cdcmpeq                          0x00005751   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x00005751   Thumb Code    38  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x00005779   Thumb Code    38  cdrcmple.o(.text)
    __aeabi_d2f                              0x000057a1   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x000057d9   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x000057d9   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x00005805   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00005805   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00005825   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00005825   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x00005847   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x00005847   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0000586d   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0000586d   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x0000587d   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000058ef   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00005909   Thumb Code   164  depilogue.o(.text)
    __ARM_scalbn                             0x000059ad   Thumb Code    44  dscalb.o(.text)
    scalbn                                   0x000059ad   Thumb Code     0  dscalb.o(.text)
    __aeabi_d2ulz                            0x000059d9   Thumb Code    54  dfixul.o(.text)
    __scatterload                            0x00005a19   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00005a19   Thumb Code     0  init.o(.text)
    _dsqrt                                   0x00005a3d   Thumb Code   162  dsqrt.o(.text)
    __decompress                             0x00005adf   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x00005adf   Thumb Code    86  __dczerorl2.o(.text)
    Booster_Init                             0x00005b35   Thumb Code    62  main.o(i.Booster_Init)
    CMU_APBPeriph0ClockCmd                   0x00005b79   Thumb Code    28  xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd)
    CMU_APBPeriph1ClockCmd                   0x00005b99   Thumb Code    28  xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd)
    CMU_HCLKConfig                           0x00005bb9   Thumb Code    48  xcm32lxx_cmu.o(i.CMU_HCLKConfig)
    CMU_HSIConfig                            0x00005bed   Thumb Code   118  xcm32lxx_cmu.o(i.CMU_HSIConfig)
    CMU_LSEConfig                            0x00005c6d   Thumb Code    92  xcm32lxx_cmu.o(i.CMU_LSEConfig)
    CMU_PCLKConfig                           0x00005ccd   Thumb Code    48  xcm32lxx_cmu.o(i.CMU_PCLKConfig)
    CMU_RTCCLKConfig                         0x00005d01   Thumb Code    40  xcm32lxx_cmu.o(i.CMU_RTCCLKConfig)
    CMU_SysClkConfig                         0x00005d2d   Thumb Code    44  xcm32lxx_cmu.o(i.CMU_SysClkConfig)
    CMU_WaitForSysClkStartUp                 0x00005d5d   Thumb Code   140  xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp)
    DelayMs                                  0x00005ded   Thumb Code    30  main.o(i.DelayMs)
    Delay_250ms                              0x00005e0d   Thumb Code    30  main.o(i.Delay_250ms)
    GetRtcSecond                             0x00005e31   Thumb Code   106  rtc.o(i.GetRtcSecond)
    I2C1_ReceiveData                         0x00005e9b   Thumb Code    76  i2cuart.o(i.I2C1_ReceiveData)
    I2C1_TransmitData                        0x00005ee7   Thumb Code    66  i2cuart.o(i.I2C1_TransmitData)
    I2C_Start                                0x00005f29   Thumb Code    56  i2cuart.o(i.I2C_Start)
    I2C_Stop                                 0x00005f65   Thumb Code    56  i2cuart.o(i.I2C_Stop)
    I2C_init                                 0x00005fa1   Thumb Code    32  i2cuart.o(i.I2C_init)
    I2C_read_byte                            0x00005fc5   Thumb Code   120  i2cuart.o(i.I2C_read_byte)
    I2C_send_byte                            0x00006041   Thumb Code   126  i2cuart.o(i.I2C_send_byte)
    Is_Leap_Year                             0x000060c5   Thumb Code    60  rtc.o(i.Is_Leap_Year)
    Master_ACK                               0x00006101   Thumb Code   102  i2cuart.o(i.Master_ACK)
    PORT_DeInit                              0x00006389   Thumb Code    38  xcm32lxx_port.o(i.PORT_DeInit)
    PORT_DirSet                              0x000063b5   Thumb Code    28  i2cuart.o(i.PORT_DirSet)
    PORT_Init                                0x000063d1   Thumb Code   112  xcm32lxx_port.o(i.PORT_Init)
    PORT_PinAFConfig                         0x00006441   Thumb Code    74  xcm32lxx_port.o(i.PORT_PinAFConfig)
    PORT_ReadInputDataBit                    0x0000648b   Thumb Code    18  xcm32lxx_port.o(i.PORT_ReadInputDataBit)
    PORT_StructInit                          0x0000649d   Thumb Code    22  xcm32lxx_port.o(i.PORT_StructInit)
    PORT_WriteBit                            0x000064b9   Thumb Code    54  xcm32lxx_port.o(i.PORT_WriteBit)
    RTC_ClearITFlag                          0x000064f9   Thumb Code    14  xcm32lxx_rtc.o(i.RTC_ClearITFlag)
    RTC_Cmd                                  0x0000650d   Thumb Code    32  xcm32lxx_rtc.o(i.RTC_Cmd)
    RTC_DeInit                               0x00006531   Thumb Code    38  xcm32lxx_rtc.o(i.RTC_DeInit)
    RTC_GetDay                               0x0000655d   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetDay)
    RTC_GetHour                              0x0000656d   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetHour)
    RTC_GetITStatus                          0x0000657d   Thumb Code    16  xcm32lxx_rtc.o(i.RTC_GetITStatus)
    RTC_GetMinute                            0x00006591   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetMinute)
    RTC_GetMonth                             0x000065a1   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetMonth)
    RTC_GetSecond                            0x000065b1   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetSecond)
    RTC_GetWeek                              0x000065c1   Thumb Code   104  rtc.o(i.RTC_GetWeek)
    RTC_GetYear                              0x00006629   Thumb Code    16  xcm32lxx_rtc.o(i.RTC_GetYear)
    RTC_GetYearLSB                           0x0000663d   Thumb Code     8  xcm32lxx_rtc.o(i.RTC_GetYearLSB)
    RTC_IRQHandler                           0x00006649   Thumb Code    16  rtc.o(i.RTC_IRQHandler)
    RTC_ITConfig                             0x00006659   Thumb Code   124  xcm32lxx_rtc.o(i.RTC_ITConfig)
    RTC_Init                                 0x000066d9   Thumb Code   222  xcm32lxx_rtc.o(i.RTC_Init)
    RTC_Run_Init                             0x000067bd   Thumb Code    40  rtc.o(i.RTC_Run_Init)
    RTC_StructInit                           0x00006811   Thumb Code    34  xcm32lxx_rtc.o(i.RTC_StructInit)
    RTC_To_Sec                               0x00006835   Thumb Code   204  rtc.o(i.RTC_To_Sec)
    SysTickConfigure                         0x00006919   Thumb Code    72  main.o(i.SysTickConfigure)
    SysTick_CLKSourceConfig                  0x00006965   Thumb Code    62  xcm32lxx_systick.o(i.SysTick_CLKSourceConfig)
    SysTick_Cmd                              0x000069a9   Thumb Code    32  xcm32lxx_systick.o(i.SysTick_Cmd)
    SysTick_Handler                          0x000069cd   Thumb Code    26  main.o(i.SysTick_Handler)
    SysTick_ITConfig                         0x000069f1   Thumb Code    32  xcm32lxx_systick.o(i.SysTick_ITConfig)
    SysTick_SetReloadValue                   0x00006a15   Thumb Code    10  xcm32lxx_systick.o(i.SysTick_SetReloadValue)
    SystemInit                               0x00006a25   Thumb Code   236  system_xcm32l.o(i.SystemInit)
    Test_ACK                                 0x00006b25   Thumb Code   104  i2cuart.o(i.Test_ACK)
    UART1_IRQHandler                         0x00006b91   Thumb Code    82  uart.o(i.UART1_IRQHandler)
    UART1_Init                               0x00006bed   Thumb Code   224  uart.o(i.UART1_Init)
    UART2_IRQHandler                         0x00006cd1   Thumb Code   136  uart.o(i.UART2_IRQHandler)
    UART2_Init                               0x00006d8d   Thumb Code   252  uart.o(i.UART2_Init)
    UART4_IRQHandler                         0x00006e95   Thumb Code    54  uart.o(i.UART4_IRQHandler)
    UART4_Init                               0x00006ed1   Thumb Code   222  uart.o(i.UART4_Init)
    UART_DeInit                              0x00006fbd   Thumb Code    88  xcm32lxx_uart.o(i.UART_DeInit)
    UART_FIFOModeConfig                      0x00007029   Thumb Code    84  xcm32lxx_uart.o(i.UART_FIFOModeConfig)
    UART_GetITStatus                         0x0000707d   Thumb Code    20  xcm32lxx_uart.o(i.UART_GetITStatus)
    UART_GetLineStatus                       0x00007091   Thumb Code    18  xcm32lxx_uart.o(i.UART_GetLineStatus)
    UART_ITConfig                            0x000070a3   Thumb Code    20  xcm32lxx_uart.o(i.UART_ITConfig)
    UART_Init                                0x000070b9   Thumb Code   680  xcm32lxx_uart.o(i.UART_Init)
    UART_PTXREModeConfig                     0x00007375   Thumb Code    24  xcm32lxx_uart.o(i.UART_PTXREModeConfig)
    UART_ReceiveData                         0x0000738d   Thumb Code     8  xcm32lxx_uart.o(i.UART_ReceiveData)
    UART_SendData                            0x00007395   Thumb Code     6  xcm32lxx_uart.o(i.UART_SendData)
    UART_StructInit                          0x0000739b   Thumb Code    24  xcm32lxx_uart.o(i.UART_StructInit)
    WDT_ClearITFlag                          0x000073b5   Thumb Code    10  xcm32lxx_wdt.o(i.WDT_ClearITFlag)
    WDT_GetITStatus                          0x000073c5   Thumb Code    16  xcm32lxx_wdt.o(i.WDT_GetITStatus)
    WDT_IRQHandler                           0x000073d9   Thumb Code    22  main.o(i.WDT_IRQHandler)
    WDT_RestartCmd                           0x000073f1   Thumb Code    26  xcm32lxx_wdt.o(i.WDT_RestartCmd)
    __0printf                                0x00007415   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x00007415   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x00007415   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x00007415   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x00007415   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x00007435   Thumb Code    38  printfa.o(i.__0snprintf)
    __1snprintf                              0x00007435   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x00007435   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x00007435   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x00007435   Thumb Code     0  printfa.o(i.__0snprintf)
    __0sprintf                               0x00007461   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x00007461   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x00007461   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x00007461   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x00007461   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x00007489   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_common_switch8                     0x000074b7   Thumb Code    28  cjson.o(i.__ARM_common_switch8)
    __ARM_fpclassify                         0x000074d5   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x00007501   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x000075ad   Thumb Code    16  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x000075c1   Thumb Code     8  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x000075c9   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x000075d9   Thumb Code    16  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x000075ed   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x00007601   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0000760f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00007611   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x00007621   Thumb Code     6  errno.o(i.__set_errno)
    cJSON_AddItemToArray                     0x00007edd   Thumb Code    48  cjson.o(i.cJSON_AddItemToArray)
    cJSON_AddItemToObject                    0x00007f0d   Thumb Code    48  cjson.o(i.cJSON_AddItemToObject)
    cJSON_CreateNumber                       0x00007f41   Thumb Code    38  cjson.o(i.cJSON_CreateNumber)
    cJSON_CreateObject                       0x00007f67   Thumb Code    20  cjson.o(i.cJSON_CreateObject)
    cJSON_CreateString                       0x00007f7b   Thumb Code    30  cjson.o(i.cJSON_CreateString)
    cJSON_Delete                             0x00007f99   Thumb Code   100  cjson.o(i.cJSON_Delete)
    cJSON_GetObjectItem                      0x00008001   Thumb Code    32  cjson.o(i.cJSON_GetObjectItem)
    cJSON_Parse                              0x00008041   Thumb Code    16  cjson.o(i.cJSON_Parse)
    cJSON_ParseWithOpts                      0x00008051   Thumb Code   104  cjson.o(i.cJSON_ParseWithOpts)
    cJSON_Print                              0x000080bd   Thumb Code    18  cjson.o(i.cJSON_Print)
    cJsonParseProtocol                       0x0000814d   Thumb Code  6688  protol.o(i.cJsonParseProtocol)
    crc32                                    0x00009bb1   Thumb Code   210  crc32.o(i.crc32)
    delay                                    0x00009c89   Thumb Code    22  i2cuart.o(i.delay)
    delay_5us                                0x00009c9f   Thumb Code    26  i2cuart.o(i.delay_5us)
    enter_stop_mode                          0x00009d2d   Thumb Code   104  user_main.o(i.enter_stop_mode)
    floor                                    0x00009dc1   Thumb Code   180  floor.o(i.floor)
    fputc                                    0x00009e89   Thumb Code    32  main.o(i.fputc)
    free                                     0x00009ead   Thumb Code    80  malloc.o(i.free)
    get_voltage                              0x00009f01   Thumb Code   158  i2cuart.o(i.get_voltage)
    main                                     0x00009fd9   Thumb Code   256  main.o(i.main)
    malloc                                   0x0000a159   Thumb Code    92  malloc.o(i.malloc)
    pack_data                                0x0000a1c5   Thumb Code   142  uart.o(i.pack_data)
    pack_luna_data                           0x0000a259   Thumb Code   158  uart.o(i.pack_luna_data)
    pow                                      0x0000aa01   Thumb Code  2548  pow.o(i.pow)
    sqrt                                     0x0000bfdd   Thumb Code    66  sqrt.o(i.sqrt)
    t_BCD2HEX                                0x0000c02b   Thumb Code    76  rtc.o(i.t_BCD2HEX)
    uart1_rx_mengmu_process                  0x0000c079   Thumb Code   772  uart.o(i.uart1_rx_mengmu_process)
    uart2_com_tx                             0x0000c395   Thumb Code    80  uart.o(i.uart2_com_tx)
    uart2_com_tx_data                        0x0000c3f1   Thumb Code    52  uart.o(i.uart2_com_tx_data)
    uart2_find_head_and_tail                 0x0000c425   Thumb Code   324  uart.o(i.uart2_find_head_and_tail)
    uart2_get_tx_buffer_size                 0x0000c569   Thumb Code    14  uart.o(i.uart2_get_tx_buffer_size)
    uart2_rx_process                         0x0000c57d   Thumb Code   492  uart.o(i.uart2_rx_process)
    uart2_tx_buffer_in                       0x0000c77d   Thumb Code    70  uart.o(i.uart2_tx_buffer_in)
    uart2_tx_buffer_out                      0x0000c7c9   Thumb Code    76  uart.o(i.uart2_tx_buffer_out)
    uart2_unpack_data                        0x0000c819   Thumb Code   116  uart.o(i.uart2_unpack_data)
    uf_GPIO_CMU_Init                         0x0000c8a9   Thumb Code    30  rtc.o(i.uf_GPIO_CMU_Init)
    uf_GPIO_Init                             0x0000c8cd   Thumb Code   218  gpio.o(i.uf_GPIO_Init)
    uf_GPIO_RTC_Init                         0x0000c9b1   Thumb Code    58  rtc.o(i.uf_GPIO_RTC_Init)
    uf_RTC_Init                              0x0000c9f1   Thumb Code   148  rtc.o(i.uf_RTC_Init)
    user_main                                0x0000caad   Thumb Code  2004  user_main.o(i.user_main)
    mon_table                                0x0000d704   Data          12  rtc.o(.constdata)
    __mathlib_zero                           0x0000d798   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0000d7cc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0000d7ec   Number         0  anon$$obj.o(Region$$Table)
    g_red_led_status                         0x20000010   Data           4  protol.o(.data)
    g_blue_led_status                        0x20000014   Data           4  protol.o(.data)
    g_green_led_status                       0x20000018   Data           4  protol.o(.data)
    g_trigtype                               0x2000001c   Data           4  protol.o(.data)
    g_last_trigtype                          0x20000020   Data           4  protol.o(.data)
    g_HaveCar                                0x20000024   Data           4  protol.o(.data)
    g_snapfinished                           0x20000028   Data           4  protol.o(.data)
    g_charge_result_time                     0x20000030   Data           8  protol.o(.data)
    g_charge_pay_time                        0x20000038   Data           8  protol.o(.data)
    g_query_time                             0x20000040   Data           4  protol.o(.data)
    startUpPeriod                            0x20000044   Data           4  protol.o(.data)
    TimingDelay                              0x20000070   Data           4  main.o(.data)
    Milli_Flag                               0x20000074   Data           2  rtc.o(.data)
    RTCTime                                  0x20000076   Data           9  rtc.o(.data)
    MSEC                                     0x2000007f   Data           1  rtc.o(.data)
    SEC                                      0x20000080   Data           1  rtc.o(.data)
    MIN                                      0x20000081   Data           1  rtc.o(.data)
    HOUR                                     0x20000082   Data           1  rtc.o(.data)
    DAY                                      0x20000083   Data           1  rtc.o(.data)
    MONTH                                    0x20000084   Data           1  rtc.o(.data)
    YEAR                                     0x20000086   Data           2  rtc.o(.data)
    g_UpdateTime                             0x20000088   Data           8  uart.o(.data)
    _fgUart2TxBusy                           0x20000090   Data           1  uart.o(.data)
    d_uart2_com_protect_tim                  0x20000091   Data           1  uart.o(.data)
    _rUart2isopen                            0x20000092   Data           1  uart.o(.data)
    _fgUart4TxBusy                           0x20000093   Data           1  uart.o(.data)
    d_uart4_com_protect_tim                  0x20000094   Data           1  uart.o(.data)
    _rUart4isopen                            0x20000095   Data           1  uart.o(.data)
    gVersion                                 0x200000a0   Data           4  user_main.o(.data)
    g_iVoltage                               0x200000a4   Data           4  user_main.o(.data)
    g_conn_num                               0x200000a8   Data           4  user_main.o(.data)
    g_Time                                   0x200000b0   Data           8  user_main.o(.data)
    g_Alarm_Time                             0x200000b8   Data           8  user_main.o(.data)
    gPowerOffTime                            0x200000c0   Data           8  user_main.o(.data)
    gPeriodSetTime                           0x200000c8   Data           8  user_main.o(.data)
    gPeriodTrigTime                          0x200000d0   Data           8  user_main.o(.data)
    gRadarTrigModeTime                       0x200000d8   Data           8  user_main.o(.data)
    g_period_trigged                         0x200000e0   Data           4  user_main.o(.data)
    g_hi3516on                               0x200000e4   Data           4  user_main.o(.data)
    g_firstOn                                0x200000e8   Data           4  user_main.o(.data)
    g_radar_status_report_3516               0x200000ec   Data           4  user_main.o(.data)
    g_UltraSonicOn                           0x200000f0   Data           4  user_main.o(.data)
    g_Status                                 0x200000f4   Data           4  user_main.o(.data)
    g_Radar_Status                           0x200000f8   Data           4  user_main.o(.data)
    g_Cover_Status                           0x200000fc   Data           4  user_main.o(.data)
    g_LastDistance                           0x20000100   Data           4  user_main.o(.data)
    g_LastHaveCarDistance                    0x20000104   Data           4  user_main.o(.data)
    g_LastNoCarDistance                      0x20000108   Data           4  user_main.o(.data)
    g_LastRadarStatus                        0x2000010c   Data           4  user_main.o(.data)
    g_firstStatus                            0x20000110   Data           4  user_main.o(.data)
    g_lastHaveCar                            0x20000114   Data           4  user_main.o(.data)
    g_4GOnTime                               0x20000118   Data           8  user_main.o(.data)
    g_XijieRadarIdx                          0x20000120   Data           4  user_main.o(.data)
    g_subIdx                                 0x20000124   Data           4  user_main.o(.data)
    g_CapIdx                                 0x20000128   Data           4  user_main.o(.data)
    g_GetComData                             0x2000012c   Data           4  user_main.o(.data)
    g_GetLog                                 0x20000130   Data           4  user_main.o(.data)
    g_energe                                 0x20000134   Data           2  user_main.o(.data)
    g_distance                               0x20000136   Data           2  user_main.o(.data)
    g_4GOn                                   0x20000138   Data           4  user_main.o(.data)
    g_CarNum                                 0x2000013c   Data           4  user_main.o(.data)
    g_NoCarNum                               0x20000140   Data           4  user_main.o(.data)
    nTrigNum                                 0x20000144   Data           4  user_main.o(.data)
    gLastSendTime                            0x20000148   Data           8  user_main.o(.data)
    __stdout                                 0x2000016c   Data           4  stdout.o(.data)
    __microlib_freelist                      0x20000170   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000174   Data           4  mvars.o(.data)
    RxReadPos                                0x20000180   Data          16  uart.o(.bss)
    RxWritePos                               0x20000190   Data          16  uart.o(.bss)
    uart2_RxBuf                              0x200001a0   Data        1024  uart.o(.bss)
    Uart1_RxBuf                              0x200005a0   Data         256  uart.o(.bss)
    uart_proc_buf                            0x200006a0   Data        1024  uart.o(.bss)
    uart_proced_buf                          0x20000aa0   Data        1024  uart.o(.bss)
    _rUart2Tx                                0x20000ea0   Data         506  uart.o(.bss)
    _rUart4Tx                                0x2000109a   Data         306  uart.o(.bss)
    g_XijieRadarInfo                         0x200012d0   Data         160  user_main.o(.bss)
    g_SubRadarInfo                           0x20001370   Data          64  user_main.o(.bss)
    __heap_base                              0x200013b0   Data           0  startup_xcm32l.o(HEAP)
    __heap_limit                             0x20001fb0   Data           0  startup_xcm32l.o(HEAP)
    __initial_sp                             0x200023b0   Data           0  startup_xcm32l.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000050c1

  Load Region LR_IROM1 (Base: 0x00005000, Size: 0x00008968, Max: 0x0003b000, ABSOLUTE, COMPRESSED[0x0000882c])

    Execution Region ER_IROM1 (Base: 0x00005000, Size: 0x000087ec, Max: 0x0003b000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00005000   0x000000c0   Data   RO            3    RESET               startup_xcm32l.o
    0x000050c0   0x00000000   Code   RO         3262  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000050c0   0x00000004   Code   RO         3643    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000050c4   0x00000004   Code   RO         3646    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000050c8   0x00000000   Code   RO         3648    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000050c8   0x00000000   Code   RO         3650    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000050c8   0x00000008   Code   RO         3651    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000050d0   0x00000000   Code   RO         3653    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000050d0   0x00000000   Code   RO         3655    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000050d0   0x00000004   Code   RO         3644    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000050d4   0x0000001c   Code   RO            4    .text               startup_xcm32l.o
    0x000050f0   0x00000028   Code   RO         3275    .text               mc_p.l(idiv.o)
    0x00005118   0x00000060   Code   RO         3277    .text               mc_p.l(uldiv.o)
    0x00005178   0x0000004c   Code   RO         3279    .text               mc_p.l(ldiv.o)
    0x000051c4   0x0000000c   Code   RO         3281    .text               mc_p.l(tolower.o)
    0x000051d0   0x00000024   Code   RO         3283    .text               mc_p.l(memcpya.o)
    0x000051f4   0x00000024   Code   RO         3285    .text               mc_p.l(memseta.o)
    0x00005218   0x00000014   Code   RO         3287    .text               mc_p.l(strchr.o)
    0x0000522c   0x0000000e   Code   RO         3289    .text               mc_p.l(strlen.o)
    0x0000523a   0x0000001c   Code   RO         3291    .text               mc_p.l(strcmp.o)
    0x00005256   0x00000012   Code   RO         3293    .text               mc_p.l(strcpy.o)
    0x00005268   0x0000001e   Code   RO         3295    .text               mc_p.l(strncmp.o)
    0x00005286   0x0000007c   Code   RO         3588    .text               mf_p.l(fdiv.o)
    0x00005302   0x00000002   PAD
    0x00005304   0x00000164   Code   RO         3590    .text               mf_p.l(dadd.o)
    0x00005468   0x000000d0   Code   RO         3592    .text               mf_p.l(dmul.o)
    0x00005538   0x000000f0   Code   RO         3594    .text               mf_p.l(ddiv.o)
    0x00005628   0x0000000e   Code   RO         3596    .text               mf_p.l(ffltui.o)
    0x00005636   0x00000002   PAD
    0x00005638   0x00000028   Code   RO         3598    .text               mf_p.l(dflti.o)
    0x00005660   0x0000001c   Code   RO         3600    .text               mf_p.l(dfltui.o)
    0x0000567c   0x00000028   Code   RO         3602    .text               mf_p.l(ffixui.o)
    0x000056a4   0x00000048   Code   RO         3604    .text               mf_p.l(dfixi.o)
    0x000056ec   0x0000003c   Code   RO         3606    .text               mf_p.l(dfixui.o)
    0x00005728   0x00000028   Code   RO         3608    .text               mf_p.l(f2d.o)
    0x00005750   0x00000028   Code   RO         3610    .text               mf_p.l(cdcmple.o)
    0x00005778   0x00000028   Code   RO         3612    .text               mf_p.l(cdrcmple.o)
    0x000057a0   0x00000038   Code   RO         3614    .text               mf_p.l(d2f.o)
    0x000057d8   0x0000002c   Code   RO         3669    .text               mc_p.l(uidiv.o)
    0x00005804   0x00000020   Code   RO         3671    .text               mc_p.l(llshl.o)
    0x00005824   0x00000022   Code   RO         3673    .text               mc_p.l(llushr.o)
    0x00005846   0x00000026   Code   RO         3675    .text               mc_p.l(llsshr.o)
    0x0000586c   0x00000000   Code   RO         3686    .text               mc_p.l(iusefp.o)
    0x0000586c   0x00000082   Code   RO         3687    .text               mf_p.l(fepilogue.o)
    0x000058ee   0x000000be   Code   RO         3689    .text               mf_p.l(depilogue.o)
    0x000059ac   0x0000002c   Code   RO         3693    .text               mf_p.l(dscalb.o)
    0x000059d8   0x00000040   Code   RO         3695    .text               mf_p.l(dfixul.o)
    0x00005a18   0x00000024   Code   RO         3697    .text               mc_p.l(init.o)
    0x00005a3c   0x000000a2   Code   RO         3701    .text               mf_p.l(dsqrt.o)
    0x00005ade   0x00000056   Code   RO         3711    .text               mc_p.l(__dczerorl2.o)
    0x00005b34   0x00000044   Code   RO          754    i.Booster_Init      main.o
    0x00005b78   0x00000020   Code   RO         1344    i.CMU_APBPeriph0ClockCmd  xcm32lxx_cmu.o
    0x00005b98   0x00000020   Code   RO         1345    i.CMU_APBPeriph1ClockCmd  xcm32lxx_cmu.o
    0x00005bb8   0x00000034   Code   RO         1351    i.CMU_HCLKConfig    xcm32lxx_cmu.o
    0x00005bec   0x00000080   Code   RO         1353    i.CMU_HSIConfig     xcm32lxx_cmu.o
    0x00005c6c   0x00000060   Code   RO         1357    i.CMU_LSEConfig     xcm32lxx_cmu.o
    0x00005ccc   0x00000034   Code   RO         1360    i.CMU_PCLKConfig    xcm32lxx_cmu.o
    0x00005d00   0x0000002c   Code   RO         1362    i.CMU_RTCCLKConfig  xcm32lxx_cmu.o
    0x00005d2c   0x00000030   Code   RO         1363    i.CMU_SysClkConfig  xcm32lxx_cmu.o
    0x00005d5c   0x00000090   Code   RO         1365    i.CMU_WaitForSysClkStartUp  xcm32lxx_cmu.o
    0x00005dec   0x0000001e   Code   RO          755    i.DelayMs           main.o
    0x00005e0a   0x00000002   PAD
    0x00005e0c   0x00000024   Code   RO          756    i.Delay_250ms       main.o
    0x00005e30   0x0000006a   Code   RO          854    i.GetRtcSecond      rtc.o
    0x00005e9a   0x0000004c   Code   RO          649    i.I2C1_ReceiveData  i2cuart.o
    0x00005ee6   0x00000042   Code   RO          650    i.I2C1_TransmitData  i2cuart.o
    0x00005f28   0x0000003c   Code   RO          651    i.I2C_Start         i2cuart.o
    0x00005f64   0x0000003c   Code   RO          652    i.I2C_Stop          i2cuart.o
    0x00005fa0   0x00000024   Code   RO          653    i.I2C_init          i2cuart.o
    0x00005fc4   0x0000007c   Code   RO          654    i.I2C_read_byte     i2cuart.o
    0x00006040   0x00000084   Code   RO          655    i.I2C_send_byte     i2cuart.o
    0x000060c4   0x0000003c   Code   RO          855    i.Is_Leap_Year      rtc.o
    0x00006100   0x0000006c   Code   RO          656    i.Master_ACK        i2cuart.o
    0x0000616c   0x00000014   Code   RO          757    i.NVIC_ClearPendingIRQ  main.o
    0x00006180   0x00000014   Code   RO          963    i.NVIC_ClearPendingIRQ  uart.o
    0x00006194   0x00000014   Code   RO          758    i.NVIC_DisableIRQ   main.o
    0x000061a8   0x00000014   Code   RO          964    i.NVIC_DisableIRQ   uart.o
    0x000061bc   0x00000014   Code   RO          759    i.NVIC_EnableIRQ    main.o
    0x000061d0   0x00000014   Code   RO          965    i.NVIC_EnableIRQ    uart.o
    0x000061e4   0x0000003c   Code   RO          856    i.NVIC_Init_rtc     rtc.o
    0x00006220   0x00000078   Code   RO          760    i.NVIC_SetPriority  main.o
    0x00006298   0x00000078   Code   RO          857    i.NVIC_SetPriority  rtc.o
    0x00006310   0x00000078   Code   RO          966    i.NVIC_SetPriority  uart.o
    0x00006388   0x0000002c   Code   RO         2320    i.PORT_DeInit       xcm32lxx_port.o
    0x000063b4   0x0000001c   Code   RO          657    i.PORT_DirSet       i2cuart.o
    0x000063d0   0x00000070   Code   RO         2326    i.PORT_Init         xcm32lxx_port.o
    0x00006440   0x0000004a   Code   RO         2327    i.PORT_PinAFConfig  xcm32lxx_port.o
    0x0000648a   0x00000012   Code   RO         2329    i.PORT_ReadInputDataBit  xcm32lxx_port.o
    0x0000649c   0x0000001c   Code   RO         2334    i.PORT_StructInit   xcm32lxx_port.o
    0x000064b8   0x00000040   Code   RO         2337    i.PORT_WriteBit     xcm32lxx_port.o
    0x000064f8   0x00000014   Code   RO         2655    i.RTC_ClearITFlag   xcm32lxx_rtc.o
    0x0000650c   0x00000024   Code   RO         2656    i.RTC_Cmd           xcm32lxx_rtc.o
    0x00006530   0x0000002c   Code   RO         2657    i.RTC_DeInit        xcm32lxx_rtc.o
    0x0000655c   0x00000010   Code   RO         2658    i.RTC_GetDay        xcm32lxx_rtc.o
    0x0000656c   0x00000010   Code   RO         2659    i.RTC_GetHour       xcm32lxx_rtc.o
    0x0000657c   0x00000014   Code   RO         2660    i.RTC_GetITStatus   xcm32lxx_rtc.o
    0x00006590   0x00000010   Code   RO         2661    i.RTC_GetMinute     xcm32lxx_rtc.o
    0x000065a0   0x00000010   Code   RO         2662    i.RTC_GetMonth      xcm32lxx_rtc.o
    0x000065b0   0x00000010   Code   RO         2663    i.RTC_GetSecond     xcm32lxx_rtc.o
    0x000065c0   0x00000068   Code   RO          858    i.RTC_GetWeek       rtc.o
    0x00006628   0x00000014   Code   RO         2665    i.RTC_GetYear       xcm32lxx_rtc.o
    0x0000663c   0x0000000c   Code   RO         2667    i.RTC_GetYearLSB    xcm32lxx_rtc.o
    0x00006648   0x00000010   Code   RO          859    i.RTC_IRQHandler    rtc.o
    0x00006658   0x00000080   Code   RO         2668    i.RTC_ITConfig      xcm32lxx_rtc.o
    0x000066d8   0x000000e4   Code   RO         2669    i.RTC_Init          xcm32lxx_rtc.o
    0x000067bc   0x00000054   Code   RO          860    i.RTC_Run_Init      rtc.o
    0x00006810   0x00000022   Code   RO         2671    i.RTC_StructInit    xcm32lxx_rtc.o
    0x00006832   0x00000002   PAD
    0x00006834   0x000000e0   Code   RO          862    i.RTC_To_Sec        rtc.o
    0x00006914   0x00000002   Code   RO           12    i.SetSysClock       system_xcm32l.o
    0x00006916   0x00000002   PAD
    0x00006918   0x0000004c   Code   RO          761    i.SysTickConfigure  main.o
    0x00006964   0x00000044   Code   RO         2877    i.SysTick_CLKSourceConfig  xcm32lxx_systick.o
    0x000069a8   0x00000024   Code   RO         2879    i.SysTick_Cmd       xcm32lxx_systick.o
    0x000069cc   0x00000024   Code   RO          762    i.SysTick_Handler   main.o
    0x000069f0   0x00000024   Code   RO         2883    i.SysTick_ITConfig  xcm32lxx_systick.o
    0x00006a14   0x00000010   Code   RO         2885    i.SysTick_SetReloadValue  xcm32lxx_systick.o
    0x00006a24   0x00000100   Code   RO           14    i.SystemInit        system_xcm32l.o
    0x00006b24   0x0000006c   Code   RO          658    i.Test_ACK          i2cuart.o
    0x00006b90   0x0000005c   Code   RO          968    i.UART1_IRQHandler  uart.o
    0x00006bec   0x000000e4   Code   RO          969    i.UART1_Init        uart.o
    0x00006cd0   0x000000bc   Code   RO          970    i.UART2_IRQHandler  uart.o
    0x00006d8c   0x00000108   Code   RO          971    i.UART2_Init        uart.o
    0x00006e94   0x0000003c   Code   RO          972    i.UART4_IRQHandler  uart.o
    0x00006ed0   0x000000ec   Code   RO          973    i.UART4_Init        uart.o
    0x00006fbc   0x0000006c   Code   RO         3037    i.UART_DeInit       xcm32lxx_uart.o
    0x00007028   0x00000054   Code   RO         3038    i.UART_FIFOModeConfig  xcm32lxx_uart.o
    0x0000707c   0x00000014   Code   RO         3039    i.UART_GetITStatus  xcm32lxx_uart.o
    0x00007090   0x00000012   Code   RO         3040    i.UART_GetLineStatus  xcm32lxx_uart.o
    0x000070a2   0x00000014   Code   RO         3042    i.UART_ITConfig     xcm32lxx_uart.o
    0x000070b6   0x00000002   PAD
    0x000070b8   0x000002bc   Code   RO         3043    i.UART_Init         xcm32lxx_uart.o
    0x00007374   0x00000018   Code   RO         3044    i.UART_PTXREModeConfig  xcm32lxx_uart.o
    0x0000738c   0x00000008   Code   RO         3045    i.UART_ReceiveData  xcm32lxx_uart.o
    0x00007394   0x00000006   Code   RO         3046    i.UART_SendData     xcm32lxx_uart.o
    0x0000739a   0x00000018   Code   RO         3047    i.UART_StructInit   xcm32lxx_uart.o
    0x000073b2   0x00000002   PAD
    0x000073b4   0x00000010   Code   RO         3188    i.WDT_ClearITFlag   xcm32lxx_wdt.o
    0x000073c4   0x00000014   Code   RO         3192    i.WDT_GetITStatus   xcm32lxx_wdt.o
    0x000073d8   0x00000016   Code   RO          764    i.WDT_IRQHandler    main.o
    0x000073ee   0x00000002   PAD
    0x000073f0   0x00000024   Code   RO         3194    i.WDT_RestartCmd    xcm32lxx_wdt.o
    0x00007414   0x00000020   Code   RO         3532    i.__0printf         mc_p.l(printfa.o)
    0x00007434   0x0000002c   Code   RO         3533    i.__0snprintf       mc_p.l(printfa.o)
    0x00007460   0x00000028   Code   RO         3534    i.__0sprintf        mc_p.l(printfa.o)
    0x00007488   0x0000002e   Code   RO         3691    i.__ARM_clz         mf_p.l(depilogue.o)
    0x000074b6   0x0000001c   Code   RO          438    i.__ARM_common_switch8  cjson.o
    0x000074d2   0x00000002   PAD
    0x000074d4   0x0000002c   Code   RO         3630    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00007500   0x000000ac   Code   RO         3632    i.__kernel_poly     m_ps.l(poly.o)
    0x000075ac   0x00000014   Code   RO         3616    i.__mathlib_dbl_divzero  m_ps.l(dunder.o)
    0x000075c0   0x00000008   Code   RO         3618    i.__mathlib_dbl_infnan2  m_ps.l(dunder.o)
    0x000075c8   0x00000010   Code   RO         3619    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x000075d8   0x00000014   Code   RO         3620    i.__mathlib_dbl_overflow  m_ps.l(dunder.o)
    0x000075ec   0x00000014   Code   RO         3622    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x00007600   0x0000000e   Code   RO         3705    i.__scatterload_copy  mc_p.l(handlers.o)
    0x0000760e   0x00000002   Code   RO         3706    i.__scatterload_null  mc_p.l(handlers.o)
    0x00007610   0x0000000e   Code   RO         3707    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000761e   0x00000002   PAD
    0x00007620   0x0000000c   Code   RO         3681    i.__set_errno       mc_p.l(errno.o)
    0x0000762c   0x00000158   Code   RO         3539    i._fp_digits        mc_p.l(printfa.o)
    0x00007784   0x000006ec   Code   RO         3540    i._printf_core      mc_p.l(printfa.o)
    0x00007e70   0x00000020   Code   RO         3541    i._printf_post_padding  mc_p.l(printfa.o)
    0x00007e90   0x0000002c   Code   RO         3542    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00007ebc   0x00000016   Code   RO         3543    i._snputc           mc_p.l(printfa.o)
    0x00007ed2   0x0000000a   Code   RO         3544    i._sputc            mc_p.l(printfa.o)
    0x00007edc   0x00000030   Code   RO          107    i.cJSON_AddItemToArray  cjson.o
    0x00007f0c   0x00000034   Code   RO          108    i.cJSON_AddItemToObject  cjson.o
    0x00007f40   0x00000026   Code   RO          117    i.cJSON_CreateNumber  cjson.o
    0x00007f66   0x00000014   Code   RO          118    i.cJSON_CreateObject  cjson.o
    0x00007f7a   0x0000001e   Code   RO          119    i.cJSON_CreateString  cjson.o
    0x00007f98   0x00000068   Code   RO          122    i.cJSON_Delete      cjson.o
    0x00008000   0x00000020   Code   RO          131    i.cJSON_GetObjectItem  cjson.o
    0x00008020   0x00000020   Code   RO          135    i.cJSON_New_Item    cjson.o
    0x00008040   0x00000010   Code   RO          136    i.cJSON_Parse       cjson.o
    0x00008050   0x0000006c   Code   RO          137    i.cJSON_ParseWithOpts  cjson.o
    0x000080bc   0x00000012   Code   RO          138    i.cJSON_Print       cjson.o
    0x000080ce   0x00000050   Code   RO          143    i.cJSON_strcasecmp  cjson.o
    0x0000811e   0x00000002   PAD
    0x00008120   0x0000002c   Code   RO          144    i.cJSON_strdup      cjson.o
    0x0000814c   0x00001a64   Code   RO          471    i.cJsonParseProtocol  protol.o
    0x00009bb0   0x000000d8   Code   RO          576    i.crc32             crc32.o
    0x00009c88   0x00000016   Code   RO          659    i.delay             i2cuart.o
    0x00009c9e   0x0000001a   Code   RO          660    i.delay_5us         i2cuart.o
    0x00009cb8   0x00000074   Code   RO          146    i.ensure            cjson.o
    0x00009d2c   0x00000094   Code   RO         1161    i.enter_stop_mode   user_main.o
    0x00009dc0   0x000000c8   Code   RO         3249    i.floor             m_ps.l(floor.o)
    0x00009e88   0x00000024   Code   RO          765    i.fputc             main.o
    0x00009eac   0x00000054   Code   RO         3560    i.free              mc_p.l(malloc.o)
    0x00009f00   0x000000d8   Code   RO          661    i.get_voltage       i2cuart.o
    0x00009fd8   0x00000180   Code   RO          766    i.main              main.o
    0x0000a158   0x0000006c   Code   RO         3561    i.malloc            mc_p.l(malloc.o)
    0x0000a1c4   0x00000094   Code   RO          975    i.pack_data         uart.o
    0x0000a258   0x000000a4   Code   RO          976    i.pack_luna_data    uart.o
    0x0000a2fc   0x000000a8   Code   RO          147    i.parse_array       cjson.o
    0x0000a3a4   0x00000114   Code   RO          148    i.parse_hex4        cjson.o
    0x0000a4b8   0x0000018c   Code   RO          149    i.parse_number      cjson.o
    0x0000a644   0x00000110   Code   RO          150    i.parse_object      cjson.o
    0x0000a754   0x000001ec   Code   RO          151    i.parse_string      cjson.o
    0x0000a940   0x000000c0   Code   RO          152    i.parse_value       cjson.o
    0x0000aa00   0x000009fc   Code   RO         3253    i.pow               m_ps.l(pow.o)
    0x0000b3fc   0x0000001c   Code   RO          153    i.pow2gt            cjson.o
    0x0000b418   0x00000260   Code   RO          154    i.print_array       cjson.o
    0x0000b678   0x000001a8   Code   RO          155    i.print_number      cjson.o
    0x0000b820   0x00000488   Code   RO          156    i.print_object      cjson.o
    0x0000bca8   0x00000010   Code   RO          157    i.print_string      cjson.o
    0x0000bcb8   0x000001d8   Code   RO          158    i.print_string_ptr  cjson.o
    0x0000be90   0x00000134   Code   RO          159    i.print_value       cjson.o
    0x0000bfc4   0x00000016   Code   RO          160    i.skip              cjson.o
    0x0000bfda   0x00000002   PAD
    0x0000bfdc   0x00000048   Code   RO         3636    i.sqrt              m_ps.l(sqrt.o)
    0x0000c024   0x00000006   Code   RO          161    i.suffix_object     cjson.o
    0x0000c02a   0x0000004c   Code   RO          863    i.t_BCD2HEX         rtc.o
    0x0000c076   0x00000002   PAD
    0x0000c078   0x0000031c   Code   RO          978    i.uart1_rx_mengmu_process  uart.o
    0x0000c394   0x0000005c   Code   RO          979    i.uart2_com_tx      uart.o
    0x0000c3f0   0x00000034   Code   RO          980    i.uart2_com_tx_data  uart.o
    0x0000c424   0x00000144   Code   RO          981    i.uart2_find_head_and_tail  uart.o
    0x0000c568   0x00000014   Code   RO          982    i.uart2_get_tx_buffer_size  uart.o
    0x0000c57c   0x00000200   Code   RO          983    i.uart2_rx_process  uart.o
    0x0000c77c   0x0000004c   Code   RO          984    i.uart2_tx_buffer_in  uart.o
    0x0000c7c8   0x00000050   Code   RO          985    i.uart2_tx_buffer_out  uart.o
    0x0000c818   0x00000090   Code   RO          986    i.uart2_unpack_data  uart.o
    0x0000c8a8   0x00000024   Code   RO          865    i.uf_GPIO_CMU_Init  rtc.o
    0x0000c8cc   0x000000e4   Code   RO          624    i.uf_GPIO_Init      gpio.o
    0x0000c9b0   0x00000040   Code   RO          866    i.uf_GPIO_RTC_Init  rtc.o
    0x0000c9f0   0x00000098   Code   RO          868    i.uf_RTC_Init       rtc.o
    0x0000ca88   0x00000024   Code   RO          162    i.update            cjson.o
    0x0000caac   0x00000850   Code   RO         1162    i.user_main         user_main.o
    0x0000d2fc   0x00000007   Data   RO          163    .constdata          cjson.o
    0x0000d303   0x00000001   PAD
    0x0000d304   0x00000400   Data   RO          577    .constdata          crc32.o
    0x0000d704   0x0000000c   Data   RO          869    .constdata          rtc.o
    0x0000d710   0x00000088   Data   RO         3254    .constdata          m_ps.l(pow.o)
    0x0000d798   0x00000008   Data   RO         3634    .constdata          m_ps.l(qnan.o)
    0x0000d7a0   0x0000002b   Data   RO         1164    .conststring        user_main.o
    0x0000d7cb   0x00000001   PAD
    0x0000d7cc   0x00000020   Data   RO         3703    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x000023b0, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x00000040])

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0000000c   Data   RW          164    .data               cjson.o
    0x2000000c   0x00000004   PAD
    0x20000010   0x00000038   Data   RW          482    .data               protol.o
    0x20000048   0x00000028   Data   RW          664    .data               i2cuart.o
    0x20000070   0x00000004   Data   RW          767    .data               main.o
    0x20000074   0x00000014   Data   RW          870    .data               rtc.o
    0x20000088   0x00000014   Data   RW          993    .data               uart.o
    0x2000009c   0x00000004   PAD
    0x200000a0   0x000000b0   Data   RW         1165    .data               user_main.o
    0x20000150   0x0000001c   Data   RW         2338    .data               xcm32lxx_port.o
    0x2000016c   0x00000004   Data   RW         3668    .data               mc_p.l(stdout.o)
    0x20000170   0x00000004   Data   RW         3677    .data               mc_p.l(mvars.o)
    0x20000174   0x00000004   Data   RW         3678    .data               mc_p.l(mvars.o)
    0x20000178   0x00000004   Data   RW         3682    .data               mc_p.l(errno.o)
    0x2000017c   0x00000004   PAD
    0x20000180   0x0000114c   Zero   RW          992    .bss                uart.o
    0x200012cc   0x00000004   PAD
    0x200012d0   0x000000e0   Zero   RW         1163    .bss                user_main.o
    0x200013b0   0x00000c00   Zero   RW            2    HEAP                startup_xcm32l.o
    0x20001fb0   0x00000400   Zero   RW            1    STACK               startup_xcm32l.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      5642        256          7         12          0      24261   cjson.o
       216          6       1024          0          0       1192   crc32.o
       228         10          0          0          0        507   gpio.o
      1062         90          0         40          0       7504   i2cuart.o
       868        186          0          4          0      10930   main.o
      6756       1006          0         56          0      12905   protol.o
      1102         98         12         20          0      12645   rtc.o
        28          8        192          0       4096        616   startup_xcm32l.o
       258         20          0          0          0      82681   system_xcm32l.o
      3656        238          0         20       4428      17136   uart.o
      2276        476         43        176        224       4618   user_main.o
       628         42          0          0          0       5269   xcm32lxx_cmu.o
       340         22          0         28          0       3731   xcm32lxx_port.o
       622         68          0          0          0       6455   xcm32lxx_rtc.o
       156         20          0          0          0       1856   xcm32lxx_systick.o
      1012         40          0          0          0       6064   xcm32lxx_uart.o
        72         20          0          0          0       1581   xcm32lxx_wdt.o

    ----------------------------------------------------------------------
     24942       <USER>       <GROUP>        364       8752     199951   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          2          8          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        84         14          0          0          0        340   dunder.o
       200         20          0          0          0         76   floor.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
      2556        196        136          0          0        240   pow.o
         0          0          8          0          0          0   qnan.o
        72          6          0          0          0         76   sqrt.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         60   errno.o
        30          0          0          0          0          0   handlers.o
        40          0          0          0          0         72   idiv.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        76          0          0          0          0         76   ldiv.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
       192         20          0          0          0        144   malloc.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
         0          0          0          8          0          0   mvars.o
      2340        108          0          0          0        700   printfa.o
         0          0          0          4          0          0   stdout.o
        20          0          0          0          0         60   strchr.o
        28          0          0          0          0         68   strcmp.o
        18          0          0          0          0         60   strcpy.o
        14          0          0          0          0         60   strlen.o
        30          0          0          0          0         72   strncmp.o
        12          0          0          0          0         60   tolower.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdcmple.o
        40          2          0          0          0         68   cdrcmple.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        60         10          0          0          0         68   dfixui.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
        28          4          0          0          0         68   dfltui.o
       208          6          0          0          0         88   dmul.o
        44          0          0          0          0         72   dscalb.o
       162          0          0          0          0         80   dsqrt.o
        40          0          0          0          0         60   f2d.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        40          0          0          0          0         60   ffixui.o
        14          0          0          0          0         68   ffltui.o

    ----------------------------------------------------------------------
      8398        <USER>        <GROUP>         16          4       4520   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3128        240        144          0          0        868   m_ps.l
      3270        150          0         16          0       2020   mc_p.l
      1994         60          0          0          0       1632   mf_p.l

    ----------------------------------------------------------------------
      8398        <USER>        <GROUP>         16          4       4520   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     33340       3056       1456        380       8756     196187   Grand Totals
     33340       3056       1456         64       8756     196187   ELF Image Totals (compressed)
     33340       3056       1456         64          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                34796 (  33.98kB)
    Total RW  Size (RW Data + ZI Data)              9136 (   8.92kB)
    Total ROM Size (Code + RO Data + RW Data)      34860 (  34.04kB)

==============================================================================

