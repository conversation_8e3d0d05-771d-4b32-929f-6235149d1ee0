<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\xcm32l_app.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\xcm32l_app.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060061: Last Updated: Fri Jun 24 18:19:44 2022
<BR><P>
<H3>Maximum Stack Usage =        600 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; user_main &rArr; cJsonParseProtocol &rArr; cJSON_Parse &rArr; cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[7]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">NMI_Handler</a><BR>
 <LI><a href="#[8]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">HardFault_Handler</a><BR>
 <LI><a href="#[9]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SVC_Handler</a><BR>
 <LI><a href="#[a]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">PendSV_Handler</a><BR>
 <LI><a href="#[13]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[13]">ADC_IRQHandler</a><BR>
 <LI><a href="#[10b]">print_object</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e2]">print_value</a><BR>
 <LI><a href="#[104]">print_array</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e2]">print_value</a><BR>
 <LI><a href="#[da]">cJSON_Delete</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[da]">cJSON_Delete</a><BR>
 <LI><a href="#[fe]">parse_object</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e0]">parse_value</a><BR>
 <LI><a href="#[fb]">parse_array</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e0]">parse_value</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[13]">ADC_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[17]">BASETIMER_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[c]">DMAC_IRQHandler</a> from uart.o(i.DMAC_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[8]">HardFault_Handler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[11]">I2C1_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[26]">I2C2_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[2a]">LCD_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[15]">LVD_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[7]">NMI_Handler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[d]">P0_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[18]">P1P2_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[19]">P3P4_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1a]">P5P6_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[27]">PCA12_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[28]">PCA34_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[12]">RAM_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[16]">RTC_IRQHandler</a> from rtc.o(i.RTC_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[6]">Reset_Handler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[2b]">SCI7816_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[10]">SPI1_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[23]">SPI2_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[24]">SPI3_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[25]">SPI4_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from main.o(i.SysTick_Handler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[2d]">SystemInit</a> from system_xcm32l.o(i.SystemInit) referenced from startup_xcm32l.o(.text)
 <LI><a href="#[e]">TIMER1_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1b]">TIMER2_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1c]">TIMER3_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1d]">TIMER4_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[f]">UART1_IRQHandler</a> from uart.o(i.UART1_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1e]">UART2_IRQHandler</a> from uart.o(i.UART2_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[1f]">UART3_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[20]">UART4_IRQHandler</a> from uart.o(i.UART4_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[21]">UART5_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[22]">UART6_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[14]">VC_IRQHandler</a> from startup_xcm32l.o(.text) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[29]">WDT_IRQHandler</a> from main.o(i.WDT_IRQHandler) referenced from startup_xcm32l.o(RESET)
 <LI><a href="#[2e]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_xcm32l.o(.text)
 <LI><a href="#[30]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[31]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[2f]">fputc</a> from main.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[5]">free</a> from malloc.o(i.free) referenced 2 times from cjson.o(.data)
 <LI><a href="#[2c]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[4]">malloc</a> from malloc.o(i.malloc) referenced 2 times from cjson.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(.text)
</UL>
<P><STRONG><a name="[11f]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[32]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[50]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[120]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[121]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[122]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[123]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[124]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[6]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>BASETIMER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>I2C2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>LCD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>P0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>P1P2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>P3P4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>P5P6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>PCA12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>PCA34_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RAM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>SCI7816_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>VC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_xcm32l.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[125]"></a>__aeabi_idiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, idiv.o(.text), UNUSED)

<P><STRONG><a name="[34]"></a>__aeabi_idivmod</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, idiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;t_HEX2BCD
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Is_Leap_Year
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_rx_mengmu_process
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_ldivmod</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, ldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = __aeabi_ldivmod &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_rx_process
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_find_head_and_tail
</UL>

<P><STRONG><a name="[e3]"></a>tolower</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tolower.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strcasecmp
</UL>

<P><STRONG><a name="[11d]"></a>abs</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, siabs.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[e5]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pack_data
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strdup
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_unpack_data
</UL>

<P><STRONG><a name="[126]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[127]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[128]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[129]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3a]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[12a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3c]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[10e]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
</UL>

<P><STRONG><a name="[e4]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strdup
</UL>

<P><STRONG><a name="[e7]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[105]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
</UL>

<P><STRONG><a name="[ed]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;floor
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>

<P><STRONG><a name="[43]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[44]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;floor
</UL>

<P><STRONG><a name="[45]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>

<P><STRONG><a name="[46]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>

<P><STRONG><a name="[47]"></a>__aeabi_ui2f</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ffltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_ui2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[49]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[4a]"></a>__aeabi_ui2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
</UL>

<P><STRONG><a name="[b7]"></a>__aeabi_f2uiz</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ffixui.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[4b]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __aeabi_d2iz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateNumber
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
</UL>

<P><STRONG><a name="[4c]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2uiz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[b6]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[108]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>

<P><STRONG><a name="[109]"></a>__aeabi_cdcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>

<P><STRONG><a name="[cf]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;floor
</UL>

<P><STRONG><a name="[4d]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
</UL>

<P><STRONG><a name="[12b]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[35]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
</UL>

<P><STRONG><a name="[38]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[12c]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[37]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[12d]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[40]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_lasr
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[12e]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[12f]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[3e]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>

<P><STRONG><a name="[48]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
</UL>

<P><STRONG><a name="[42]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[41]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[cc]"></a>__ARM_scalbn</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
</UL>

<P><STRONG><a name="[130]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[4f]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[33]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[131]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[51]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[132]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[133]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[52]"></a>Booster_DeInit</STRONG> (Thumb, 208 bytes, Stack size 16 bytes, main.o(i.Booster_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Booster_DeInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_StructInit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MengMuRadarReceive
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[58]"></a>Booster_Init</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, main.o(i.Booster_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Booster_Init &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_StructInit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[59]"></a>Booster_Start</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, main.o(i.Booster_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Booster_Start &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_StructInit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5a]"></a>Booster_Stop</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, main.o(i.Booster_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Booster_Stop &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_StructInit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
</UL>

<P><STRONG><a name="[9b]"></a>CMU_APBPeriph0ClockCmd</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[a5]"></a>CMU_APBPeriph1ClockCmd</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_RTC_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_CMU_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[11c]"></a>CMU_GetSysClkFreq</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, xcm32lxx_cmu.o(i.CMU_GetSysClkFreq))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[11b]"></a>CMU_GetSysClkSource</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, xcm32lxx_cmu.o(i.CMU_GetSysClkSource))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[f6]"></a>CMU_HCLKConfig</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_HCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_HCLKConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f4]"></a>CMU_HSIConfig</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_HSIConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_HSIConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[119]"></a>CMU_LSEConfig</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, xcm32lxx_cmu.o(i.CMU_LSEConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = CMU_LSEConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[b9]"></a>CMU_LSIConfig</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_LSIConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_LSIConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[f7]"></a>CMU_PCLKConfig</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_PCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_PCLKConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11a]"></a>CMU_RTCCLKConfig</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_RTCCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_RTCCLKConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[f5]"></a>CMU_SysClkConfig</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_SysClkConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_SysClkConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bb]"></a>CMU_WDTCLKConfig</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_WDTCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_WDTCLKConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[ba]"></a>CMU_WaitForSysClkStartUp</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMU_WaitForSysClkStartUp
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[c]"></a>DMAC_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, uart.o(i.DMAC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMAC_IRQHandler &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_ToggleBit
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetITStatus
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearITFlag
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DMA_ClearITFlag</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, xcm32lxx_dma.o(i.DMA_ClearITFlag))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_ClearITFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
</UL>

<P><STRONG><a name="[5e]"></a>DMA_Cmd</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, xcm32lxx_dma.o(i.DMA_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[114]"></a>DMA_DeInit</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, xcm32lxx_dma.o(i.DMA_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
</UL>

<P><STRONG><a name="[5b]"></a>DMA_GetITStatus</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, xcm32lxx_dma.o(i.DMA_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>DMA_ITConfig</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, xcm32lxx_dma.o(i.DMA_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
</UL>

<P><STRONG><a name="[116]"></a>DMA_Init</STRONG> (Thumb, 696 bytes, Stack size 12 bytes, xcm32lxx_dma.o(i.DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
</UL>

<P><STRONG><a name="[115]"></a>DMA_StructInit</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, xcm32lxx_dma.o(i.DMA_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
</UL>

<P><STRONG><a name="[82]"></a>DelayMs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, main.o(i.DelayMs))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[56]"></a>Delay_ms</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, main.o(i.Delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;do_production_test_proc
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOn
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
</UL>

<P><STRONG><a name="[60]"></a>GetRtcSecond</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, rtc.o(i.GetRtcSecond))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = GetRtcSecond &rArr; RTC_To_Sec &rArr; Is_Leap_Year &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_To_Sec
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetYear
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetSecond
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetMonth
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetMinute
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetHour
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetDay
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;t_BCD2HEX
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5f]"></a>GetSysTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(i.GetSysTick))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_com_tx_data
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOn
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
</UL>

<P><STRONG><a name="[69]"></a>I2C1_ReceiveData</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, i2cuart.o(i.I2C1_ReceiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C1_ReceiveData &rArr; Test_ACK &rArr; I2C_Stop &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Master_ACK
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_send_byte
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_read_byte
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
</UL>

<P><STRONG><a name="[70]"></a>I2C1_TransmitData</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, i2cuart.o(i.I2C1_TransmitData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C1_TransmitData &rArr; Test_ACK &rArr; I2C_Stop &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_send_byte
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
</UL>

<P><STRONG><a name="[6a]"></a>I2C_Start</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, i2cuart.o(i.I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_Start &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_TransmitData
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[6f]"></a>I2C_Stop</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, i2cuart.o(i.I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_Stop &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_TransmitData
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[73]"></a>I2C_init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, i2cuart.o(i.I2C_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_init &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
</UL>

<P><STRONG><a name="[6e]"></a>I2C_read_byte</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, i2cuart.o(i.I2C_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_read_byte &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_ReadInputDataBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[6b]"></a>I2C_send_byte</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, i2cuart.o(i.I2C_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_send_byte &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_TransmitData
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[75]"></a>Is_Leap_Year</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, rtc.o(i.Is_Leap_Year))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Is_Leap_Year &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_To_Sec
</UL>

<P><STRONG><a name="[6d]"></a>Master_ACK</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, i2cuart.o(i.Master_ACK))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Master_ACK &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[76]"></a>MengMuRadarReceive</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, user_main.o(i.MengMuRadarReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = MengMuRadarReceive &rArr; uart1_rx_mengmu_process &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_rx_mengmu_process
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[11e]"></a>PMU_EnterDeepSleep3Mode</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, xcm32lxx_pmu.o(i.PMU_EnterDeepSleep3Mode))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[118]"></a>PORT_DeInit</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, xcm32lxx_port.o(i.PORT_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
</UL>

<P><STRONG><a name="[71]"></a>PORT_DirSet</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, i2cuart.o(i.PORT_DirSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PORT_DirSet
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Master_ACK
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_send_byte
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_read_byte
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[54]"></a>PORT_Init</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, xcm32lxx_port.o(i.PORT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PORT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_RTC_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_CMU_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
</UL>

<P><STRONG><a name="[a6]"></a>PORT_PinAFConfig</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, xcm32lxx_port.o(i.PORT_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PORT_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_RTC_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[74]"></a>PORT_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, xcm32lxx_port.o(i.PORT_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_read_byte
</UL>

<P><STRONG><a name="[53]"></a>PORT_StructInit</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, xcm32lxx_port.o(i.PORT_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
</UL>

<P><STRONG><a name="[5d]"></a>PORT_ToggleBit</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, xcm32lxx_port.o(i.PORT_ToggleBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PORT_ToggleBit
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
</UL>

<P><STRONG><a name="[55]"></a>PORT_WriteBit</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, xcm32lxx_port.o(i.PORT_WriteBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PORT_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;do_production_test_proc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_On
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOn
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_Off
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_Off
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Master_ACK
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_send_byte
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_read_byte
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[7c]"></a>RTC_ClearITFlag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_ClearITFlag))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_IRQHandler
</UL>

<P><STRONG><a name="[81]"></a>RTC_Cmd</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[84]"></a>RTC_DeInit</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[63]"></a>RTC_GetDay</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetDay))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[64]"></a>RTC_GetHour</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetHour))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
</UL>

<P><STRONG><a name="[7b]"></a>RTC_GetITStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_IRQHandler
</UL>

<P><STRONG><a name="[65]"></a>RTC_GetMinute</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetMinute))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[62]"></a>RTC_GetMonth</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetMonth))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[66]"></a>RTC_GetSecond</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetSecond))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[61]"></a>RTC_GetYear</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_GetYear))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[16]"></a>RTC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rtc.o(i.RTC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetITStatus
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ClearITFlag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>RTC_ITConfig</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[86]"></a>RTC_Init</STRONG> (Thumb, 222 bytes, Stack size 12 bytes, xcm32lxx_rtc.o(i.RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RTC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[7d]"></a>RTC_Run_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, rtc.o(i.RTC_Run_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = RTC_Run_Init &rArr; uf_RTC_Init &rArr; RTC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Cmd
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_RTC_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_CMU_Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init_rtc
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[83]"></a>RTC_SetTime</STRONG> (Thumb, 92 bytes, Stack size 40 bytes, rtc.o(i.RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = RTC_SetTime &rArr; NVIC_Init_rtc &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_StructInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ITConfig
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_DeInit
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Cmd
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init_rtc
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[85]"></a>RTC_StructInit</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, xcm32lxx_rtc.o(i.RTC_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_RTC_Init
</UL>

<P><STRONG><a name="[68]"></a>RTC_To_Sec</STRONG> (Thumb, 204 bytes, Stack size 40 bytes, rtc.o(i.RTC_To_Sec))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = RTC_To_Sec &rArr; Is_Leap_Year &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Is_Leap_Year
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[78]"></a>RadarTurnOff</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, user_main.o(i.RadarTurnOff))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RadarTurnOff &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysTick
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MengMuRadarReceive
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>RadarTurnOn</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, user_main.o(i.RadarTurnOn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RadarTurnOn &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysTick
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[89]"></a>SnapModule_Off</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, user_main.o(i.SnapModule_Off))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SnapModule_Off &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter1
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8b]"></a>SnapModule_On</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, user_main.o(i.SnapModule_On))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SnapModule_On &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetHour
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter1
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[8c]"></a>Switch_4G_Off</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user_main.o(i.Switch_4G_Off))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Switch_4G_Off &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>Switch_4G_On</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, user_main.o(i.Switch_4G_On))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Switch_4G_On &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[8e]"></a>SysTickConfigure</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, main.o(i.SysTickConfigure))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysTickConfigure &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_SetReloadValue
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_ITConfig
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Cmd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EnableIRQ
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_DisableIRQ
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[90]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, xcm32lxx_systick.o(i.SysTick_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[92]"></a>SysTick_Cmd</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, xcm32lxx_systick.o(i.SysTick_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, main.o(i.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>SysTick_ITConfig</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, xcm32lxx_systick.o(i.SysTick_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[8f]"></a>SysTick_SetReloadValue</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_systick.o(i.SysTick_SetReloadValue))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[2d]"></a>SystemInit</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, system_xcm32l.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(.text)
</UL>
<P><STRONG><a name="[9f]"></a>TIMER_Cmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, xcm32lxx_timer.o(i.TIMER_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[9a]"></a>TIMER_DeInit</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, xcm32lxx_timer.o(i.TIMER_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[9d]"></a>TIMER_Init</STRONG> (Thumb, 182 bytes, Stack size 12 bytes, xcm32lxx_timer.o(i.TIMER_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIMER_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[9e]"></a>TIMER_SetLoadCounter0</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, xcm32lxx_timer.o(i.TIMER_SetLoadCounter0))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[8a]"></a>TIMER_SetLoadCounter1</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, xcm32lxx_timer.o(i.TIMER_SetLoadCounter1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER_SetLoadCounter1
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;do_production_test_proc
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_Off
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[9c]"></a>TIMER_StructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, xcm32lxx_timer.o(i.TIMER_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
</UL>

<P><STRONG><a name="[6c]"></a>Test_ACK</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, i2cuart.o(i.Test_ACK))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Test_ACK &rArr; I2C_Stop &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_ReadInputDataBit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_5us
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DirSet
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_TransmitData
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>

<P><STRONG><a name="[99]"></a>Timer1_PwmOut_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, main.o(i.Timer1_PwmOut_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Timer1_PwmOut_Init &rArr; TIMER_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_StructInit
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter1
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter0
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_DeInit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>Timer2_PwmOut_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, main.o(i.Timer2_PwmOut_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Timer2_PwmOut_Init &rArr; TIMER_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_StructInit
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter1
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter0
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_DeInit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f]"></a>UART1_IRQHandler</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, uart.o(i.UART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UART1_IRQHandler &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ReceiveData
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetITStatus
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetLineStatus
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[a4]"></a>UART1_Init</STRONG> (Thumb, 314 bytes, Stack size 32 bytes, uart.o(i.UART1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = UART1_Init &rArr; UART_Init &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_PinAFConfig
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_PTXREModeConfig
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ITConfig
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_FIFOModeConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DeInit
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_DMA_UartRxReqInit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EnableIRQ
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_DisableIRQ
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_ClearPendingIRQ
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e]"></a>UART2_IRQHandler</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, uart.o(i.UART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UART2_IRQHandler &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ReceiveData
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetITStatus
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetLineStatus
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[b2]"></a>UART2_Init</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, uart.o(i.UART2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = UART2_Init &rArr; UART_Init &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_PinAFConfig
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_PTXREModeConfig
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ITConfig
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_FIFOModeConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DeInit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EnableIRQ
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_DisableIRQ
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_ClearPendingIRQ
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[20]"></a>UART4_IRQHandler</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, uart.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART4_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ReceiveData
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetITStatus
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetLineStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[b4]"></a>UART4_Init</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, uart.o(i.UART4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = UART4_Init &rArr; UART_Init &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_PinAFConfig
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_StructInit
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ITConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DeInit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_DisableIRQ
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_ClearPendingIRQ
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a7]"></a>UART_DeInit</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[ab]"></a>UART_FIFOModeConfig</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, xcm32lxx_uart.o(i.UART_FIFOModeConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_FIFOModeConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[a1]"></a>UART_GetITStatus</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[a3]"></a>UART_GetLineStatus</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_GetLineStatus))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_com_tx_data
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[aa]"></a>UART_ITConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[a8]"></a>UART_Init</STRONG> (Thumb, 680 bytes, Stack size 56 bytes, xcm32lxx_uart.o(i.UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = UART_Init &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[b1]"></a>UART_PTXREModeConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_PTXREModeConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[a2]"></a>UART_ReceiveData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[b3]"></a>UART_SendData</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_com_tx_data
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[b5]"></a>UART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, xcm32lxx_uart.o(i.UART_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
</UL>

<P><STRONG><a name="[c2]"></a>WDT_ClearITFlag</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, xcm32lxx_wdt.o(i.WDT_ClearITFlag))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_IRQHandler
</UL>

<P><STRONG><a name="[bf]"></a>WDT_Cmd</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, xcm32lxx_wdt.o(i.WDT_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[b8]"></a>WDT_Configure</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, main.o(i.WDT_Configure))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WDT_Configure &rArr; CMU_WaitForSysClkStartUp
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_WaitForSysClkStartUp
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_StructInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_RestartCmd
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_DeInit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Cmd
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_WDTCLKConfig
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_LSIConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EnableIRQ
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_DisableIRQ
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bc]"></a>WDT_DeInit</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, xcm32lxx_wdt.o(i.WDT_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[c1]"></a>WDT_GetITStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, xcm32lxx_wdt.o(i.WDT_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_IRQHandler
</UL>

<P><STRONG><a name="[29]"></a>WDT_IRQHandler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, main.o(i.WDT_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WDT_IRQHandler &rArr; WDT_RestartCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_RestartCmd
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_GetITStatus
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_ClearITFlag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_xcm32l.o(RESET)
</UL>
<P><STRONG><a name="[be]"></a>WDT_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, xcm32lxx_wdt.o(i.WDT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WDT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[c0]"></a>WDT_RestartCmd</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, xcm32lxx_wdt.o(i.WDT_RestartCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WDT_RestartCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_IRQHandler
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[bd]"></a>WDT_StructInit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, xcm32lxx_wdt.o(i.WDT_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
</UL>

<P><STRONG><a name="[c3]"></a>__0printf</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[134]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[57]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_On
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOn
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MengMuRadarReceive
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_unpack_data
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_Off
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_Off
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
</UL>

<P><STRONG><a name="[135]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[136]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[c5]"></a>__0snprintf</STRONG> (Thumb, 38 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[137]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[eb]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[138]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[139]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[c6]"></a>__0sprintf</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[13a]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[10a]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>

<P><STRONG><a name="[13b]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[13c]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[4e]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[10f]"></a>__ARM_common_switch8</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, cjson.o(i.__ARM_common_switch8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_common_switch8
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[103]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[c7]"></a>__kernel_poly</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[c8]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[c9]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[ca]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[cb]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_overflow &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[cd]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_underflow &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[13d]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[13e]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[13f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[101]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[d2]"></a>cJSON_AddItemToArray</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, cjson.o(i.cJSON_AddItemToArray))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cJSON_AddItemToArray
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;suffix_object
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToObject
</UL>

<P><STRONG><a name="[d4]"></a>cJSON_AddItemToObject</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, cjson.o(i.cJSON_AddItemToObject))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cJSON_AddItemToObject &rArr; cJSON_AddItemToArray
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToArray
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strdup
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[d6]"></a>cJSON_CreateNumber</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, cjson.o(i.cJSON_CreateNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cJSON_CreateNumber &rArr; __aeabi_d2iz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[d8]"></a>cJSON_CreateObject</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, cjson.o(i.cJSON_CreateObject))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cJSON_CreateObject &rArr; cJSON_New_Item
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[d9]"></a>cJSON_CreateString</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, cjson.o(i.cJSON_CreateString))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cJSON_CreateString &rArr; cJSON_strdup
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strdup
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[da]"></a>cJSON_Delete</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, cjson.o(i.cJSON_Delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + In Cycle
<LI>Call Chain = cJSON_Delete &rArr;  cJSON_Delete (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Delete
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_ParseWithOpts
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Delete
</UL>

<P><STRONG><a name="[db]"></a>cJSON_GetObjectItem</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, cjson.o(i.cJSON_GetObjectItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cJSON_GetObjectItem &rArr; cJSON_strcasecmp
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strcasecmp
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[dd]"></a>cJSON_Parse</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, cjson.o(i.cJSON_Parse))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = cJSON_Parse &rArr; cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_ParseWithOpts
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[de]"></a>cJSON_ParseWithOpts</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, cjson.o(i.cJSON_ParseWithOpts))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Delete
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skip
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Parse
</UL>

<P><STRONG><a name="[e1]"></a>cJSON_Print</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, cjson.o(i.cJSON_Print))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = cJSON_Print &rArr; print_value &rArr; print_number &rArr; floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[e6]"></a>cJsonParseProtocol</STRONG> (Thumb, 5530 bytes, Stack size 144 bytes, protol.o(i.cJsonParseProtocol))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = cJsonParseProtocol &rArr; cJSON_Parse &rArr; cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_com_tx_data
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;t_HEX2BCD
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pack_data
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_To_Sec
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Print
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Parse
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_GetObjectItem
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Delete
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateString
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateObject
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateNumber
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToObject
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[fa]"></a>crc32</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, crc32.o(i.crc32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pack_data
</UL>

<P><STRONG><a name="[98]"></a>delay</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, i2cuart.o(i.delay))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
</UL>

<P><STRONG><a name="[72]"></a>delay_5us</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, i2cuart.o(i.delay_5us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_5us
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_ACK
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Master_ACK
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_send_byte
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_read_byte
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[f0]"></a>do_production_test_proc</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, user_main.o(i.do_production_test_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = do_production_test_proc &rArr; PORT_WriteBit
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER_SetLoadCounter1
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[ef]"></a>enter_stop_mode</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, user_main.o(i.enter_stop_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = enter_stop_mode &rArr; Booster_Stop &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_Off
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_Off
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Stop
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[f3]"></a>floor</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, floor.o(i.floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>

<P><STRONG><a name="[2f]"></a>fputc</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetLineStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[5]"></a>free</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cjson.o(.data)
</UL>
<P><STRONG><a name="[e8]"></a>get_voltage</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, i2cuart.o(i.get_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = get_voltage &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_TransmitData
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_ReceiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[2c]"></a>main</STRONG> (Thumb, 132 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = main &rArr; user_main &rArr; cJsonParseProtocol &rArr; cJSON_Parse &rArr; cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uf_GPIO_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_SysClkConfig
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_HSIConfig
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_Off
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_Off
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_PCLKConfig
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_HCLKConfig
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer2_PwmOut_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_PwmOut_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[4]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cjson.o(.data)
</UL>
<P><STRONG><a name="[e9]"></a>pack_data</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, uart.o(i.pack_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pack_data &rArr; crc32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc32
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[fd]"></a>pow</STRONG> (Thumb, 2548 bytes, Stack size 128 bytes, pow.o(i.pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
</UL>

<P><STRONG><a name="[ec]"></a>send_transparent_info</STRONG> (Thumb, 510 bytes, Stack size 48 bytes, protol.o(i.send_transparent_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = send_transparent_info &rArr; cJSON_Print &rArr; print_value &rArr; print_number &rArr; floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_com_tx_data
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pack_data
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Print
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Delete
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateString
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateObject
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateNumber
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToObject
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[102]"></a>sqrt</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[110]"></a>startSnap</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, user_main.o(i.startSnap))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = startSnap &rArr; Booster_Start &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_On
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[67]"></a>t_BCD2HEX</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, rtc.o(i.t_BCD2HEX))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
</UL>

<P><STRONG><a name="[ee]"></a>t_HEX2BCD</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, rtc.o(i.t_HEX2BCD))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = t_HEX2BCD &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[77]"></a>uart1_rx_mengmu_process</STRONG> (Thumb, 772 bytes, Stack size 80 bytes, uart.o(i.uart1_rx_mengmu_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = uart1_rx_mengmu_process &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MengMuRadarReceive
</UL>

<P><STRONG><a name="[ea]"></a>uart2_com_tx_data</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, uart.o(i.uart2_com_tx_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart2_com_tx_data
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetLineStatus
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_transparent_info
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
</UL>

<P><STRONG><a name="[111]"></a>uart2_find_head_and_tail</STRONG> (Thumb, 324 bytes, Stack size 72 bytes, uart.o(i.uart2_find_head_and_tail))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = uart2_find_head_and_tail &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_rx_process
</UL>

<P><STRONG><a name="[112]"></a>uart2_rx_process</STRONG> (Thumb, 496 bytes, Stack size 104 bytes, uart.o(i.uart2_rx_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = uart2_rx_process &rArr; uart2_find_head_and_tail &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_unpack_data
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_find_head_and_tail
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main
</UL>

<P><STRONG><a name="[113]"></a>uart2_unpack_data</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, uart.o(i.uart2_unpack_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = uart2_unpack_data &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_rx_process
</UL>

<P><STRONG><a name="[ac]"></a>uf_DMA_UartRxReqInit</STRONG> (Thumb, 108 bytes, Stack size 56 bytes, uart.o(i.uf_DMA_UartRxReqInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = uf_DMA_UartRxReqInit &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_StructInit
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ITConfig
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph0ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[7e]"></a>uf_GPIO_CMU_Init</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, rtc.o(i.uf_GPIO_CMU_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = uf_GPIO_CMU_Init &rArr; PORT_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[f8]"></a>uf_GPIO_Init</STRONG> (Thumb, 294 bytes, Stack size 16 bytes, gpio.o(i.uf_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = uf_GPIO_Init &rArr; PORT_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_StructInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_PinAFConfig
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_DeInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7f]"></a>uf_GPIO_RTC_Init</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, rtc.o(i.uf_GPIO_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = uf_GPIO_RTC_Init &rArr; PORT_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_PinAFConfig
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[80]"></a>uf_RTC_Init</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, rtc.o(i.uf_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = uf_RTC_Init &rArr; RTC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_APBPeriph1ClockCmd
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_WaitForSysClkStartUp
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_StructInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ITConfig
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_DeInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_RTCCLKConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_LSEConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[f9]"></a>user_main</STRONG> (Thumb, 2250 bytes, Stack size 32 bytes, user_main.o(i.user_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = user_main &rArr; cJsonParseProtocol &rArr; cJSON_Parse &rArr; cJSON_ParseWithOpts &rArr; parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_voltage
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enter_stop_mode
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT_WriteBit
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRtcSecond
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJsonParseProtocol
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PMU_EnterDeepSleep3Mode
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_GetSysClkSource
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMU_GetSysClkFreq
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;startSnap
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;do_production_test_proc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_4G_On
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SnapModule_On
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOn
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MengMuRadarReceive
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_rx_process
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RadarTurnOff
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Start
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Booster_DeInit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abs
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[97]"></a>SetSysClock</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_xcm32l.o(i.SetSysClock))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[d7]"></a>cJSON_New_Item</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, cjson.o(i.cJSON_New_Item))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = cJSON_New_Item
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_ParseWithOpts
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateString
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateObject
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateNumber
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_object
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_array
</UL>

<P><STRONG><a name="[dc]"></a>cJSON_strcasecmp</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, cjson.o(i.cJSON_strcasecmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cJSON_strcasecmp
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tolower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_GetObjectItem
</UL>

<P><STRONG><a name="[d5]"></a>cJSON_strdup</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, cjson.o(i.cJSON_strdup))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cJSON_strdup
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_CreateString
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToObject
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[f1]"></a>ensure</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, cjson.o(i.ensure))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ensure
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow2gt
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
</UL>

<P><STRONG><a name="[fb]"></a>parse_array</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, cjson.o(i.parse_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + In Cycle
<LI>Call Chain = parse_array &rArr;  parse_value (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skip
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
</UL>

<P><STRONG><a name="[100]"></a>parse_hex4</STRONG> (Thumb, 276 bytes, Stack size 0 bytes, cjson.o(i.parse_hex4))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_string
</UL>

<P><STRONG><a name="[fc]"></a>parse_number</STRONG> (Thumb, 384 bytes, Stack size 80 bytes, cjson.o(i.parse_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
</UL>

<P><STRONG><a name="[fe]"></a>parse_object</STRONG> (Thumb, 266 bytes, Stack size 32 bytes, cjson.o(i.parse_object))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + In Cycle
<LI>Call Chain = parse_object &rArr;  parse_value (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skip
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_string
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_New_Item
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
</UL>

<P><STRONG><a name="[ff]"></a>parse_string</STRONG> (Thumb, 472 bytes, Stack size 40 bytes, cjson.o(i.parse_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = parse_string
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_hex4
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_value
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_object
</UL>

<P><STRONG><a name="[e0]"></a>parse_value</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, cjson.o(i.parse_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = parse_value &rArr; parse_number &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_string
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_object
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_number
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_array
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_ParseWithOpts
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_object
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_array
</UL>

<P><STRONG><a name="[f2]"></a>pow2gt</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, cjson.o(i.pow2gt))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
</UL>

<P><STRONG><a name="[104]"></a>print_array</STRONG> (Thumb, 596 bytes, Stack size 64 bytes, cjson.o(i.print_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + In Cycle
<LI>Call Chain = print_array &rArr;  print_value (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[107]"></a>print_number</STRONG> (Thumb, 360 bytes, Stack size 48 bytes, cjson.o(i.print_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = print_number &rArr; floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;floor
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[10b]"></a>print_object</STRONG> (Thumb, 1160 bytes, Stack size 80 bytes, cjson.o(i.print_object))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + In Cycle
<LI>Call Chain = print_object &rArr;  print_value (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[10d]"></a>print_string</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, cjson.o(i.print_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = print_string &rArr; print_string_ptr &rArr; ensure
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_value
</UL>

<P><STRONG><a name="[10c]"></a>print_string_ptr</STRONG> (Thumb, 448 bytes, Stack size 40 bytes, cjson.o(i.print_string_ptr))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = print_string_ptr &rArr; ensure
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
</UL>

<P><STRONG><a name="[e2]"></a>print_value</STRONG> (Thumb, 282 bytes, Stack size 40 bytes, cjson.o(i.print_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = print_value &rArr; print_number &rArr; floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_switch8
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_string
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ensure
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_strdup
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_Print
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
</UL>

<P><STRONG><a name="[df]"></a>skip</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, cjson.o(i.skip))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_ParseWithOpts
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_object
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_array
</UL>

<P><STRONG><a name="[d3]"></a>suffix_object</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cjson.o(i.suffix_object))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cJSON_AddItemToArray
</UL>

<P><STRONG><a name="[106]"></a>update</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, cjson.o(i.update))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = update
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_object
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_array
</UL>

<P><STRONG><a name="[94]"></a>NVIC_ClearPendingIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(i.NVIC_ClearPendingIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[93]"></a>NVIC_DisableIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(i.NVIC_DisableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[96]"></a>NVIC_EnableIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(i.NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[95]"></a>NVIC_SetPriority</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, main.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Configure
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickConfigure
</UL>

<P><STRONG><a name="[79]"></a>NVIC_Init_rtc</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, rtc.o(i.NVIC_Init_rtc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init_rtc &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTime
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Run_Init
</UL>

<P><STRONG><a name="[7a]"></a>NVIC_SetPriority</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, rtc.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init_rtc
</UL>

<P><STRONG><a name="[ae]"></a>NVIC_ClearPendingIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(i.NVIC_ClearPendingIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[ad]"></a>NVIC_DisableIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(i.NVIC_DisableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[b0]"></a>NVIC_EnableIRQ</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(i.NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[af]"></a>NVIC_SetPriority</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, uart.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[ce]"></a>_fp_digits</STRONG> (Thumb, 316 bytes, Stack size 56 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[c4]"></a>_printf_core</STRONG> (Thumb, 1754 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[d1]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[d0]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[30]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[31]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
