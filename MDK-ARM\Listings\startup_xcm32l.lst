


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ********************//**
    2 00000000         ; * @file     startup_XCM32L.s
    3 00000000         ; * @brief    CMSIS Core Device Startup File for
    4 00000000         ; *           XCM32L Device Series
    5 00000000         ; * @version  V1.0.5
    6 00000000         ; * @date     11-25-2018
    7 00000000         ; *
    8 00000000         ; * @note
    9 00000000         ; *
   10 00000000         ; ******************************************************
                       ************************/
   11 00000000         ;   All rights reserved.
   12 00000000         ;   Redistribution and use in source and binary forms, w
                       ith or without
   13 00000000         ;   modification, are permitted provided that the follow
                       ing conditions are met:
   14 00000000         ;   - Redistributions of source code must retain the abo
                       ve copyright
   15 00000000         ;     notice, this list of conditions and the following 
                       disclaimer.
   16 00000000         ;   - Redistributions in binary form must reproduce the 
                       above copyright
   17 00000000         ;     notice, this list of conditions and the following 
                       disclaimer in the
   18 00000000         ;     documentation and/or other materials provided with
                        the distribution.
   19 00000000         ;   - Neither the name of ARM nor the names of its contr
                       ibutors may be used
   20 00000000         ;     to endorse or promote products derived from this s
                       oftware without
   21 00000000         ;     specific prior written permission.
   22 00000000         ;   *
   23 00000000         ;   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS A
                       ND CONTRIBUTORS "AS IS"
   24 00000000         ;   AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BU
                       T NOT LIMITED TO, THE
   25 00000000         ;   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FO
                       R A PARTICULAR PURPOSE
   26 00000000         ;   ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS BE
   27 00000000         ;   LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL
                       , EXEMPLARY, OR
   28 00000000         ;   CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO
                       , PROCUREMENT OF
   29 00000000         ;   SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
                       PROFITS; OR BUSINESS
   30 00000000         ;   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LI
                       ABILITY, WHETHER IN
   31 00000000         ;   CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLI
                       GENCE OR OTHERWISE)
   32 00000000         ;   ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, 
                       EVEN IF ADVISED OF THE
   33 00000000         ;   POSSIBILITY OF SUCH DAMAGE.
   34 00000000         ;   ----------------------------------------------------
                       -----------------------*/
   35 00000000         ;/*
   36 00000000         ;//-------- <<< Use Configuration Wizard in Context Menu
                        >>> ------------------



ARM Macro Assembler    Page 2 


   37 00000000         ;*/
   38 00000000         
   39 00000000         
   40 00000000         ; <h> Stack Configuration
   41 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   42 00000000         ; </h>
   43 00000000         
   44 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   45 00000000         
   46 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   47 00000000         Stack_Mem
                               SPACE            Stack_Size
   48 00000400         __initial_sp
   49 00000400         
   50 00000400         
   51 00000400         ; <h> Heap Configuration
   52 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   53 00000400         ; </h>
   54 00000400         
   55 00000400 00000C00 
                       Heap_Size
                               EQU              0x00000C00
   56 00000400         
   57 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   58 00000000         __heap_base
   59 00000000         Heap_Mem
                               SPACE            Heap_Size
   60 00000C00         __heap_limit
   61 00000C00         
   62 00000C00         
   63 00000C00                 PRESERVE8
   64 00000C00                 THUMB
   65 00000C00         
   66 00000C00         
   67 00000C00         ; Vector Table Mapped to Address 0 at Reset
   68 00000C00         
   69 00000C00                 AREA             RESET, DATA, READONLY
   70 00000000                 EXPORT           __Vectors
   71 00000000                 EXPORT           __Vectors_End
   72 00000000                 EXPORT           __Vectors_Size
   73 00000000         
   74 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   75 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   76 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   77 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   78 00000010 00000000        DCD              0           ; Reserved
   79 00000014 00000000        DCD              0           ; Reserved
   80 00000018 00000000        DCD              0           ; Reserved
   81 0000001C 00000000        DCD              0           ; Reserved
   82 00000020 00000000        DCD              0           ; Reserved
   83 00000024 00000000        DCD              0           ; Reserved
   84 00000028 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 3 


   85 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   86 00000030 00000000        DCD              0           ; Reserved
   87 00000034 00000000        DCD              0           ; Reserved
   88 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   89 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   90 00000040         
   91 00000040         ; External Interrupts
   92 00000040 00000000        DCD              DMAC_IRQHandler
   93 00000044 00000000        DCD              P0_IRQHandler
   94 00000048 00000000        DCD              TIMER1_IRQHandler
   95 0000004C 00000000        DCD              UART1_IRQHandler
   96 00000050 00000000        DCD              SPI1_IRQHandler
   97 00000054 00000000        DCD              I2C1_IRQHandler
   98 00000058 00000000        DCD              RAM_IRQHandler
   99 0000005C 00000000        DCD              ADC_IRQHandler
  100 00000060 00000000        DCD              VC_IRQHandler
  101 00000064 00000000        DCD              LVD_IRQHandler
  102 00000068 00000000        DCD              RTC_IRQHandler
  103 0000006C 00000000        DCD              BASETIMER_IRQHandler
  104 00000070 00000000        DCD              P1P2_IRQHandler
  105 00000074 00000000        DCD              P3P4_IRQHandler
  106 00000078 00000000        DCD              P5P6_IRQHandler
  107 0000007C 00000000        DCD              TIMER2_IRQHandler
  108 00000080 00000000        DCD              TIMER3_IRQHandler
  109 00000084 00000000        DCD              TIMER4_IRQHandler
  110 00000088 00000000        DCD              UART2_IRQHandler
  111 0000008C 00000000        DCD              UART3_IRQHandler
  112 00000090 00000000        DCD              UART4_IRQHandler
  113 00000094 00000000        DCD              UART5_IRQHandler
  114 00000098 00000000        DCD              UART6_IRQHandler
  115 0000009C 00000000        DCD              SPI2_IRQHandler
  116 000000A0 00000000        DCD              SPI3_IRQHandler
  117 000000A4 00000000        DCD              SPI4_IRQHandler
  118 000000A8 00000000        DCD              I2C2_IRQHandler
  119 000000AC 00000000        DCD              PCA12_IRQHandler
  120 000000B0 00000000        DCD              PCA34_IRQHandler
  121 000000B4 00000000        DCD              WDT_IRQHandler
  122 000000B8 00000000        DCD              LCD_IRQHandler
  123 000000BC 00000000        DCD              SCI7816_IRQHandler
  124 000000C0         __Vectors_End
  125 000000C0         
  126 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  127 000000C0         
  128 000000C0                 AREA             |.text|, CODE, READONLY
  129 00000000         
  130 00000000         
  131 00000000         ; Reset Handler
  132 00000000         
  133 00000000         Reset_Handler
                               PROC
  134 00000000                 EXPORT           Reset_Handler             [WEAK
]
  135 00000000                 IMPORT           SystemInit
  136 00000000                 IMPORT           __main
  137 00000000 4804            LDR              R0, =SystemInit



ARM Macro Assembler    Page 4 


  138 00000002 4780            BLX              R0
  139 00000004 4804            LDR              R0, =__main
  140 00000006 4700            BX               R0
  141 00000008                 ENDP
  142 00000008         
  143 00000008         
  144 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  145 00000008         
  146 00000008         NMI_Handler
                               PROC
  147 00000008                 EXPORT           NMI_Handler               [WEAK
]
  148 00000008 E7FE            B                .
  149 0000000A                 ENDP
  151 0000000A         HardFault_Handler
                               PROC
  152 0000000A                 EXPORT           HardFault_Handler         [WEAK
]
  153 0000000A E7FE            B                .
  154 0000000C                 ENDP
  155 0000000C         SVC_Handler
                               PROC
  156 0000000C                 EXPORT           SVC_Handler               [WEAK
]
  157 0000000C E7FE            B                .
  158 0000000E                 ENDP
  159 0000000E         PendSV_Handler
                               PROC
  160 0000000E                 EXPORT           PendSV_Handler            [WEAK
]
  161 0000000E E7FE            B                .
  162 00000010                 ENDP
  163 00000010         SysTick_Handler
                               PROC
  164 00000010                 EXPORT           SysTick_Handler           [WEAK
]
  165 00000010 E7FE            B                .
  166 00000012                 ENDP
  167 00000012         
  168 00000012         Default_Handler
                               PROC
  169 00000012         
  170 00000012                 EXPORT           DMAC_IRQHandler             [WE
AK]
  171 00000012                 EXPORT           P0_IRQHandler               [WE
AK]
  172 00000012                 EXPORT           TIMER1_IRQHandler           [WE
AK]
  173 00000012                 EXPORT           UART1_IRQHandler           [WEA
K]
  174 00000012                 EXPORT           SPI1_IRQHandler             [WE
AK]
  175 00000012                 EXPORT           I2C1_IRQHandler             [WE
AK]
  176 00000012                 EXPORT           RAM_IRQHandler              [WE
AK]
  177 00000012                 EXPORT           ADC_IRQHandler              [WE
AK]



ARM Macro Assembler    Page 5 


  178 00000012                 EXPORT           VC_IRQHandler               [WE
AK]
  179 00000012                 EXPORT           LVD_IRQHandler              [WE
AK]
  180 00000012                 EXPORT           RTC_IRQHandler              [WE
AK]
  181 00000012                 EXPORT           BASETIMER_IRQHandler        [WE
AK]
  182 00000012                 EXPORT           P1P2_IRQHandler             [WE
AK]
  183 00000012                 EXPORT           P3P4_IRQHandler             [WE
AK]
  184 00000012                 EXPORT           P5P6_IRQHandler             [WE
AK]
  185 00000012                 EXPORT           TIMER2_IRQHandler           [WE
AK]
  186 00000012                 EXPORT           TIMER3_IRQHandler           [WE
AK]
  187 00000012                 EXPORT           TIMER4_IRQHandler           [WE
AK]
  188 00000012                 EXPORT           UART2_IRQHandler            [WE
AK]
  189 00000012                 EXPORT           UART3_IRQHandler            [WE
AK]
  190 00000012                 EXPORT           UART4_IRQHandler            [WE
AK]
  191 00000012                 EXPORT           UART5_IRQHandler            [WE
AK]
  192 00000012                 EXPORT           UART6_IRQHandler            [WE
AK]
  193 00000012                 EXPORT           SPI2_IRQHandler             [WE
AK]
  194 00000012                 EXPORT           SPI3_IRQHandler             [WE
AK]
  195 00000012                 EXPORT           SPI4_IRQHandler             [WE
AK]
  196 00000012                 EXPORT           I2C2_IRQHandler             [WE
AK]
  197 00000012                 EXPORT           PCA12_IRQHandler            [WE
AK]
  198 00000012                 EXPORT           PCA34_IRQHandler            [WE
AK]
  199 00000012                 EXPORT           WDT_IRQHandler              [WE
AK]
  200 00000012                 EXPORT           LCD_IRQHandler              [WE
AK]
  201 00000012                 EXPORT           SCI7816_IRQHandler          [WE
AK]
  202 00000012         
  203 00000012         
  204 00000012         
  205 00000012         DMAC_IRQHandler
  206 00000012         P0_IRQHandler
  207 00000012         TIMER1_IRQHandler
  208 00000012         UART1_IRQHandler
  209 00000012         SPI1_IRQHandler
  210 00000012         I2C1_IRQHandler
  211 00000012         RAM_IRQHandler
  212 00000012         ADC_IRQHandler



ARM Macro Assembler    Page 6 


  213 00000012         VC_IRQHandler
  214 00000012         LVD_IRQHandler
  215 00000012         RTC_IRQHandler
  216 00000012         BASETIMER_IRQHandler
  217 00000012         P1P2_IRQHandler
  218 00000012         P3P4_IRQHandler
  219 00000012         P5P6_IRQHandler
  220 00000012         TIMER2_IRQHandler
  221 00000012         TIMER3_IRQHandler
  222 00000012         TIMER4_IRQHandler
  223 00000012         UART2_IRQHandler
  224 00000012         UART3_IRQHandler
  225 00000012         UART4_IRQHandler
  226 00000012         UART5_IRQHandler
  227 00000012         UART6_IRQHandler
  228 00000012         SPI2_IRQHandler
  229 00000012         SPI3_IRQHandler
  230 00000012         SPI4_IRQHandler
  231 00000012         I2C2_IRQHandler
  232 00000012         PCA12_IRQHandler
  233 00000012         PCA34_IRQHandler
  234 00000012         WDT_IRQHandler
  235 00000012         LCD_IRQHandler
  236 00000012         SCI7816_IRQHandler
  237 00000012 E7FE            B                .
  238 00000014         
  239 00000014                 ENDP
  240 00000014         
  241 00000014         
  242 00000014                 ALIGN
  243 00000014         
  244 00000014         
  245 00000014         ; User Initial Stack & Heap
  246 00000014         
  247 00000014                 IF               :DEF:__MICROLIB
  248 00000014         
  249 00000014                 EXPORT           __initial_sp
  250 00000014                 EXPORT           __heap_base
  251 00000014                 EXPORT           __heap_limit
  252 00000014         
  253 00000014                 ELSE
  268                          ENDIF
  269 00000014         
  270 00000014         
  271 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=.\objects\startup_xcm32l.d -o.\objects\startup_xcm32l.o -IF:\code
\xcm32l\fh_xcm32l_app\MDK-ARM\RTE -IC:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\I
nclude -IC:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include --pr
edefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 518" --predefin
e="_RTE_ SETA 1" --predefine="ARMCM0P SETA 1" --list=.\listings\startup_xcm32l.
lst ..\xcm32lxx_lib\core\startup_XCM32L.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 46 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 47 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 48 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 74 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 249 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 57 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 59 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 58 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 250 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: __heap_base used once
__heap_limit 00000C00

Symbol: __heap_limit
   Definitions
      At line 60 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 251 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 69 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 74 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 70 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 126 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 124 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 71 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 126 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 128 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 00000012

Symbol: ADC_IRQHandler
   Definitions
      At line 212 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 99 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 177 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

BASETIMER_IRQHandler 00000012

Symbol: BASETIMER_IRQHandler
   Definitions
      At line 216 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 103 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 181 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

DMAC_IRQHandler 00000012

Symbol: DMAC_IRQHandler
   Definitions
      At line 205 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 92 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 170 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

Default_Handler 00000012

Symbol: Default_Handler
   Definitions
      At line 168 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      None
Comment: Default_Handler unused
HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 151 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 77 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 152 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

I2C1_IRQHandler 00000012

Symbol: I2C1_IRQHandler
   Definitions
      At line 210 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 97 in file ..\xcm32lxx_lib\core\startup_XCM32L.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 175 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

I2C2_IRQHandler 00000012

Symbol: I2C2_IRQHandler
   Definitions
      At line 231 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 118 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 196 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

LCD_IRQHandler 00000012

Symbol: LCD_IRQHandler
   Definitions
      At line 235 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 122 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 200 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

LVD_IRQHandler 00000012

Symbol: LVD_IRQHandler
   Definitions
      At line 214 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 101 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 179 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 146 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 76 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 147 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

P0_IRQHandler 00000012

Symbol: P0_IRQHandler
   Definitions
      At line 206 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 93 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 171 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

P1P2_IRQHandler 00000012

Symbol: P1P2_IRQHandler
   Definitions
      At line 217 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 104 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 182 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

P3P4_IRQHandler 00000012

Symbol: P3P4_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 218 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 105 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 183 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

P5P6_IRQHandler 00000012

Symbol: P5P6_IRQHandler
   Definitions
      At line 219 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 106 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 184 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

PCA12_IRQHandler 00000012

Symbol: PCA12_IRQHandler
   Definitions
      At line 232 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 119 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 197 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

PCA34_IRQHandler 00000012

Symbol: PCA34_IRQHandler
   Definitions
      At line 233 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 120 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 198 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 159 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 88 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 160 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

RAM_IRQHandler 00000012

Symbol: RAM_IRQHandler
   Definitions
      At line 211 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 98 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 176 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

RTC_IRQHandler 00000012

Symbol: RTC_IRQHandler
   Definitions
      At line 215 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 102 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 180 in file ..\xcm32lxx_lib\core\startup_XCM32L.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 133 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 75 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 134 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SCI7816_IRQHandler 00000012

Symbol: SCI7816_IRQHandler
   Definitions
      At line 236 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 123 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 201 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SPI1_IRQHandler 00000012

Symbol: SPI1_IRQHandler
   Definitions
      At line 209 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 96 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 174 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SPI2_IRQHandler 00000012

Symbol: SPI2_IRQHandler
   Definitions
      At line 228 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 115 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 193 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SPI3_IRQHandler 00000012

Symbol: SPI3_IRQHandler
   Definitions
      At line 229 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 116 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 194 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SPI4_IRQHandler 00000012

Symbol: SPI4_IRQHandler
   Definitions
      At line 230 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 117 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 195 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 155 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 85 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 156 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 163 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 89 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 164 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

TIMER1_IRQHandler 00000012

Symbol: TIMER1_IRQHandler
   Definitions
      At line 207 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 94 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 172 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

TIMER2_IRQHandler 00000012

Symbol: TIMER2_IRQHandler
   Definitions
      At line 220 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 107 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 185 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

TIMER3_IRQHandler 00000012

Symbol: TIMER3_IRQHandler
   Definitions
      At line 221 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 108 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 186 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

TIMER4_IRQHandler 00000012

Symbol: TIMER4_IRQHandler
   Definitions
      At line 222 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 109 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 187 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

UART1_IRQHandler 00000012

Symbol: UART1_IRQHandler
   Definitions
      At line 208 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 95 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 173 in file ..\xcm32lxx_lib\core\startup_XCM32L.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

UART2_IRQHandler 00000012

Symbol: UART2_IRQHandler
   Definitions
      At line 223 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 110 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 188 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

UART3_IRQHandler 00000012

Symbol: UART3_IRQHandler
   Definitions
      At line 224 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 111 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 189 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

UART4_IRQHandler 00000012

Symbol: UART4_IRQHandler
   Definitions
      At line 225 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 112 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 190 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

UART5_IRQHandler 00000012

Symbol: UART5_IRQHandler
   Definitions
      At line 226 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 113 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 191 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

UART6_IRQHandler 00000012

Symbol: UART6_IRQHandler
   Definitions
      At line 227 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 114 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 192 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

VC_IRQHandler 00000012

Symbol: VC_IRQHandler
   Definitions
      At line 213 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 100 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 178 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

WDT_IRQHandler 00000012

Symbol: WDT_IRQHandler
   Definitions
      At line 234 in file ..\xcm32lxx_lib\core\startup_XCM32L.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 121 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
      At line 199 in file ..\xcm32lxx_lib\core\startup_XCM32L.s

40 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000C00

Symbol: Heap_Size
   Definitions
      At line 55 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 59 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 44 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 47 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: Stack_Size used once
__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 126 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 72 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 135 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 137 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 136 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
   Uses
      At line 139 in file ..\xcm32lxx_lib\core\startup_XCM32L.s
Comment: __main used once
2 symbols
391 symbols in table
