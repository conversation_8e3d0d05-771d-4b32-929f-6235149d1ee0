Dependencies for Project 'XCM32L_app', Target 'XCM32L_app': (DO NOT MODIFY !)
F (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)()
F (..\xcm32lxx_lib\core\startup_XCM32L.s)(0x5D3FF162)(--cpu Cortex-M0+ --li -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

--pd "__UVISION_VERSION SETA 518" --pd "_RTE_ SETA 1" --pd "ARMCM0P SETA 1"

--list .\listings\startup_xcm32l.lst --xref -o .\objects\startup_xcm32l.o --depend .\objects\startup_xcm32l.d)
F (..\xcm32lxx_lib\core\system_XCM32L.c)(0x5F589866)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\system_xcm32l.o --omf_browse .\objects\system_xcm32l.crf --depend .\objects\system_xcm32l.d)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)()
F (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)()
F (..\User\cJSON\cJSON.c)(0x6045A39D)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\cjson.o --omf_browse .\objects\cjson.crf --depend .\objects\cjson.d)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\ctype.h)(0x569DEA38)
I (..\User\cJSON\cJSON.h)(0x6045A39D)
F (..\User\cJSON\protol.c)(0x626632CB)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\protol.o --omf_browse .\objects\protol.crf --depend .\objects\protol.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x569DEA3A)
I (..\User\cJSON\cJSON.h)(0x6045A39D)
I (..\User\i2cUart.h)(0x60DBE7B1)
I (..\User\cJSON\protol.h)(0x625FE2F1)
I (..\User\user_main.h)(0x6125B928)
I (..\User\rtc.h)(0x60D937C3)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (..\User\uart.h)(0x62612D1D)
I (..\User\main.h)(0x6260D440)
F (..\User\crc32.c)(0x60BBC69F)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\crc32.o --omf_browse .\objects\crc32.crf --depend .\objects\crc32.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
F (..\User\flash.c)(0x60ACA280)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\flash.o --omf_browse .\objects\flash.crf --depend .\objects\flash.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
F (..\User\gpio.c)(0x625FDA17)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\gpio.o --omf_browse .\objects\gpio.crf --depend .\objects\gpio.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\User\i2cUart.c)(0x60DDA158)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\i2cuart.o --omf_browse .\objects\i2cuart.crf --depend .\objects\i2cuart.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\User\main.c)(0x627B1B5A)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\common.h)(0x625FDFC4)
I (..\User\gpio.h)(0x60BBB53C)
I (..\User\uart.h)(0x62612D1D)
I (..\User\rtc.h)(0x60D937C3)
I (..\User\i2cUart.h)(0x60DBE7B1)
I (..\User\main.h)(0x6260D440)
F (..\User\rtc.c)(0x625FE886)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (..\User\rtc.h)(0x60D937C3)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x569DEA3A)
F (..\User\uart.c)(0x62624AEC)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\uart.o --omf_browse .\objects\uart.crf --depend .\objects\uart.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (C:\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x569DEA3A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\user_main.h)(0x6125B928)
I (..\User\uart.h)(0x62612D1D)
I (..\xcm32lxx_lib\inc\XCM32Lxx_dma.h)(0x5C9B1E00)
I (..\User\main.h)(0x6260D440)
F (..\User\user_main.c)(0x6295AD74)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\user_main.o --omf_browse .\objects\user_main.crf --depend .\objects\user_main.d)
I (..\User\gpio.h)(0x60BBB53C)
I (..\User\cJSON\protol.h)(0x625FE2F1)
I (..\User\rtc.h)(0x60D937C3)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\uart.h)(0x62612D1D)
I (..\User\user_main.h)(0x6125B928)
I (..\User\i2cUart.h)(0x60DBE7B1)
I (..\User\main.h)(0x6260D440)
F (..\xcm32lxx_lib\src\XCM32Lxx_adc.c)(0x5D443A6D)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_adc.o --omf_browse .\objects\xcm32lxx_adc.crf --depend .\objects\xcm32lxx_adc.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_adc.h)(0x5C9B1C77)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_buzzer.c)(0x5C9B1732)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_buzzer.o --omf_browse .\objects\xcm32lxx_buzzer.crf --depend .\objects\xcm32lxx_buzzer.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_buzzer.h)(0x5C9B1CCB)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_cmu.c)(0x5C9B1719)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_cmu.o --omf_browse .\objects\xcm32lxx_cmu.crf --depend .\objects\xcm32lxx_cmu.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_crc.c)(0x5C9B16C2)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_crc.o --omf_browse .\objects\xcm32lxx_crc.crf --depend .\objects\xcm32lxx_crc.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_crc.h)(0x5C9B1DB2)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_des.c)(0x5C9B1795)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_des.o --omf_browse .\objects\xcm32lxx_des.crf --depend .\objects\xcm32lxx_des.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_des.h)(0x5C9B1DD9)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_dma.c)(0x5C9B1B11)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_dma.o --omf_browse .\objects\xcm32lxx_dma.crf --depend .\objects\xcm32lxx_dma.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_dma.h)(0x5C9B1E00)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_flash.c)(0x5C9B183C)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_flash.o --omf_browse .\objects\xcm32lxx_flash.crf --depend .\objects\xcm32lxx_flash.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_i2c.c)(0x5C9B1874)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_i2c.o --omf_browse .\objects\xcm32lxx_i2c.crf --depend .\objects\xcm32lxx_i2c.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_i2c.h)(0x5C9B1E64)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_lcd.c)(0x5C9B189E)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_lcd.o --omf_browse .\objects\xcm32lxx_lcd.crf --depend .\objects\xcm32lxx_lcd.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_lcd.h)(0x5C9B1E89)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_lvd.c)(0x5C9B18BE)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_lvd.o --omf_browse .\objects\xcm32lxx_lvd.crf --depend .\objects\xcm32lxx_lvd.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_lvd.h)(0x5C9B1EB0)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_misc.c)(0x5C9B1948)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_misc.o --omf_browse .\objects\xcm32lxx_misc.crf --depend .\objects\xcm32lxx_misc.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
F (..\xcm32lxx_lib\src\XCM32Lxx_pca.c)(0x5C9B1AD4)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_pca.o --omf_browse .\objects\xcm32lxx_pca.crf --depend .\objects\xcm32lxx_pca.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_pmu.c)(0x5C9B1974)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_pmu.o --omf_browse .\objects\xcm32lxx_pmu.crf --depend .\objects\xcm32lxx_pmu.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_port.c)(0x5C9B1992)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_port.o --omf_browse .\objects\xcm32lxx_port.crf --depend .\objects\xcm32lxx_port.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_ram.c)(0x5C9B19B0)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_ram.o --omf_browse .\objects\xcm32lxx_ram.crf --depend .\objects\xcm32lxx_ram.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_ram.h)(0x5C9B1FB7)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_rmu.c)(0x5C9B1ADB)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_rmu.o --omf_browse .\objects\xcm32lxx_rmu.crf --depend .\objects\xcm32lxx_rmu.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_rng.c)(0x5C9B1ADD)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_rng.o --omf_browse .\objects\xcm32lxx_rng.crf --depend .\objects\xcm32lxx_rng.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rng.h)(0x5C9B1FF4)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_rtc.c)(0x5C9B1AE0)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_rtc.o --omf_browse .\objects\xcm32lxx_rtc.crf --depend .\objects\xcm32lxx_rtc.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_spi.c)(0x5C9B1A2D)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_spi.o --omf_browse .\objects\xcm32lxx_spi.crf --depend .\objects\xcm32lxx_spi.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_spi.h)(0x5C9B203A)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_systick.c)(0x5C9B1A43)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_systick.o --omf_browse .\objects\xcm32lxx_systick.crf --depend .\objects\xcm32lxx_systick.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_timer.c)(0x5C9B1A5A)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_timer.o --omf_browse .\objects\xcm32lxx_timer.crf --depend .\objects\xcm32lxx_timer.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_uart.c)(0x5C9B1A73)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_uart.o --omf_browse .\objects\xcm32lxx_uart.crf --depend .\objects\xcm32lxx_uart.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_vc.c)(0x5C9B1A97)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_vc.o --omf_browse .\objects\xcm32lxx_vc.crf --depend .\objects\xcm32lxx_vc.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_vc.h)(0x5C9B20BD)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
F (..\xcm32lxx_lib\src\XCM32Lxx_wdt.c)(0x5C9B1AC6)(--c99 -c --cpu Cortex-M0+ -D__MICROLIB --li -g -O0 --apcs=interwork --split_sections -I ..\xcm32lxx_lib\core -I ..\xcm32lxx_lib\inc -I ..\User\cJSON -I ..\User

-I F:\code\xcm32l\fh_xcm32l_app\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Include

-I C:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\Device\ARM\ARMCM0plus\Include

-D__UVISION_VERSION="518" -D_RTE_ -DARMCM0P

-o .\objects\xcm32lxx_wdt.o --omf_browse .\objects\xcm32lxx_wdt.crf --depend .\objects\xcm32lxx_wdt.d)
I (..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h)(0x5C9B20DF)
I (..\xcm32lxx_lib\core\XCM32L.h)(0x60D936D1)
I (..\xcm32lxx_lib\core\core_cm0plus.h)(0x60C1AD09)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\xcm32lxx_lib\core\core_cmInstr.h)(0x62B038BC)
I (..\xcm32lxx_lib\core\core_cmFunc.h)(0x62B038BB)
I (..\xcm32lxx_lib\core\system_XCM32L.h)(0x60AFB176)
I (..\xcm32lxx_lib\inc\XCM32Lxx_conf.h)(0x60BBC89B)
I (..\xcm32lxx_lib\inc\XCM32Lxx_port.h)(0x5C9B1F94)
I (..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h)(0x5C9B1CF9)
I (..\xcm32lxx_lib\inc\XCM32Lxx_uart.h)(0x5C9B209F)
I (..\xcm32lxx_lib\inc\XCM32Lxx_timer.h)(0x5C9B207C)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pca.h)(0x5C9B1F30)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h)(0x5C9B2011)
I (..\xcm32lxx_lib\inc\XCM32Lxx_flash.h)(0x5C9B1E3E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h)(0x5C9B1F50)
I (..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h)(0x5C9B1FD6)
I (..\xcm32lxx_lib\inc\XCM32Lxx_systick.h)(0x5C9B205E)
I (..\xcm32lxx_lib\inc\XCM32Lxx_misc.h)(0x5C9B1EED)
