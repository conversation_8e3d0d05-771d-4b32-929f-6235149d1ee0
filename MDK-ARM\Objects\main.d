.\objects\main.o: ..\User\main.c
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_port.h
.\objects\main.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\main.o: ..\xcm32lxx_lib\core\core_cm0plus.h
.\objects\main.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\main.o: ..\xcm32lxx_lib\core\core_cmInstr.h
.\objects\main.o: ..\xcm32lxx_lib\core\core_cmFunc.h
.\objects\main.o: ..\xcm32lxx_lib\core\system_XCM32L.h
.\objects\main.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_uart.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_timer.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pca.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_flash.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_systick.h
.\objects\main.o: ..\xcm32lxx_lib\inc\XCM32Lxx_misc.h
.\objects\main.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\main.o: ..\User\common.h
.\objects\main.o: ..\User\gpio.h
.\objects\main.o: ..\User\uart.h
.\objects\main.o: ..\User\rtc.h
.\objects\main.o: ..\User\i2cUart.h
.\objects\main.o: ..\User\main.h
