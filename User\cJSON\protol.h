﻿#ifndef _PROTOL_H_
#define _PROTOL_H_


#define DEVICE_JSON      0xFF

#define DATA_TYPE_DATA	 		0x00
#define DATA_TYPE_DEBUG	 		0x01
#define DATA_TYPE_TRANSPORT		0x02
#define DATA_TYPE_MENGMU		0x03
#define DATA_TYPE_LOG		 	0xEE
#define DATA_TYPE_STM32_LOG		 0xDD

#define LED_DEFAULT      0
#define LED_ON           1
#define LED_OFF          2
#define LED_FLASH        3

#define UART_DATA_MAX_NUM		(10)
#define UART_RESULT_MAX_NUM		(10)



#define STM32_STATUS_IDLE						(1) //STOP STATUS
#define STM32_STATUS_FIRSTON					(2) //FIRST ON STATUS
#define STM32_STATUS_PERIOD_TRIG_4G_ON			(3)
#define STM32_STATUS_PERIOD_TRIG_MAINBOARD_ON	(4)
#define STM32_STATUS_RADAR_TRIG_SUPERWAVE_ON	(5)
#define STM32_STATUS_RADAR_TRIG_4G_ON			(6)
#define STM32_STATUS_RADAR_TRIG_MAINBOARD_ON	(7)
#define STM32_STATUS_CHECH_SUPERWAVE_DISTANCE	(8)
#define STM32_STATUS_RADAR_TRIG_BLOCK_ON		(9)
#define STM32_STATUS_RETRY		(10)  //相机发送失败，要重新拉起一次



void set_color_led_radar_value(int radar_status);

void json_create_voltage(float voltage);
int cJsonParseProtocol(char *buf);

#endif
