/**
 * @file    gpio_optimized.h
 * @brief   Optimized GPIO control module header file
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _GPIO_OPTIMIZED_H_
#define _GPIO_OPTIMIZED_H_

/* Includes ------------------------------------------------------------------*/
#include "XCM32Lxx_port.h"
#include "system_config.h"

/* Exported function prototypes ----------------------------------------------*/
/**
 * @brief  Initialize all GPIO pins
 * @param  None
 * @retval None
 */
void uf_GPIO_Init(void);

/**
 * @brief  Deinitialize all GPIO pins
 * @param  None
 * @retval None
 */
void uf_GPIO_DeInit(void);

/**
 * @brief  GPIO test function
 * @param  None
 * @retval 0 (never returns)
 */
int GPIO_test_main(void);

/**
 * @brief  Control LED state
 * @param  led_port: LED port
 * @param  led_pin: LED pin
 * @param  state: LED state (LED_ON, LED_OFF, LED_TOGGLE)
 * @retval None
 */
void GPIO_Control_LED(PORT_TypeDef* led_port, uint16_t led_pin, uint8_t state);

/**
 * @brief  Control power module state
 * @param  power_port: Power control port
 * @param  power_pin: Power control pin
 * @param  state: Power state (POWER_ON or POWER_OFF)
 * @retval None
 */
void GPIO_Control_Power(PORT_TypeDef* power_port, uint16_t power_pin, uint8_t state);

/**
 * @brief  Read GPIO pin state
 * @param  gpio_port: GPIO port
 * @param  gpio_pin: GPIO pin
 * @retval Pin state (Bit_SET or Bit_RESET)
 */
uint8_t GPIO_Read_Pin(PORT_TypeDef* gpio_port, uint16_t gpio_pin);

#endif /* _GPIO_OPTIMIZED_H_ */
