/**
  ******************************************************************************
  * @file    XCM32Lxx_conf.h 
  * <AUTHOR>
  * @version V1.0.5
  * @date    2018-11-25
  * @brief   Library configuration file.
  ******************************************************************************
  * @attention
  *
  * COPYRIGHT 2013-2018; Hangzhou GreenWhale technology co., LTD.
  *

  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __XCM32LXX_CONF_H
#define __XCM32LXX_CONF_H

/* Includes ------------------------------------------------------------------*/
/* Comment the line below to disable peripheral header file inclusion */
#include "XCM32Lxx_port.h"
#include "XCM32Lxx_cmu.h"
#include "XCM32Lxx_uart.h"
//#include "XCM32Lxx_i2c.h"
//#include "XCM32Lxx_dma.h"
//#include "XCM32Lxx_spi.h"
#include "XCM32Lxx_timer.h"
#include "XCM32Lxx_pca.h"
#include "XCM32Lxx_rtc.h"
#include "XCM32Lxx_wdt.h"
//#include "XCM32Lxx_adc.h"
//#include "XCM32Lxx_vc.h"
//#include "XCM32Lxx_lvd.h"
#include "XCM32Lxx_flash.h"
//#include "XCM32Lxx_ram.h"
//#include "XCM32Lxx_buzzer.h"
//#include "XCM32Lxx_crc.h"
//#include "XCM32Lxx_des.h"
//#include "XCM32Lxx_rng.h"
//#include "XCM32Lxx_lcd.h"
#include "XCM32Lxx_pmu.h"
#include "XCM32Lxx_rmu.h"
#include "XCM32Lxx_systick.h"
#include "XCM32Lxx_misc.h"

//
/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Uncomment the line below to expanse the "assert_param" macro in the 
   Standard Peripheral Library drivers code */

// #define USE_FULL_ASSERT    1

/* Exported macro ------------------------------------------------------------*/
#ifdef  USE_FULL_ASSERT

/**
  * @brief  The assert_param macro is used for function's parameters check.
  * @param  expr: If expr is false, it calls assert_failed function which reports 
  *         the name of the source file and the source line number of the call 
  *         that failed. If expr is true, it returns no value.
  * @retval None
  */
  #define assert_param(expr) ( (expr) ? (void)0 : assert_failed((uint8_t *)__FILE__, __LINE__) )
/* Exported functions ------------------------------------------------------- */
	void assert_failed(uint8_t* file, uint32_t line);
#else
  #define assert_param(expr) ((void)0)
#endif /* USE_FULL_ASSERT */

#endif /* __XCM32LXX__CONF_H */

/************************ (C) COPYRIGHT XCMTRI *****END OF FILE****/
