--cpu Cortex-M0+
".\objects\startup_xcm32l.o"
".\objects\system_xcm32l.o"
".\objects\cjson.o"
".\objects\protol.o"
".\objects\crc32.o"
".\objects\flash.o"
".\objects\gpio.o"
".\objects\i2cuart.o"
".\objects\main.o"
".\objects\rtc.o"
".\objects\uart.o"
".\objects\user_main.o"
".\objects\xcm32lxx_adc.o"
".\objects\xcm32lxx_buzzer.o"
".\objects\xcm32lxx_cmu.o"
".\objects\xcm32lxx_crc.o"
".\objects\xcm32lxx_des.o"
".\objects\xcm32lxx_dma.o"
".\objects\xcm32lxx_flash.o"
".\objects\xcm32lxx_i2c.o"
".\objects\xcm32lxx_lcd.o"
".\objects\xcm32lxx_lvd.o"
".\objects\xcm32lxx_misc.o"
".\objects\xcm32lxx_pca.o"
".\objects\xcm32lxx_pmu.o"
".\objects\xcm32lxx_port.o"
".\objects\xcm32lxx_ram.o"
".\objects\xcm32lxx_rmu.o"
".\objects\xcm32lxx_rng.o"
".\objects\xcm32lxx_rtc.o"
".\objects\xcm32lxx_spi.o"
".\objects\xcm32lxx_systick.o"
".\objects\xcm32lxx_timer.o"
".\objects\xcm32lxx_uart.o"
".\objects\xcm32lxx_vc.o"
".\objects\xcm32lxx_wdt.o"
--library_type=microlib --strict --scatter ".\Objects\xcm32l_app.sct"
--summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\xcm32l_app.map" -o .\Objects\xcm32l_app.axf