Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    startup_xcm32l.o(RESET) refers to startup_xcm32l.o(STACK) for __initial_sp
    startup_xcm32l.o(RESET) refers to startup_xcm32l.o(.text) for Reset_Handler
    startup_xcm32l.o(RESET) refers to main.o(i.SysTick_Handler) for SysTick_Handler
    startup_xcm32l.o(RESET) refers to uart.o(i.DMAC_IRQHandler) for DMAC_IRQHandler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART1_IRQHandler) for UART1_IRQHandler
    startup_xcm32l.o(RESET) refers to rtc.o(i.RTC_IRQHandler) for RTC_IRQHandler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART2_IRQHandler) for UART2_IRQHandler
    startup_xcm32l.o(RESET) refers to uart.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_xcm32l.o(RESET) refers to main.o(i.WDT_IRQHandler) for WDT_IRQHandler
    startup_xcm32l.o(.text) refers to system_xcm32l.o(i.SystemInit) for SystemInit
    startup_xcm32l.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_xcm32l.o(i.SystemCoreClockUpdate) refers to system_xcm32l.o(.data) for SystemCoreClock
    system_xcm32l.o(i.SystemInit) refers to system_xcm32l.o(i.SetSysClock) for SetSysClock
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    cjson.o(i.cJSON_AddItemToArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_CreateArray) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateBool) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateFalse) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateFloatArray) refers to f2d.o(.text) for __aeabi_f2d
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateIntArray) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateNull) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNumber) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNumber) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateObject) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateTrue) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_Delete) refers to cjson.o(.data) for cJSON_free
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_DetachItemFromArray) for cJSON_DetachItemFromArray
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_DetachItemFromObject) for cJSON_DetachItemFromObject
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_DetachItemFromArray) for cJSON_DetachItemFromArray
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_GetErrorPtr) refers to cjson.o(.data) for ep
    cjson.o(i.cJSON_GetObjectItem) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_InitHooks) refers to malloc.o(i.malloc) for malloc
    cjson.o(i.cJSON_InitHooks) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_InitHooks) refers to malloc.o(i.free) for free
    cjson.o(i.cJSON_InsertItemInArray) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    cjson.o(i.cJSON_New_Item) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_New_Item) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_Parse) refers to cjson.o(i.cJSON_ParseWithOpts) for cJSON_ParseWithOpts
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.skip) for skip
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(.data) for ep
    cjson.o(i.cJSON_Print) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_ReplaceItemInArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_strcasecmp) for cJSON_strcasecmp
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.cJSON_ReplaceItemInArray) for cJSON_ReplaceItemInArray
    cjson.o(i.cJSON_strcasecmp) refers to tolower.o(.text) for tolower
    cjson.o(i.cJSON_strdup) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_strdup) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_strdup) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.create_reference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.create_reference) refers to memcpya.o(.text) for __aeabi_memcpy4
    cjson.o(i.ensure) refers to cjson.o(i.pow2gt) for pow2gt
    cjson.o(i.ensure) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.ensure) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.parse_array) refers to cjson.o(i.skip) for skip
    cjson.o(i.parse_array) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_array) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_array) refers to cjson.o(.data) for ep
    cjson.o(i.parse_number) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.parse_number) refers to dmul.o(.text) for __aeabi_dmul
    cjson.o(i.parse_number) refers to dadd.o(.text) for __aeabi_dadd
    cjson.o(i.parse_number) refers to pow.o(i.pow) for pow
    cjson.o(i.parse_number) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.parse_object) refers to cjson.o(i.skip) for skip
    cjson.o(i.parse_object) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_object) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_object) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_object) refers to cjson.o(.data) for ep
    cjson.o(i.parse_string) refers to cjson.o(i.parse_hex4) for parse_hex4
    cjson.o(i.parse_string) refers to cjson.o(.data) for ep
    cjson.o(i.parse_string) refers to cjson.o(.constdata) for firstByteMark
    cjson.o(i.parse_value) refers to strncmp.o(.text) for strncmp
    cjson.o(i.parse_value) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_value) refers to cjson.o(i.parse_number) for parse_number
    cjson.o(i.parse_value) refers to cjson.o(i.parse_array) for parse_array
    cjson.o(i.parse_value) refers to cjson.o(i.parse_object) for parse_object
    cjson.o(i.parse_value) refers to cjson.o(.data) for ep
    cjson.o(i.print_array) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_array) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_array) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_array) refers to cjson.o(i.update) for update
    cjson.o(i.print_array) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print_array) refers to strlen.o(.text) for strlen
    cjson.o(i.print_array) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_array) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_number) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    cjson.o(i.print_number) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_number) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.print_number) refers to dadd.o(.text) for __aeabi_dsub
    cjson.o(i.print_number) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.print_number) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_number) refers to floor.o(i.floor) for floor
    cjson.o(i.print_number) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_object) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_object) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_object) refers to cjson.o(i.update) for update
    cjson.o(i.print_object) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_object) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print_object) refers to strlen.o(.text) for strlen
    cjson.o(i.print_object) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_object) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_object) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_string) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_string_ptr) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_string_ptr) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_string_ptr) refers to strchr.o(.text) for strchr
    cjson.o(i.print_string_ptr) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_string_ptr) refers to cjson.o(.data) for cJSON_malloc
    cjson.o(i.print_value) refers to cjson.o(i.__ARM_common_switch8) for __ARM_common_switch8
    cjson.o(i.print_value) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_value) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_value) refers to cjson.o(i.print_number) for print_number
    cjson.o(i.print_value) refers to cjson.o(i.print_string) for print_string
    cjson.o(i.print_value) refers to cjson.o(i.print_array) for print_array
    cjson.o(i.print_value) refers to cjson.o(i.print_object) for print_object
    cjson.o(i.print_value) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.update) refers to strlen.o(.text) for strlen
    cjson.o(.data) refers to malloc.o(i.malloc) for malloc
    cjson.o(.data) refers to malloc.o(i.free) for free
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Parse) for cJSON_Parse
    protol.o(i.cJsonParseProtocol) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_GetObjectItem) for cJSON_GetObjectItem
    protol.o(i.cJsonParseProtocol) refers to strcmp.o(.text) for strcmp
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.cJsonParseProtocol) refers to i2cuart.o(i.get_voltage) for get_voltage
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.cJsonParseProtocol) refers to dflti.o(.text) for __aeabi_i2d
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.cJsonParseProtocol) refers to f2d.o(.text) for __aeabi_f2d
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.cJsonParseProtocol) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.cJsonParseProtocol) refers to strlen.o(.text) for strlen
    protol.o(i.cJsonParseProtocol) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.cJsonParseProtocol) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.cJsonParseProtocol) refers to malloc.o(i.free) for free
    protol.o(i.cJsonParseProtocol) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.cJsonParseProtocol) refers to printfa.o(i.__0snprintf) for __2snprintf
    protol.o(i.cJsonParseProtocol) refers to main.o(i.Delay_ms) for Delay_ms
    protol.o(i.cJsonParseProtocol) refers to protol.o(i.send_transparent_info) for send_transparent_info
    protol.o(i.cJsonParseProtocol) refers to strncmp.o(.text) for strncmp
    protol.o(i.cJsonParseProtocol) refers to user_main.o(.data) for g_iVoltage
    protol.o(i.cJsonParseProtocol) refers to uart.o(.bss) for uart_proc_buf
    protol.o(i.cJsonParseProtocol) refers to protol.o(.data) for g_CurrentCarST
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.RTC_To_Sec) for RTC_To_Sec
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.t_HEX2BCD) for t_HEX2BCD
    protol.o(i.cJsonParseProtocol) refers to rtc.o(i.RTC_SetTime) for RTC_SetTime
    protol.o(i.cJsonParseProtocol) refers to user_main.o(i.enter_stop_mode) for enter_stop_mode
    protol.o(i.cJsonParseProtocol) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    protol.o(i.create_objects) refers to memcpya.o(.text) for __aeabi_memcpy4
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateFalse) for cJSON_CreateFalse
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.create_objects) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.create_objects) refers to malloc.o(i.free) for free
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateStringArray) for cJSON_CreateStringArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_CreateIntArray) for cJSON_CreateIntArray
    protol.o(i.create_objects) refers to cjson.o(i.cJSON_AddItemToArray) for cJSON_AddItemToArray
    protol.o(i.create_objects) refers to protol.o(.constdata) for .constdata
    protol.o(i.dofile) refers to malloc.o(i.malloc) for malloc
    protol.o(i.dofile) refers to fread.o(i.fread) for fread
    protol.o(i.dofile) refers to protol.o(i.doit) for doit
    protol.o(i.dofile) refers to malloc.o(i.free) for free
    protol.o(i.doit) refers to cjson.o(i.cJSON_Parse) for cJSON_Parse
    protol.o(i.doit) refers to cjson.o(i.cJSON_GetErrorPtr) for cJSON_GetErrorPtr
    protol.o(i.doit) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.doit) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.doit) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.doit) refers to malloc.o(i.free) for free
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.json_create_voltage) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.json_create_voltage) refers to f2d.o(.text) for __aeabi_f2d
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.json_create_voltage_new) refers to f2d.o(.text) for __aeabi_f2d
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.json_create_voltage_new) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.json_create_voltage_new) refers to strlen.o(.text) for strlen
    protol.o(i.json_create_voltage_new) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.json_create_voltage_new) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.json_create_voltage_new) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.json_create_voltage_new) refers to malloc.o(i.free) for free
    protol.o(i.json_create_voltage_new) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.json_create_voltage_new) refers to uart.o(.bss) for uart_proc_buf
    protol.o(i.main_cjson) refers to memcpya.o(.text) for __aeabi_memcpy4
    protol.o(i.main_cjson) refers to protol.o(i.doit) for doit
    protol.o(i.main_cjson) refers to protol.o(i.create_objects) for create_objects
    protol.o(i.main_cjson) refers to protol.o(.conststring) for .conststring
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.send_init_finish_info) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.send_init_finish_info) refers to strlen.o(.text) for strlen
    protol.o(i.send_init_finish_info) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.send_init_finish_info) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.send_init_finish_info) refers to malloc.o(i.free) for free
    protol.o(i.send_init_finish_info) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.send_init_finish_info) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.send_init_finish_info) refers to uart.o(.bss) for uart_proc_buf
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    protol.o(i.send_transparent_info) refers to printfa.o(i.__0printf) for __2printf
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_AddItemToObject) for cJSON_AddItemToObject
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    protol.o(i.send_transparent_info) refers to dflti.o(.text) for __aeabi_i2d
    protol.o(i.send_transparent_info) refers to dfltui.o(.text) for __aeabi_ui2d
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_Print) for cJSON_Print
    protol.o(i.send_transparent_info) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    protol.o(i.send_transparent_info) refers to memseta.o(.text) for __aeabi_memclr
    protol.o(i.send_transparent_info) refers to strlen.o(.text) for strlen
    protol.o(i.send_transparent_info) refers to uart.o(i.pack_data) for pack_data
    protol.o(i.send_transparent_info) refers to uart.o(i.uart2_com_tx_data) for uart2_com_tx_data
    protol.o(i.send_transparent_info) refers to malloc.o(i.free) for free
    protol.o(i.send_transparent_info) refers to user_main.o(.data) for g_trigDis
    protol.o(i.send_transparent_info) refers to protol.o(.data) for g_sendFaild
    protol.o(i.send_transparent_info) refers to user_main.o(.bss) for g_tmpval
    protol.o(i.send_transparent_info) refers to uart.o(.bss) for uart_proc_buf
    protol.o(.constdata) refers to protol.o(.conststring) for .conststring
    crc32.o(i.crc32) refers to crc32.o(.constdata) for crc_table
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_flash.o(i.FLASH_ReadWaitCycleCmd) for FLASH_ReadWaitCycleCmd
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_HSECmd) for CMU_HSECmd
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_SysClkConfig) for CMU_SysClkConfig
    flash.o(i.CMU_HSI2HSE) refers to xcm32lxx_cmu.o(i.CMU_HSIConfig) for CMU_HSIConfig
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVS) for FLASH_SetTNVS
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTPGS) for FLASH_SetTPGS
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTPROG) for FLASH_SetTPROG
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVH) for FLASH_SetTNVH
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTRCV) for FLASH_SetTRCV
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTERASE) for FLASH_SetTERASE
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTME) for FLASH_SetTME
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_SetTNVH1) for FLASH_SetTNVH1
    flash.o(i.Flash_test_main) refers to printfa.o(i.__0printf) for __2printf
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_UnLockPage) for FLASH_UnLockPage
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_ReadWord) for FLASH_ReadWord
    flash.o(i.Flash_test_main) refers to xcm32lxx_flash.o(i.FLASH_WriteWord) for FLASH_WriteWord
    flash.o(i.Flash_test_main) refers to flash.o(.conststring) for .conststring
    flash.o(i.Flash_test_main) refers to flash.o(.bss) for rBuffer
    gpio.o(i.GPIO_test_main) refers to gpio.o(i.uf_GPIO_Init) for uf_GPIO_Init
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    gpio.o(i.GPIO_test_main) refers to main.o(i.DelayMs) for DelayMs
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    gpio.o(i.GPIO_test_main) refers to xcm32lxx_port.o(i.PORT_ToggleBit) for PORT_ToggleBit
    gpio.o(i.uf_GPIO_DeInit) refers to xcm32lxx_port.o(i.PORT_DeInit) for PORT_DeInit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_DeInit) for PORT_DeInit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    gpio.o(i.uf_GPIO_Init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_Start) for I2C_Start
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_send_byte) for I2C_send_byte
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.Test_ACK) for Test_ACK
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.Master_ACK) for Master_ACK
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_read_byte) for I2C_read_byte
    i2cuart.o(i.I2C1_ReceiveData) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_Start) for I2C_Start
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_send_byte) for I2C_send_byte
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.Test_ACK) for Test_ACK
    i2cuart.o(i.I2C1_TransmitData) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.I2C_Start) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_Start) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_Start) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_Stop) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_Stop) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_Stop) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_init) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_read_byte) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_read_byte) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_read_byte) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.I2C_read_byte) refers to xcm32lxx_port.o(i.PORT_ReadInputDataBit) for PORT_ReadInputDataBit
    i2cuart.o(i.I2C_send_byte) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.I2C_send_byte) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.I2C_send_byte) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Master_ACK) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.Master_ACK) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.Master_ACK) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.PORT_DirSet) for PORT_DirSet
    i2cuart.o(i.Test_ACK) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.Test_ACK) refers to xcm32lxx_port.o(i.PORT_ReadInputDataBit) for PORT_ReadInputDataBit
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.delay_5us) for delay_5us
    i2cuart.o(i.Test_ACK) refers to i2cuart.o(i.I2C_Stop) for I2C_Stop
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.get_voltage) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    i2cuart.o(i.get_voltage) refers to dfltui.o(.text) for __aeabi_ui2d
    i2cuart.o(i.get_voltage) refers to dmul.o(.text) for __aeabi_dmul
    i2cuart.o(i.get_voltage) refers to ddiv.o(.text) for __aeabi_ddiv
    i2cuart.o(i.get_voltage) refers to dadd.o(.text) for __aeabi_dadd
    i2cuart.o(i.get_voltage) refers to d2f.o(.text) for __aeabi_d2f
    i2cuart.o(i.get_voltage) refers to i2cuart.o(.data) for P1
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    i2cuart.o(i.i2c_get_Voltage_test) refers to dfltui.o(.text) for __aeabi_ui2d
    i2cuart.o(i.i2c_get_Voltage_test) refers to dmul.o(.text) for __aeabi_dmul
    i2cuart.o(i.i2c_get_Voltage_test) refers to ddiv.o(.text) for __aeabi_ddiv
    i2cuart.o(i.i2c_get_Voltage_test) refers to dadd.o(.text) for __aeabi_dadd
    i2cuart.o(i.i2c_get_Voltage_test) refers to d2f.o(.text) for __aeabi_d2f
    i2cuart.o(i.i2c_get_Voltage_test) refers to f2d.o(.text) for __aeabi_f2d
    i2cuart.o(i.i2c_get_Voltage_test) refers to printfa.o(i.__0printf) for __2printf
    i2cuart.o(i.i2c_get_Voltage_test) refers to i2cuart.o(.data) for P1
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C_init) for I2C_init
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C1_TransmitData) for I2C1_TransmitData
    i2cuart.o(i.test111) refers to i2cuart.o(i.delay) for delay
    i2cuart.o(i.test111) refers to i2cuart.o(i.I2C1_ReceiveData) for I2C1_ReceiveData
    main.o(i.Booster_DeInit) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    main.o(i.Booster_DeInit) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    main.o(i.Booster_DeInit) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.Booster_DeInit) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.Booster_DeInit) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Booster_DeInit) refers to user_main.o(.data) for g_Time
    main.o(i.Booster_DeInit) refers to main.o(.constdata) for __FUNCTION__
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    main.o(i.Booster_Init) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.Booster_Init) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.Booster_Init) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Booster_Init) refers to user_main.o(.data) for g_Time
    main.o(i.Booster_Init) refers to main.o(.constdata) for __FUNCTION__
    main.o(i.Booster_Start) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    main.o(i.Booster_Start) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    main.o(i.Booster_Start) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.Booster_Start) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.Booster_Start) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Booster_Start) refers to user_main.o(.data) for g_Time
    main.o(i.Booster_Start) refers to main.o(.constdata) for __FUNCTION__
    main.o(i.Booster_Stop) refers to xcm32lxx_port.o(i.PORT_StructInit) for PORT_StructInit
    main.o(i.Booster_Stop) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    main.o(i.Booster_Stop) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    main.o(i.Booster_Stop) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.Booster_Stop) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Booster_Stop) refers to user_main.o(.data) for g_Time
    main.o(i.Booster_Stop) refers to main.o(.constdata) for __FUNCTION__
    main.o(i.Delay_ms) refers to main.o(i.GetSysTick) for GetSysTick
    main.o(i.GetSysTick) refers to main.o(.data) for uiSysTickCnt
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_SetReloadValue) for SysTick_SetReloadValue
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_ITConfig) for SysTick_ITConfig
    main.o(i.SysTickConfigure) refers to xcm32lxx_systick.o(i.SysTick_Cmd) for SysTick_Cmd
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_SetPriority) for NVIC_SetPriority
    main.o(i.SysTickConfigure) refers to main.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    main.o(i.SysTick_Handler) refers to main.o(.data) for uiSysTickCnt
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_DeInit) for TIMER_DeInit
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_StructInit) for TIMER_StructInit
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_Init) for TIMER_Init
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter0) for TIMER_SetLoadCounter0
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter1) for TIMER_SetLoadCounter1
    main.o(i.Timer1_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_Cmd) for TIMER_Cmd
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_DeInit) for TIMER_DeInit
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_StructInit) for TIMER_StructInit
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_Init) for TIMER_Init
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter0) for TIMER_SetLoadCounter0
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter1) for TIMER_SetLoadCounter1
    main.o(i.Timer2_PwmOut_Init) refers to xcm32lxx_timer.o(i.TIMER_Cmd) for TIMER_Cmd
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_LSIConfig) for CMU_LSIConfig
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_WDTCLKConfig) for CMU_WDTCLKConfig
    main.o(i.WDT_Configure) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_DeInit) for WDT_DeInit
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_StructInit) for WDT_StructInit
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_Init) for WDT_Init
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_SetPriority) for NVIC_SetPriority
    main.o(i.WDT_Configure) refers to main.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_Cmd) for WDT_Cmd
    main.o(i.WDT_Configure) refers to xcm32lxx_wdt.o(i.WDT_RestartCmd) for WDT_RestartCmd
    main.o(i.WDT_DeConfigure) refers to xcm32lxx_wdt.o(i.WDT_Cmd) for WDT_Cmd
    main.o(i.WDT_DeConfigure) refers to main.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    main.o(i.WDT_DeConfigure) refers to main.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    main.o(i.WDT_DeConfigure) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    main.o(i.WDT_DeConfigure) refers to xcm32lxx_wdt.o(i.WDT_DeInit) for WDT_DeInit
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_GetITStatus) for WDT_GetITStatus
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_ClearITFlag) for WDT_ClearITFlag
    main.o(i.WDT_IRQHandler) refers to xcm32lxx_wdt.o(i.WDT_RestartCmd) for WDT_RestartCmd
    main.o(i.fputc) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    main.o(i.fputc) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_HSIConfig) for CMU_HSIConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_SysClkConfig) for CMU_SysClkConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_HCLKConfig) for CMU_HCLKConfig
    main.o(i.main) refers to xcm32lxx_cmu.o(i.CMU_PCLKConfig) for CMU_PCLKConfig
    main.o(i.main) refers to gpio.o(i.uf_GPIO_Init) for uf_GPIO_Init
    main.o(i.main) refers to uart.o(i.UART4_Init) for UART4_Init
    main.o(i.main) refers to uart.o(i.UART2_Init) for UART2_Init
    main.o(i.main) refers to uart.o(i.UART1_Init) for UART1_Init
    main.o(i.main) refers to rtc.o(i.RTC_Run_Init) for RTC_Run_Init
    main.o(i.main) refers to main.o(i.SysTickConfigure) for SysTickConfigure
    main.o(i.main) refers to main.o(i.WDT_Configure) for WDT_Configure
    main.o(i.main) refers to main.o(i.Timer2_PwmOut_Init) for Timer2_PwmOut_Init
    main.o(i.main) refers to main.o(i.Timer1_PwmOut_Init) for Timer1_PwmOut_Init
    main.o(i.main) refers to user_main.o(i.Switch_4G_Off) for Switch_4G_Off
    main.o(i.main) refers to user_main.o(i.SnapModule_Off) for SnapModule_Off
    main.o(i.main) refers to user_main.o(i.RadarTurnOff) for RadarTurnOff
    main.o(i.main) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to main.o(i.Booster_Start) for Booster_Start
    main.o(i.main) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    main.o(i.main) refers to user_main.o(i.user_main) for user_main
    main.o(i.main) refers to user_main.o(.data) for g_Time
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetYear) for RTC_GetYear
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetMonth) for RTC_GetMonth
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetDay) for RTC_GetDay
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetHour) for RTC_GetHour
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetMinute) for RTC_GetMinute
    rtc.o(i.GetRtcSecond) refers to xcm32lxx_rtc.o(i.RTC_GetSecond) for RTC_GetSecond
    rtc.o(i.GetRtcSecond) refers to rtc.o(i.t_BCD2HEX) for t_BCD2HEX
    rtc.o(i.GetRtcSecond) refers to rtc.o(i.RTC_To_Sec) for RTC_To_Sec
    rtc.o(i.GetRtcSecond) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.GetRtcSecond) refers to rtc.o(.data) for YEAR
    rtc.o(i.Is_Leap_Year) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.NVIC_Init_rtc) refers to rtc.o(i.NVIC_SetPriority) for NVIC_SetPriority
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetYearLSB) for RTC_GetYearLSB
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetMonth) for RTC_GetMonth
    rtc.o(i.RTC_GetWeek) refers to xcm32lxx_rtc.o(i.RTC_GetDay) for RTC_GetDay
    rtc.o(i.RTC_GetWeek) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.RTC_IRQHandler) refers to xcm32lxx_rtc.o(i.RTC_GetITStatus) for RTC_GetITStatus
    rtc.o(i.RTC_IRQHandler) refers to xcm32lxx_rtc.o(i.RTC_ClearITFlag) for RTC_ClearITFlag
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_GPIO_CMU_Init) for uf_GPIO_CMU_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_GPIO_RTC_Init) for uf_GPIO_RTC_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.uf_RTC_Init) for uf_RTC_Init
    rtc.o(i.RTC_Run_Init) refers to rtc.o(i.NVIC_Init_rtc) for NVIC_Init_rtc
    rtc.o(i.RTC_Run_Init) refers to xcm32lxx_rtc.o(i.RTC_Cmd) for RTC_Cmd
    rtc.o(i.RTC_Run_Init) refers to main.o(i.DelayMs) for DelayMs
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_DeInit) for RTC_DeInit
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_StructInit) for RTC_StructInit
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_Init) for RTC_Init
    rtc.o(i.RTC_SetTime) refers to rtc.o(i.NVIC_Init_rtc) for NVIC_Init_rtc
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_ITConfig) for RTC_ITConfig
    rtc.o(i.RTC_SetTime) refers to xcm32lxx_rtc.o(i.RTC_Cmd) for RTC_Cmd
    rtc.o(i.RTC_To_Sec) refers to rtc.o(i.Is_Leap_Year) for Is_Leap_Year
    rtc.o(i.RTC_To_Sec) refers to rtc.o(.constdata) for mon_table
    rtc.o(i.t_HEX2BCD) refers to idiv.o(.text) for __aeabi_idivmod
    rtc.o(i.uf_GPIO_CMU_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_CMU_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_RTC_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    rtc.o(i.uf_GPIO_UART_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_LSEConfig) for CMU_LSEConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp) for CMU_WaitForSysClkStartUp
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_RTCCLKConfig) for CMU_RTCCLKConfig
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_DeInit) for RTC_DeInit
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_StructInit) for RTC_StructInit
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_Init) for RTC_Init
    rtc.o(i.uf_RTC_Init) refers to xcm32lxx_rtc.o(i.RTC_ITConfig) for RTC_ITConfig
    rtc.o(i.uf_RTC_Init) refers to rtc.o(.data) for RTCTime
    uart.o(i.DMAC_IRQHandler) refers to xcm32lxx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    uart.o(i.DMAC_IRQHandler) refers to xcm32lxx_dma.o(i.DMA_ClearITFlag) for DMA_ClearITFlag
    uart.o(i.DMAC_IRQHandler) refers to xcm32lxx_port.o(i.PORT_ToggleBit) for PORT_ToggleBit
    uart.o(i.DMAC_IRQHandler) refers to printfa.o(i.__0printf) for __2printf
    uart.o(i.DMAC_IRQHandler) refers to xcm32lxx_dma.o(i.DMA_Cmd) for DMA_Cmd
    uart.o(i.UART1_DeInit) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART1_DeInit) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART1_DeInit) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART1_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART1_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART1_IRQHandler) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.UART1_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.UART1_IRQHandler) refers to uart.o(.bss) for RxWritePos
    uart.o(i.UART1_IRQ_SET) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART1_IRQ_SET) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART1_IRQ_SET) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.UART1_IRQ_SET) refers to uart.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    uart.o(i.UART1_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART1_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART1_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART1_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_FIFOModeConfig) for UART_FIFOModeConfig
    uart.o(i.UART1_Init) refers to uart.o(i.uf_DMA_UartRxReqInit) for uf_DMA_UartRxReqInit
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.UART1_Init) refers to uart.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    uart.o(i.UART1_Init) refers to xcm32lxx_dma.o(i.DMA_Cmd) for DMA_Cmd
    uart.o(i.UART1_Init) refers to xcm32lxx_uart.o(i.UART_PTXREModeConfig) for UART_PTXREModeConfig
    uart.o(i.UART1_Init) refers to uart.o(.bss) for Uart1_RxBuf
    uart.o(i.UART2_DeInit) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART2_DeInit) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART2_DeInit) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART2_IRQHandler) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.UART2_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.UART2_IRQHandler) refers to uart.o(.bss) for RxWritePos
    uart.o(i.UART2_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART2_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART2_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART2_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_FIFOModeConfig) for UART_FIFOModeConfig
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_PTXREModeConfig) for UART_PTXREModeConfig
    uart.o(i.UART2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.UART2_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.UART2_Init) refers to uart.o(i.NVIC_EnableIRQ) for NVIC_EnableIRQ
    uart.o(i.UART2_Init) refers to uart.o(.bss) for uart2_RxBuf
    uart.o(i.UART4_DeInit) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetITStatus) for UART_GetITStatus
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART4_IRQHandler) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.UART4_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd) for CMU_APBPeriph1ClockCmd
    uart.o(i.UART4_Init) refers to xcm32lxx_port.o(i.PORT_Init) for PORT_Init
    uart.o(i.UART4_Init) refers to xcm32lxx_port.o(i.PORT_PinAFConfig) for PORT_PinAFConfig
    uart.o(i.UART4_Init) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_DeInit) for UART_DeInit
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_ITConfig) for UART_ITConfig
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_StructInit) for UART_StructInit
    uart.o(i.UART4_Init) refers to xcm32lxx_uart.o(i.UART_Init) for UART_Init
    uart.o(i.UART4_Init) refers to uart.o(i.NVIC_DisableIRQ) for NVIC_DisableIRQ
    uart.o(i.UART4_Init) refers to uart.o(i.NVIC_ClearPendingIRQ) for NVIC_ClearPendingIRQ
    uart.o(i.UART4_Init) refers to uart.o(i.NVIC_SetPriority) for NVIC_SetPriority
    uart.o(i.pack_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_data) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.pack_data) refers to uart.o(.data) for s_seq
    uart.o(i.pack_luna_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_luna_data) refers to uart.o(.data) for s_seq
    uart.o(i.pack_print_data) refers to crc32.o(i.crc32) for crc32
    uart.o(i.pack_print_data) refers to uart.o(.data) for s_seq
    uart.o(i.uart1_com_Receive) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.uart1_com_Receive) refers to xcm32lxx_uart.o(i.UART_ReceiveData) for UART_ReceiveData
    uart.o(i.uart1_com_Receive) refers to main.o(i.Delay_ms) for Delay_ms
    uart.o(i.uart1_com_tx_data) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.uart1_com_tx_data) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.uart1_rx_mengmu_process) refers to uldiv.o(.text) for __aeabi_uldivmod
    uart.o(i.uart1_rx_mengmu_process) refers to uart.o(.bss) for RxReadPos
    uart.o(i.uart2_com_tx_data) refers to main.o(i.GetSysTick) for GetSysTick
    uart.o(i.uart2_com_tx_data) refers to xcm32lxx_uart.o(i.UART_GetLineStatus) for UART_GetLineStatus
    uart.o(i.uart2_com_tx_data) refers to xcm32lxx_uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.uart2_find_head_and_tail) refers to ldiv.o(.text) for __aeabi_ldivmod
    uart.o(i.uart2_rx_process) refers to uart.o(i.uart2_find_head_and_tail) for uart2_find_head_and_tail
    uart.o(i.uart2_rx_process) refers to ldiv.o(.text) for __aeabi_ldivmod
    uart.o(i.uart2_rx_process) refers to uart.o(i.uart2_unpack_data) for uart2_unpack_data
    uart.o(i.uart2_rx_process) refers to uart.o(.bss) for RxReadPos
    uart.o(i.uart2_unpack_data) refers to printfa.o(i.__0printf) for __2printf
    uart.o(i.uart2_unpack_data) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_dma.o(i.DMA_DeInit) for DMA_DeInit
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_dma.o(i.DMA_StructInit) for DMA_StructInit
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_dma.o(i.DMA_Init) for DMA_Init
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    uart.o(i.uf_DMA_UartRxReqInit) refers to xcm32lxx_dma.o(i.DMA_Cmd) for DMA_Cmd
    uart.o(i.uf_DMA_UartRxReqInit) refers to uart.o(.bss) for Uart1_RxBuf
    uart.o(i.uf_DMA_UartTxReqInit) refers to xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd) for CMU_APBPeriph0ClockCmd
    uart.o(i.uf_DMA_UartTxReqInit) refers to xcm32lxx_dma.o(i.DMA_StructInit) for DMA_StructInit
    uart.o(i.uf_DMA_UartTxReqInit) refers to xcm32lxx_dma.o(i.DMA_Init) for DMA_Init
    uart.o(i.uf_DMA_UartTxReqInit) refers to xcm32lxx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    uart.o(i.uf_DMA_UartTxReqInit) refers to xcm32lxx_dma.o(i.DMA_Cmd) for DMA_Cmd
    uart.o(i.uf_DMA_UartTxReqInit) refers to uart.o(.bss) for Uart1_RxBuf
    user_main.o(i.MengMuRadarReceive) refers to uart.o(i.uart1_rx_mengmu_process) for uart1_rx_mengmu_process
    user_main.o(i.MengMuRadarReceive) refers to user_main.o(i.RadarTurnOff) for RadarTurnOff
    user_main.o(i.MengMuRadarReceive) refers to main.o(i.Booster_DeInit) for Booster_DeInit
    user_main.o(i.MengMuRadarReceive) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.MengMuRadarReceive) refers to user_main.o(.data) for g_distance
    user_main.o(i.MengMuRadarReceive) refers to user_main.o(.bss) for g_XijieRadarInfo
    user_main.o(i.RadarTurnOff) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.RadarTurnOff) refers to main.o(i.GetSysTick) for GetSysTick
    user_main.o(i.RadarTurnOff) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.RadarTurnOff) refers to user_main.o(.data) for g_UltraSonicOn
    user_main.o(i.RadarTurnOn) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.RadarTurnOn) refers to main.o(i.Delay_ms) for Delay_ms
    user_main.o(i.RadarTurnOn) refers to main.o(i.GetSysTick) for GetSysTick
    user_main.o(i.RadarTurnOn) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.RadarTurnOn) refers to user_main.o(.data) for g_UltraSonicOn
    user_main.o(i.SnapModule_Off) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.SnapModule_Off) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.SnapModule_Off) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter1) for TIMER_SetLoadCounter1
    user_main.o(i.SnapModule_Off) refers to user_main.o(.data) for g_hi3516on
    user_main.o(i.SnapModule_On) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.SnapModule_On) refers to xcm32lxx_rtc.o(i.RTC_GetHour) for RTC_GetHour
    user_main.o(i.SnapModule_On) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter1) for TIMER_SetLoadCounter1
    user_main.o(i.SnapModule_On) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.SnapModule_On) refers to user_main.o(.data) for g_hi3516on
    user_main.o(i.Switch_4G_Off) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.Switch_4G_Off) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.Switch_4G_Off) refers to user_main.o(.data) for g_4GOn
    user_main.o(i.Switch_4G_On) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.Switch_4G_On) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.Switch_4G_On) refers to user_main.o(.data) for g_4GOn
    user_main.o(i.do_production_test_proc) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.do_production_test_proc) refers to main.o(i.Delay_ms) for Delay_ms
    user_main.o(i.do_production_test_proc) refers to xcm32lxx_timer.o(i.TIMER_SetLoadCounter1) for TIMER_SetLoadCounter1
    user_main.o(i.enter_stop_mode) refers to user_main.o(i.SnapModule_Off) for SnapModule_Off
    user_main.o(i.enter_stop_mode) refers to user_main.o(i.Switch_4G_Off) for Switch_4G_Off
    user_main.o(i.enter_stop_mode) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.enter_stop_mode) refers to main.o(i.Booster_Stop) for Booster_Stop
    user_main.o(i.enter_stop_mode) refers to main.o(i.Delay_ms) for Delay_ms
    user_main.o(i.enter_stop_mode) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.enter_stop_mode) refers to user_main.o(.data) for g_Status
    user_main.o(i.enter_stop_mode) refers to protol.o(.data) for g_HaveCar
    user_main.o(i.startSnap) refers to main.o(i.Delay_ms) for Delay_ms
    user_main.o(i.startSnap) refers to main.o(i.Booster_Start) for Booster_Start
    user_main.o(i.startSnap) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.startSnap) refers to user_main.o(i.Switch_4G_On) for Switch_4G_On
    user_main.o(i.startSnap) refers to user_main.o(i.SnapModule_On) for SnapModule_On
    user_main.o(i.startSnap) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.startSnap) refers to user_main.o(.data) for g_trigCnt
    user_main.o(i.user_main) refers to rtc.o(i.GetRtcSecond) for GetRtcSecond
    user_main.o(i.user_main) refers to xcm32lxx_cmu.o(i.CMU_GetSysClkSource) for CMU_GetSysClkSource
    user_main.o(i.user_main) refers to xcm32lxx_cmu.o(i.CMU_GetSysClkFreq) for CMU_GetSysClkFreq
    user_main.o(i.user_main) refers to printfa.o(i.__0printf) for __2printf
    user_main.o(i.user_main) refers to xcm32lxx_port.o(i.PORT_WriteBit) for PORT_WriteBit
    user_main.o(i.user_main) refers to user_main.o(i.do_production_test_proc) for do_production_test_proc
    user_main.o(i.user_main) refers to user_main.o(i.Switch_4G_On) for Switch_4G_On
    user_main.o(i.user_main) refers to main.o(i.Delay_ms) for Delay_ms
    user_main.o(i.user_main) refers to user_main.o(i.SnapModule_On) for SnapModule_On
    user_main.o(i.user_main) refers to i2cuart.o(i.get_voltage) for get_voltage
    user_main.o(i.user_main) refers to memseta.o(.text) for __aeabi_memclr4
    user_main.o(i.user_main) refers to siabs.o(.text) for abs
    user_main.o(i.user_main) refers to f2d.o(.text) for __aeabi_f2d
    user_main.o(i.user_main) refers to main.o(i.Booster_Start) for Booster_Start
    user_main.o(i.user_main) refers to main.o(i.Booster_Init) for Booster_Init
    user_main.o(i.user_main) refers to user_main.o(i.RadarTurnOn) for RadarTurnOn
    user_main.o(i.user_main) refers to user_main.o(i.RadarTurnOff) for RadarTurnOff
    user_main.o(i.user_main) refers to main.o(i.Booster_DeInit) for Booster_DeInit
    user_main.o(i.user_main) refers to user_main.o(i.MengMuRadarReceive) for MengMuRadarReceive
    user_main.o(i.user_main) refers to user_main.o(.data) for g_Time
    user_main.o(i.user_main) refers to protol.o(.data) for startUpPeriod
    user_main.o(i.user_main) refers to user_main.o(.bss) for g_XijieRadarInfo
    user_main.o(i.user_main) refers to uart.o(.bss) for RxWritePos
    user_main.o(i.user_main) refers to idiv.o(.text) for __aeabi_idivmod
    user_main.o(i.user_main) refers to user_main.o(i.startSnap) for startSnap
    user_main.o(i.user_main) refers to uart.o(i.uart2_rx_process) for uart2_rx_process
    user_main.o(i.user_main) refers to protol.o(i.cJsonParseProtocol) for cJsonParseProtocol
    user_main.o(i.user_main) refers to user_main.o(i.enter_stop_mode) for enter_stop_mode
    user_main.o(i.user_main) refers to xcm32lxx_pmu.o(i.PMU_EnterDeepSleep3Mode) for PMU_EnterDeepSleep3Mode
    user_main.o(.data) refers to user_main.o(.conststring) for .conststring
    xcm32lxx_port.o(i.PORT_ReadOutputData) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ReadOutputDataBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ResetBits) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_SetBits) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_ToggleBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_Write) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_port.o(i.PORT_WriteBit) refers to xcm32lxx_port.o(.data) for gvPortOut
    xcm32lxx_uart.o(i.UART_Init) refers to ffltui.o(.text) for __aeabi_ui2f
    xcm32lxx_uart.o(i.UART_Init) refers to fdiv.o(.text) for __aeabi_fdiv
    xcm32lxx_uart.o(i.UART_Init) refers to f2d.o(.text) for __aeabi_f2d
    xcm32lxx_uart.o(i.UART_Init) refers to dadd.o(.text) for __aeabi_dadd
    xcm32lxx_uart.o(i.UART_Init) refers to dfixui.o(.text) for __aeabi_d2uiz
    xcm32lxx_uart.o(i.UART_Init) refers to ffixui.o(.text) for __aeabi_f2uiz
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.floor) for floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    fread.o(i.fread) refers to fgetc.o(.text) for fgetc
    fread.o(i.fread) refers to uidiv.o(.text) for __aeabi_uidivmod
    idiv.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_xcm32l.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_xcm32l.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_xcm32l.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fgetc.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_u.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_u.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_u.o(.text) refers to fgetc_u.o(.data) for .data
    fgetc_u.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_u.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_b.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_b.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_b.o(.text) refers to fgetc_b.o(.data) for .data
    fgetc_b.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_b.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_ub.o(.text) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_ub.o(.text) refers (Special) to semi.o(.text) for __semihosting_library_function
    fgetc_ub.o(.text) refers to fgetc_ub.o(.data) for .data
    fgetc_ub.o(.data) refers (Special) to iusesemig.o(.text) for __I$use$semihosting$fgetc
    fgetc_ub.o(.data) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing system_xcm32l.o(.rev16_text), (4 bytes).
    Removing system_xcm32l.o(.revsh_text), (4 bytes).
    Removing system_xcm32l.o(i.SystemCoreClockUpdate), (92 bytes).
    Removing system_xcm32l.o(.data), (4 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToArray), (24 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToObject), (28 bytes).
    Removing cjson.o(i.cJSON_AddItemToObjectCS), (68 bytes).
    Removing cjson.o(i.cJSON_CreateArray), (20 bytes).
    Removing cjson.o(i.cJSON_CreateBool), (30 bytes).
    Removing cjson.o(i.cJSON_CreateDoubleArray), (72 bytes).
    Removing cjson.o(i.cJSON_CreateFalse), (20 bytes).
    Removing cjson.o(i.cJSON_CreateFloatArray), (76 bytes).
    Removing cjson.o(i.cJSON_CreateIntArray), (76 bytes).
    Removing cjson.o(i.cJSON_CreateNull), (20 bytes).
    Removing cjson.o(i.cJSON_CreateStringArray), (66 bytes).
    Removing cjson.o(i.cJSON_CreateTrue), (20 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromArray), (22 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObject), (22 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromArray), (74 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObject), (50 bytes).
    Removing cjson.o(i.cJSON_Duplicate), (180 bytes).
    Removing cjson.o(i.cJSON_GetArrayItem), (20 bytes).
    Removing cjson.o(i.cJSON_GetArraySize), (18 bytes).
    Removing cjson.o(i.cJSON_GetErrorPtr), (12 bytes).
    Removing cjson.o(i.cJSON_InitHooks), (72 bytes).
    Removing cjson.o(i.cJSON_InsertItemInArray), (64 bytes).
    Removing cjson.o(i.cJSON_Minify), (190 bytes).
    Removing cjson.o(i.cJSON_PrintBuffered), (44 bytes).
    Removing cjson.o(i.cJSON_PrintUnformatted), (18 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInArray), (78 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObject), (56 bytes).
    Removing cjson.o(i.create_reference), (50 bytes).
    Removing protol.o(.rev16_text), (4 bytes).
    Removing protol.o(.revsh_text), (4 bytes).
    Removing protol.o(i.create_objects), (1116 bytes).
    Removing protol.o(i.dofile), (88 bytes).
    Removing protol.o(i.doit), (88 bytes).
    Removing protol.o(i.json_create_voltage), (184 bytes).
    Removing protol.o(i.json_create_voltage_new), (340 bytes).
    Removing protol.o(i.main_cjson), (40 bytes).
    Removing protol.o(i.send_init_finish_info), (164 bytes).
    Removing protol.o(.constdata), (176 bytes).
    Removing protol.o(.conststring), (527 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(i.CMU_HSI2HSE), (50 bytes).
    Removing flash.o(i.Flash_test_main), (672 bytes).
    Removing flash.o(.bss), (2048 bytes).
    Removing flash.o(.conststring), (140 bytes).
    Removing flash.o(.data), (8 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(i.GPIO_test_main), (144 bytes).
    Removing gpio.o(i.uf_GPIO_DeInit), (8 bytes).
    Removing i2cuart.o(.rev16_text), (4 bytes).
    Removing i2cuart.o(.revsh_text), (4 bytes).
    Removing i2cuart.o(i.i2c_get_Voltage_test), (244 bytes).
    Removing i2cuart.o(i.test111), (38 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(i.PMU_EnterDeepSleep5Mode), (32 bytes).
    Removing main.o(i.WDT_DeConfigure), (38 bytes).
    Removing main.o(i.getLog), (2 bytes).
    Removing main.o(i.logWrite), (2 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.RTC_GetWeek), (104 bytes).
    Removing rtc.o(i.uf_GPIO_UART_Init), (88 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(i.UART1_DeInit), (28 bytes).
    Removing uart.o(i.UART1_IRQ_SET), (52 bytes).
    Removing uart.o(i.UART2_DeInit), (32 bytes).
    Removing uart.o(i.UART4_DeInit), (16 bytes).
    Removing uart.o(i.pack_luna_data), (164 bytes).
    Removing uart.o(i.pack_print_data), (136 bytes).
    Removing uart.o(i.uart1_com_Receive), (98 bytes).
    Removing uart.o(i.uart1_com_tx_data), (48 bytes).
    Removing uart.o(i.uf_DMA_UartTxReqInit), (112 bytes).
    Removing user_main.o(.rev16_text), (4 bytes).
    Removing user_main.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_adc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_adc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_adc.o(i.ADC_BGRCmd), (76 bytes).
    Removing xcm32lxx_adc.o(i.ADC_ClearITFlag), (20 bytes).
    Removing xcm32lxx_adc.o(i.ADC_ClearResultAcc), (40 bytes).
    Removing xcm32lxx_adc.o(i.ADC_Cmd), (76 bytes).
    Removing xcm32lxx_adc.o(i.ADC_DeInit), (48 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetConvCompletedFlagStatus), (24 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing xcm32lxx_adc.o(i.ADC_GetResult), (36 bytes).
    Removing xcm32lxx_adc.o(i.ADC_Init), (268 bytes).
    Removing xcm32lxx_adc.o(i.ADC_InterSignalSourceVCCConfig), (112 bytes).
    Removing xcm32lxx_adc.o(i.ADC_PeriphReflectTrigConvCmd), (40 bytes).
    Removing xcm32lxx_adc.o(i.ADC_SetContinuousSampleCounter), (32 bytes).
    Removing xcm32lxx_adc.o(i.ADC_SoftwareStartConvCmd), (36 bytes).
    Removing xcm32lxx_adc.o(i.ADC_StructInit), (24 bytes).
    Removing xcm32lxx_adc.o(i.ADC_TempSensorCmd), (76 bytes).
    Removing xcm32lxx_buzzer.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_buzzer.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_Cmd), (52 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_DeInit), (48 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_Init), (76 bytes).
    Removing xcm32lxx_buzzer.o(i.BUZZER_StructInit), (8 bytes).
    Removing xcm32lxx_cmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_cmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_BASETMCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetHCLKFreq), (88 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_GetPCLKFreq), (104 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_HSECmd), (96 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_IRC40KConfig), (68 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LCDCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LPUARTCLKConfig), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_LockUpCmd), (44 bytes).
    Removing xcm32lxx_cmu.o(i.CMU_RAM_PARCmd), (44 bytes).
    Removing xcm32lxx_crc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_crc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_crc.o(i.CRC_DeInit), (48 bytes).
    Removing xcm32lxx_crc.o(i.CRC_GetCRCFlagStatus), (20 bytes).
    Removing xcm32lxx_crc.o(i.CRC_GetCRCResult), (32 bytes).
    Removing xcm32lxx_crc.o(i.CRC_Init), (96 bytes).
    Removing xcm32lxx_crc.o(i.CRC_InitResult), (28 bytes).
    Removing xcm32lxx_crc.o(i.CRC_SendCRCData), (20 bytes).
    Removing xcm32lxx_crc.o(i.CRC_StructInit), (14 bytes).
    Removing xcm32lxx_des.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_des.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_des.o(i.DES_Cmd), (52 bytes).
    Removing xcm32lxx_des.o(i.DES_DeInit), (48 bytes).
    Removing xcm32lxx_des.o(i.DES_GetCompletedFlagStatus), (20 bytes).
    Removing xcm32lxx_des.o(i.DES_GetData), (28 bytes).
    Removing xcm32lxx_des.o(i.DES_Init), (128 bytes).
    Removing xcm32lxx_des.o(i.DES_SetData), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_SetIV), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_SetKey), (24 bytes).
    Removing xcm32lxx_des.o(i.DES_SetRandomNumber), (12 bytes).
    Removing xcm32lxx_des.o(i.DES_StructInit), (16 bytes).
    Removing xcm32lxx_dma.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_dma.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetCombineITStatus), (32 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetCompletedFlagStatus), (32 bytes).
    Removing xcm32lxx_dma.o(i.DMA_GetRawITStatus), (36 bytes).
    Removing xcm32lxx_flash.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_flash.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ClearITFlag), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_EraseMass), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_GetITStatus), (24 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_LockPage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadByte), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadCmd), (60 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadHalfWord), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadWaitCycleCmd), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_ReadWord), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTERASE), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTME), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVH), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVH1), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTNVS), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTPGS), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTPROG), (16 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_SetTRCV), (12 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_UnLockPage), (76 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteByte), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteCmd), (64 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteHalfWord), (68 bytes).
    Removing xcm32lxx_flash.o(i.FLASH_WriteWord), (68 bytes).
    Removing xcm32lxx_i2c.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_i2c.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_AbrtConfig), (24 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ClearITFlag), (64 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_Cmd), (34 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_DMAModeConfig), (74 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_DeInit), (60 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetEnabledFlagStatus), (20 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetFlagStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetITStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_GetRawITStatus), (18 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_Init), (280 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRead), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithReadStop), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRestartRead), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithRestartReadStop), (8 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_SendDataWithStop), (10 bytes).
    Removing xcm32lxx_i2c.o(i.I2C_StructInit), (42 bytes).
    Removing xcm32lxx_lcd.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_lcd.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_ClearITFlag), (20 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_Cmd), (52 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_DeInit), (44 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_GetITStatus), (28 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_ITConfig), (40 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_Init), (208 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_RAMHInit), (16 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_RAMLInit), (16 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_SetRAMData), (148 bytes).
    Removing xcm32lxx_lcd.o(i.LCD_StructInit), (22 bytes).
    Removing xcm32lxx_lvd.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_lvd.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_ClearITFlag), (20 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_Cmd), (76 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_FilterCmd), (36 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_GetITStatus), (28 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_HysteresisCmd), (76 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_ITConfig), (40 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_Init), (212 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_StructInit), (16 bytes).
    Removing xcm32lxx_lvd.o(i.LVD_WarmUpCmd), (76 bytes).
    Removing xcm32lxx_misc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_misc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_misc.o(i.NVIC_DeInit), (88 bytes).
    Removing xcm32lxx_pca.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_pca.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_CCAPOPolarityConfig), (34 bytes).
    Removing xcm32lxx_pca.o(i.PCA_ClearITFlag), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_Cmd), (44 bytes).
    Removing xcm32lxx_pca.o(i.PCA_DeInit), (88 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturer), (76 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturerHSB), (64 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCapturerLSB), (64 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounter), (16 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounterHSB), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetCounterLSB), (8 bytes).
    Removing xcm32lxx_pca.o(i.PCA_GetITStatus), (18 bytes).
    Removing xcm32lxx_pca.o(i.PCA_ITConfig), (282 bytes).
    Removing xcm32lxx_pca.o(i.PCA_Init), (400 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCapturerHSB), (56 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCapturerLSB), (56 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCounterHSB), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_SetCounterLSB), (4 bytes).
    Removing xcm32lxx_pca.o(i.PCA_StructInit), (18 bytes).
    Removing xcm32lxx_pmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_pmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterDeepSleep4Mode), (68 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterPowerDown3Mode), (48 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterPowerDown4Mode), (60 bytes).
    Removing xcm32lxx_pmu.o(i.PMU_EnterSleepMode), (52 bytes).
    Removing xcm32lxx_port.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_port.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_ClearITFlag), (8 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_GetITStatus), (18 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_ITConfig), (40 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_Init), (78 bytes).
    Removing xcm32lxx_port.o(i.PORT_EXTI_StructInit), (10 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadInputData), (8 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadOutputData), (28 bytes).
    Removing xcm32lxx_port.o(i.PORT_ReadOutputDataBit), (40 bytes).
    Removing xcm32lxx_port.o(i.PORT_ResetBits), (44 bytes).
    Removing xcm32lxx_port.o(i.PORT_SetBits), (44 bytes).
    Removing xcm32lxx_port.o(i.PORT_Write), (32 bytes).
    Removing xcm32lxx_ram.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_ram.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadByte), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadHalfWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMReadWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteByte), (56 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteHalfWord), (60 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAMWriteWord), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_BackUpRAM_DeInit), (44 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ClearITFlag), (20 bytes).
    Removing xcm32lxx_ram.o(i.RAM_GetITStatus), (20 bytes).
    Removing xcm32lxx_ram.o(i.RAM_GetODCErrAddress), (16 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadByte), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadHalfWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_ReadWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteByte), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteHalfWord), (24 bytes).
    Removing xcm32lxx_ram.o(i.RAM_WriteWord), (24 bytes).
    Removing xcm32lxx_rmu.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rmu.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_ClearResetFlag), (16 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_GetResetFlag), (24 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_Periph0ResetCmd), (32 bytes).
    Removing xcm32lxx_rmu.o(i.RMU_Periph1ResetCmd), (32 bytes).
    Removing xcm32lxx_rng.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rng.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rng.o(i.RNG_CircuitCmd), (52 bytes).
    Removing xcm32lxx_rng.o(i.RNG_Cmd), (52 bytes).
    Removing xcm32lxx_rng.o(i.RNG_DeInit), (44 bytes).
    Removing xcm32lxx_rng.o(i.RNG_GetCompletedFlagStatus), (24 bytes).
    Removing xcm32lxx_rng.o(i.RNG_GetRandomNumber), (28 bytes).
    Removing xcm32lxx_rng.o(i.RNG_Init), (100 bytes).
    Removing xcm32lxx_rng.o(i.RNG_StructInit), (12 bytes).
    Removing xcm32lxx_rtc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_rtc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_AdjustConfig), (28 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_GetSixteenOfSecond), (16 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_GetYearHSB), (12 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_GetYearLSB), (12 bytes).
    Removing xcm32lxx_rtc.o(i.RTC_ResetCmd), (36 bytes).
    Removing xcm32lxx_spi.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_spi.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ClearITFlag), (52 bytes).
    Removing xcm32lxx_spi.o(i.SPI_Cmd), (34 bytes).
    Removing xcm32lxx_spi.o(i.SPI_DMAModeConfig), (54 bytes).
    Removing xcm32lxx_spi.o(i.SPI_DeInit), (140 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetFlagStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetITStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetRawITStatus), (18 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetRxFIFOAvaDataCounter), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_GetTxFIFOAvaDataCounter), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ITConfig), (20 bytes).
    Removing xcm32lxx_spi.o(i.SPI_Init), (300 bytes).
    Removing xcm32lxx_spi.o(i.SPI_ReceiveData), (8 bytes).
    Removing xcm32lxx_spi.o(i.SPI_SendData), (4 bytes).
    Removing xcm32lxx_spi.o(i.SPI_StructInit), (36 bytes).
    Removing xcm32lxx_systick.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_systick.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_ClearITFlag), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetCurrentValue), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetITStatus), (20 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_GetReloadValue), (16 bytes).
    Removing xcm32lxx_systick.o(i.SysTick_SetCurrentValue), (16 bytes).
    Removing xcm32lxx_timer.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_timer.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ClearAllITFlag), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ClearITFlag), (6 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetAllITStatus), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetAllRawITStatus), (16 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetCurrentCounter), (6 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_GetITStatus), (14 bytes).
    Removing xcm32lxx_timer.o(i.TIMER_ITConfig), (24 bytes).
    Removing xcm32lxx_uart.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_uart.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_uart.o(i.UART_GetMDMStatus), (18 bytes).
    Removing xcm32lxx_vc.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_vc.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_vc.o(i.VC_BGRCmd), (76 bytes).
    Removing xcm32lxx_vc.o(i.VC_ClearITFlag), (20 bytes).
    Removing xcm32lxx_vc.o(i.VC_Cmd), (76 bytes).
    Removing xcm32lxx_vc.o(i.VC_FilterCmd), (36 bytes).
    Removing xcm32lxx_vc.o(i.VC_GetITStatus), (28 bytes).
    Removing xcm32lxx_vc.o(i.VC_ITConfig), (40 bytes).
    Removing xcm32lxx_vc.o(i.VC_Init), (204 bytes).
    Removing xcm32lxx_vc.o(i.VC_StructInit), (14 bytes).
    Removing xcm32lxx_vc.o(i.VC_VCCScalerCmd), (76 bytes).
    Removing xcm32lxx_wdt.o(.rev16_text), (4 bytes).
    Removing xcm32lxx_wdt.o(.revsh_text), (4 bytes).
    Removing xcm32lxx_wdt.o(i.WDT_GetCurrentWDTimer), (12 bytes).

341 unused section(s) (total 18597 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/tolower.c         0x00000000   Number         0  tolower.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fclose.c          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_b.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_ub.o ABSOLUTE
    ../clib/microlib/stdio/fgetc.c           0x00000000   Number         0  fgetc_u.o ABSOLUTE
    ../clib/microlib/stdio/fopen.c           0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/microlib/stdio/fread.c           0x00000000   Number         0  fread.o ABSOLUTE
    ../clib/microlib/stdio/fseek.c           0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/microlib/stdio/ftell.c           0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/abs.c            0x00000000   Number         0  ilabs.o ABSOLUTE
    ../clib/microlib/stdlib/abs.c            0x00000000   Number         0  siabs.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemig.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\User\cJSON\cJSON.c                    0x00000000   Number         0  cjson.o ABSOLUTE
    ..\User\cJSON\protol.c                   0x00000000   Number         0  protol.o ABSOLUTE
    ..\User\crc32.c                          0x00000000   Number         0  crc32.o ABSOLUTE
    ..\User\flash.c                          0x00000000   Number         0  flash.o ABSOLUTE
    ..\User\gpio.c                           0x00000000   Number         0  gpio.o ABSOLUTE
    ..\User\i2cUart.c                        0x00000000   Number         0  i2cuart.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\rtc.c                            0x00000000   Number         0  rtc.o ABSOLUTE
    ..\User\uart.c                           0x00000000   Number         0  uart.o ABSOLUTE
    ..\User\user_main.c                      0x00000000   Number         0  user_main.o ABSOLUTE
    ..\\User\\cJSON\\protol.c                0x00000000   Number         0  protol.o ABSOLUTE
    ..\\User\\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    ..\\User\\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\User\\i2cUart.c                      0x00000000   Number         0  i2cuart.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\User\\uart.c                         0x00000000   Number         0  uart.o ABSOLUTE
    ..\\User\\user_main.c                    0x00000000   Number         0  user_main.o ABSOLUTE
    ..\\xcm32lxx_lib\\core\\system_XCM32L.c  0x00000000   Number         0  system_xcm32l.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_adc.c    0x00000000   Number         0  xcm32lxx_adc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_buzzer.c 0x00000000   Number         0  xcm32lxx_buzzer.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_cmu.c    0x00000000   Number         0  xcm32lxx_cmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_crc.c    0x00000000   Number         0  xcm32lxx_crc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_des.c    0x00000000   Number         0  xcm32lxx_des.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_dma.c    0x00000000   Number         0  xcm32lxx_dma.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_flash.c  0x00000000   Number         0  xcm32lxx_flash.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_i2c.c    0x00000000   Number         0  xcm32lxx_i2c.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_lcd.c    0x00000000   Number         0  xcm32lxx_lcd.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_lvd.c    0x00000000   Number         0  xcm32lxx_lvd.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_misc.c   0x00000000   Number         0  xcm32lxx_misc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_pca.c    0x00000000   Number         0  xcm32lxx_pca.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_pmu.c    0x00000000   Number         0  xcm32lxx_pmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_port.c   0x00000000   Number         0  xcm32lxx_port.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_ram.c    0x00000000   Number         0  xcm32lxx_ram.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rmu.c    0x00000000   Number         0  xcm32lxx_rmu.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rng.c    0x00000000   Number         0  xcm32lxx_rng.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_rtc.c    0x00000000   Number         0  xcm32lxx_rtc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_spi.c    0x00000000   Number         0  xcm32lxx_spi.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_systick.c 0x00000000   Number         0  xcm32lxx_systick.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_timer.c  0x00000000   Number         0  xcm32lxx_timer.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_uart.c   0x00000000   Number         0  xcm32lxx_uart.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_vc.c     0x00000000   Number         0  xcm32lxx_vc.o ABSOLUTE
    ..\\xcm32lxx_lib\\src\\XCM32Lxx_wdt.c    0x00000000   Number         0  xcm32lxx_wdt.o ABSOLUTE
    ..\xcm32lxx_lib\core\startup_XCM32L.s    0x00000000   Number         0  startup_xcm32l.o ABSOLUTE
    ..\xcm32lxx_lib\core\system_XCM32L.c     0x00000000   Number         0  system_xcm32l.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_adc.c       0x00000000   Number         0  xcm32lxx_adc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_buzzer.c    0x00000000   Number         0  xcm32lxx_buzzer.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_cmu.c       0x00000000   Number         0  xcm32lxx_cmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_crc.c       0x00000000   Number         0  xcm32lxx_crc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_des.c       0x00000000   Number         0  xcm32lxx_des.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_dma.c       0x00000000   Number         0  xcm32lxx_dma.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_flash.c     0x00000000   Number         0  xcm32lxx_flash.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_i2c.c       0x00000000   Number         0  xcm32lxx_i2c.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_lcd.c       0x00000000   Number         0  xcm32lxx_lcd.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_lvd.c       0x00000000   Number         0  xcm32lxx_lvd.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_misc.c      0x00000000   Number         0  xcm32lxx_misc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_pca.c       0x00000000   Number         0  xcm32lxx_pca.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_pmu.c       0x00000000   Number         0  xcm32lxx_pmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_port.c      0x00000000   Number         0  xcm32lxx_port.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_ram.c       0x00000000   Number         0  xcm32lxx_ram.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rmu.c       0x00000000   Number         0  xcm32lxx_rmu.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rng.c       0x00000000   Number         0  xcm32lxx_rng.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_rtc.c       0x00000000   Number         0  xcm32lxx_rtc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_spi.c       0x00000000   Number         0  xcm32lxx_spi.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_systick.c   0x00000000   Number         0  xcm32lxx_systick.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_timer.c     0x00000000   Number         0  xcm32lxx_timer.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_uart.c      0x00000000   Number         0  xcm32lxx_uart.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_vc.c        0x00000000   Number         0  xcm32lxx_vc.o ABSOLUTE
    ..\xcm32lxx_lib\src\XCM32Lxx_wdt.c       0x00000000   Number         0  xcm32lxx_wdt.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00005000   Section      192  startup_xcm32l.o(RESET)
    .ARM.Collect$$$$00000000                 0x000050c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000050c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000050c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000050c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000050c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000050c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x000050d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000050d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000050d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x000050d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000050d4   Section       28  startup_xcm32l.o(.text)
    .text                                    0x000050f0   Section        0  idiv.o(.text)
    .text                                    0x00005118   Section        0  uldiv.o(.text)
    .text                                    0x00005178   Section        0  ldiv.o(.text)
    .text                                    0x000051c4   Section        0  tolower.o(.text)
    .text                                    0x000051d0   Section        0  siabs.o(.text)
    .text                                    0x000051d8   Section        0  memcpya.o(.text)
    .text                                    0x000051fc   Section        0  memseta.o(.text)
    .text                                    0x00005220   Section        0  strchr.o(.text)
    .text                                    0x00005234   Section        0  strlen.o(.text)
    .text                                    0x00005242   Section        0  strcmp.o(.text)
    .text                                    0x0000525e   Section        0  strcpy.o(.text)
    .text                                    0x00005270   Section        0  strncmp.o(.text)
    .text                                    0x0000528e   Section        0  fdiv.o(.text)
    .text                                    0x0000530c   Section        0  dadd.o(.text)
    .text                                    0x00005470   Section        0  dmul.o(.text)
    .text                                    0x00005540   Section        0  ddiv.o(.text)
    .text                                    0x00005630   Section        0  ffltui.o(.text)
    .text                                    0x00005640   Section        0  dflti.o(.text)
    .text                                    0x00005668   Section        0  dfltui.o(.text)
    .text                                    0x00005684   Section        0  ffixui.o(.text)
    .text                                    0x000056ac   Section        0  dfixi.o(.text)
    .text                                    0x000056f4   Section        0  dfixui.o(.text)
    .text                                    0x00005730   Section        0  f2d.o(.text)
    .text                                    0x00005758   Section       40  cdcmple.o(.text)
    .text                                    0x00005780   Section       40  cdrcmple.o(.text)
    .text                                    0x000057a8   Section        0  d2f.o(.text)
    .text                                    0x000057e0   Section        0  uidiv.o(.text)
    .text                                    0x0000580c   Section        0  llshl.o(.text)
    .text                                    0x0000582c   Section        0  llushr.o(.text)
    .text                                    0x0000584e   Section        0  llsshr.o(.text)
    .text                                    0x00005874   Section        0  iusefp.o(.text)
    .text                                    0x00005874   Section        0  fepilogue.o(.text)
    .text                                    0x000058f6   Section        0  depilogue.o(.text)
    .text                                    0x000059b4   Section        0  dscalb.o(.text)
    .text                                    0x000059e0   Section        0  dfixul.o(.text)
    .text                                    0x00005a20   Section       36  init.o(.text)
    .text                                    0x00005a44   Section        0  dsqrt.o(.text)
    .text                                    0x00005ae6   Section        0  __dczerorl2.o(.text)
    i.Booster_DeInit                         0x00005b3c   Section        0  main.o(i.Booster_DeInit)
    i.Booster_Init                           0x00005c38   Section        0  main.o(i.Booster_Init)
    i.Booster_Start                          0x00005cdc   Section        0  main.o(i.Booster_Start)
    i.Booster_Stop                           0x00005d78   Section        0  main.o(i.Booster_Stop)
    i.CMU_APBPeriph0ClockCmd                 0x00005e6c   Section        0  xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd)
    i.CMU_APBPeriph1ClockCmd                 0x00005e8c   Section        0  xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd)
    i.CMU_GetSysClkFreq                      0x00005eac   Section        0  xcm32lxx_cmu.o(i.CMU_GetSysClkFreq)
    i.CMU_GetSysClkSource                    0x00005ef0   Section        0  xcm32lxx_cmu.o(i.CMU_GetSysClkSource)
    i.CMU_HCLKConfig                         0x00005f30   Section        0  xcm32lxx_cmu.o(i.CMU_HCLKConfig)
    i.CMU_HSIConfig                          0x00005f64   Section        0  xcm32lxx_cmu.o(i.CMU_HSIConfig)
    i.CMU_LSEConfig                          0x00005fe4   Section        0  xcm32lxx_cmu.o(i.CMU_LSEConfig)
    i.CMU_LSIConfig                          0x00006044   Section        0  xcm32lxx_cmu.o(i.CMU_LSIConfig)
    i.CMU_PCLKConfig                         0x000060a8   Section        0  xcm32lxx_cmu.o(i.CMU_PCLKConfig)
    i.CMU_RTCCLKConfig                       0x000060dc   Section        0  xcm32lxx_cmu.o(i.CMU_RTCCLKConfig)
    i.CMU_SysClkConfig                       0x00006108   Section        0  xcm32lxx_cmu.o(i.CMU_SysClkConfig)
    i.CMU_WDTCLKConfig                       0x00006138   Section        0  xcm32lxx_cmu.o(i.CMU_WDTCLKConfig)
    i.CMU_WaitForSysClkStartUp               0x0000616c   Section        0  xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp)
    i.DMAC_IRQHandler                        0x000061fc   Section        0  uart.o(i.DMAC_IRQHandler)
    i.DMA_ClearITFlag                        0x00006240   Section        0  xcm32lxx_dma.o(i.DMA_ClearITFlag)
    i.DMA_Cmd                                0x00006254   Section        0  xcm32lxx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x00006274   Section        0  xcm32lxx_dma.o(i.DMA_DeInit)
    i.DMA_GetITStatus                        0x000062a4   Section        0  xcm32lxx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x000062c8   Section        0  xcm32lxx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x000062f4   Section        0  xcm32lxx_dma.o(i.DMA_Init)
    i.DMA_StructInit                         0x000065b8   Section        0  xcm32lxx_dma.o(i.DMA_StructInit)
    i.DelayMs                                0x00006602   Section        0  main.o(i.DelayMs)
    i.Delay_ms                               0x00006620   Section        0  main.o(i.Delay_ms)
    i.GetRtcSecond                           0x0000663c   Section        0  rtc.o(i.GetRtcSecond)
    i.GetSysTick                             0x00006754   Section        0  main.o(i.GetSysTick)
    i.I2C1_ReceiveData                       0x00006760   Section        0  i2cuart.o(i.I2C1_ReceiveData)
    i.I2C1_TransmitData                      0x000067ac   Section        0  i2cuart.o(i.I2C1_TransmitData)
    i.I2C_Start                              0x000067f0   Section        0  i2cuart.o(i.I2C_Start)
    i.I2C_Stop                               0x0000682c   Section        0  i2cuart.o(i.I2C_Stop)
    i.I2C_init                               0x00006868   Section        0  i2cuart.o(i.I2C_init)
    i.I2C_read_byte                          0x0000688c   Section        0  i2cuart.o(i.I2C_read_byte)
    i.I2C_send_byte                          0x00006908   Section        0  i2cuart.o(i.I2C_send_byte)
    i.Is_Leap_Year                           0x0000698c   Section        0  rtc.o(i.Is_Leap_Year)
    i.Master_ACK                             0x000069c8   Section        0  i2cuart.o(i.Master_ACK)
    i.MengMuRadarReceive                     0x00006a34   Section        0  user_main.o(i.MengMuRadarReceive)
    i.NVIC_ClearPendingIRQ                   0x00006b20   Section        0  main.o(i.NVIC_ClearPendingIRQ)
    NVIC_ClearPendingIRQ                     0x00006b21   Thumb Code    14  main.o(i.NVIC_ClearPendingIRQ)
    i.NVIC_ClearPendingIRQ                   0x00006b34   Section        0  uart.o(i.NVIC_ClearPendingIRQ)
    NVIC_ClearPendingIRQ                     0x00006b35   Thumb Code    14  uart.o(i.NVIC_ClearPendingIRQ)
    i.NVIC_DisableIRQ                        0x00006b48   Section        0  main.o(i.NVIC_DisableIRQ)
    NVIC_DisableIRQ                          0x00006b49   Thumb Code    14  main.o(i.NVIC_DisableIRQ)
    i.NVIC_DisableIRQ                        0x00006b5c   Section        0  uart.o(i.NVIC_DisableIRQ)
    NVIC_DisableIRQ                          0x00006b5d   Thumb Code    14  uart.o(i.NVIC_DisableIRQ)
    i.NVIC_EnableIRQ                         0x00006b70   Section        0  main.o(i.NVIC_EnableIRQ)
    NVIC_EnableIRQ                           0x00006b71   Thumb Code    14  main.o(i.NVIC_EnableIRQ)
    i.NVIC_EnableIRQ                         0x00006b84   Section        0  uart.o(i.NVIC_EnableIRQ)
    NVIC_EnableIRQ                           0x00006b85   Thumb Code    14  uart.o(i.NVIC_EnableIRQ)
    i.NVIC_Init_rtc                          0x00006b98   Section        0  rtc.o(i.NVIC_Init_rtc)
    NVIC_Init_rtc                            0x00006b99   Thumb Code    52  rtc.o(i.NVIC_Init_rtc)
    i.NVIC_SetPriority                       0x00006bd4   Section        0  main.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006bd5   Thumb Code   110  main.o(i.NVIC_SetPriority)
    i.NVIC_SetPriority                       0x00006c4c   Section        0  rtc.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006c4d   Thumb Code   110  rtc.o(i.NVIC_SetPriority)
    i.NVIC_SetPriority                       0x00006cc4   Section        0  uart.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x00006cc5   Thumb Code   110  uart.o(i.NVIC_SetPriority)
    i.PMU_EnterDeepSleep3Mode                0x00006d3c   Section        0  xcm32lxx_pmu.o(i.PMU_EnterDeepSleep3Mode)
    i.PORT_DeInit                            0x00006d70   Section        0  xcm32lxx_port.o(i.PORT_DeInit)
    i.PORT_DirSet                            0x00006d9c   Section        0  i2cuart.o(i.PORT_DirSet)
    i.PORT_Init                              0x00006db8   Section        0  xcm32lxx_port.o(i.PORT_Init)
    i.PORT_PinAFConfig                       0x00006e28   Section        0  xcm32lxx_port.o(i.PORT_PinAFConfig)
    i.PORT_ReadInputDataBit                  0x00006e72   Section        0  xcm32lxx_port.o(i.PORT_ReadInputDataBit)
    i.PORT_StructInit                        0x00006e84   Section        0  xcm32lxx_port.o(i.PORT_StructInit)
    i.PORT_ToggleBit                         0x00006ea0   Section        0  xcm32lxx_port.o(i.PORT_ToggleBit)
    i.PORT_WriteBit                          0x00006ecc   Section        0  xcm32lxx_port.o(i.PORT_WriteBit)
    i.RTC_ClearITFlag                        0x00006f0c   Section        0  xcm32lxx_rtc.o(i.RTC_ClearITFlag)
    i.RTC_Cmd                                0x00006f20   Section        0  xcm32lxx_rtc.o(i.RTC_Cmd)
    i.RTC_DeInit                             0x00006f44   Section        0  xcm32lxx_rtc.o(i.RTC_DeInit)
    i.RTC_GetDay                             0x00006f70   Section        0  xcm32lxx_rtc.o(i.RTC_GetDay)
    i.RTC_GetHour                            0x00006f80   Section        0  xcm32lxx_rtc.o(i.RTC_GetHour)
    i.RTC_GetITStatus                        0x00006f90   Section        0  xcm32lxx_rtc.o(i.RTC_GetITStatus)
    i.RTC_GetMinute                          0x00006fa4   Section        0  xcm32lxx_rtc.o(i.RTC_GetMinute)
    i.RTC_GetMonth                           0x00006fb4   Section        0  xcm32lxx_rtc.o(i.RTC_GetMonth)
    i.RTC_GetSecond                          0x00006fc4   Section        0  xcm32lxx_rtc.o(i.RTC_GetSecond)
    i.RTC_GetYear                            0x00006fd4   Section        0  xcm32lxx_rtc.o(i.RTC_GetYear)
    i.RTC_IRQHandler                         0x00006fe8   Section        0  rtc.o(i.RTC_IRQHandler)
    i.RTC_ITConfig                           0x00006ff8   Section        0  xcm32lxx_rtc.o(i.RTC_ITConfig)
    i.RTC_Init                               0x00007078   Section        0  xcm32lxx_rtc.o(i.RTC_Init)
    i.RTC_Run_Init                           0x0000715c   Section        0  rtc.o(i.RTC_Run_Init)
    i.RTC_SetTime                            0x0000717e   Section        0  rtc.o(i.RTC_SetTime)
    i.RTC_StructInit                         0x000071da   Section        0  xcm32lxx_rtc.o(i.RTC_StructInit)
    i.RTC_To_Sec                             0x000071fc   Section        0  rtc.o(i.RTC_To_Sec)
    i.RadarTurnOff                           0x000072dc   Section        0  user_main.o(i.RadarTurnOff)
    i.RadarTurnOn                            0x00007338   Section        0  user_main.o(i.RadarTurnOn)
    i.SetSysClock                            0x00007398   Section        0  system_xcm32l.o(i.SetSysClock)
    SetSysClock                              0x00007399   Thumb Code     2  system_xcm32l.o(i.SetSysClock)
    i.SnapModule_Off                         0x0000739c   Section        0  user_main.o(i.SnapModule_Off)
    i.SnapModule_On                          0x0000740c   Section        0  user_main.o(i.SnapModule_On)
    i.Switch_4G_Off                          0x00007488   Section        0  user_main.o(i.Switch_4G_Off)
    i.Switch_4G_On                           0x000074d8   Section        0  user_main.o(i.Switch_4G_On)
    i.SysTickConfigure                       0x00007534   Section        0  main.o(i.SysTickConfigure)
    i.SysTick_CLKSourceConfig                0x00007588   Section        0  xcm32lxx_systick.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Cmd                            0x000075cc   Section        0  xcm32lxx_systick.o(i.SysTick_Cmd)
    i.SysTick_Handler                        0x000075f0   Section        0  main.o(i.SysTick_Handler)
    i.SysTick_ITConfig                       0x00007600   Section        0  xcm32lxx_systick.o(i.SysTick_ITConfig)
    i.SysTick_SetReloadValue                 0x00007624   Section        0  xcm32lxx_systick.o(i.SysTick_SetReloadValue)
    i.SystemInit                             0x00007634   Section        0  system_xcm32l.o(i.SystemInit)
    i.TIMER_Cmd                              0x00007734   Section        0  xcm32lxx_timer.o(i.TIMER_Cmd)
    i.TIMER_DeInit                           0x00007758   Section        0  xcm32lxx_timer.o(i.TIMER_DeInit)
    i.TIMER_Init                             0x000077d8   Section        0  xcm32lxx_timer.o(i.TIMER_Init)
    i.TIMER_SetLoadCounter0                  0x00007898   Section        0  xcm32lxx_timer.o(i.TIMER_SetLoadCounter0)
    i.TIMER_SetLoadCounter1                  0x0000789c   Section        0  xcm32lxx_timer.o(i.TIMER_SetLoadCounter1)
    i.TIMER_StructInit                       0x000078ec   Section        0  xcm32lxx_timer.o(i.TIMER_StructInit)
    i.Test_ACK                               0x000078f8   Section        0  i2cuart.o(i.Test_ACK)
    i.Timer1_PwmOut_Init                     0x00007964   Section        0  main.o(i.Timer1_PwmOut_Init)
    i.Timer2_PwmOut_Init                     0x000079b8   Section        0  main.o(i.Timer2_PwmOut_Init)
    i.UART1_IRQHandler                       0x00007a0c   Section        0  uart.o(i.UART1_IRQHandler)
    i.UART1_Init                             0x00007a88   Section        0  uart.o(i.UART1_Init)
    i.UART2_IRQHandler                       0x00007bcc   Section        0  uart.o(i.UART2_IRQHandler)
    i.UART2_Init                             0x00007c40   Section        0  uart.o(i.UART2_Init)
    i.UART4_IRQHandler                       0x00007d20   Section        0  uart.o(i.UART4_IRQHandler)
    i.UART4_Init                             0x00007d5c   Section        0  uart.o(i.UART4_Init)
    i.UART_DeInit                            0x00007e20   Section        0  xcm32lxx_uart.o(i.UART_DeInit)
    i.UART_FIFOModeConfig                    0x00007e8c   Section        0  xcm32lxx_uart.o(i.UART_FIFOModeConfig)
    i.UART_GetITStatus                       0x00007ee0   Section        0  xcm32lxx_uart.o(i.UART_GetITStatus)
    i.UART_GetLineStatus                     0x00007ef4   Section        0  xcm32lxx_uart.o(i.UART_GetLineStatus)
    i.UART_ITConfig                          0x00007f06   Section        0  xcm32lxx_uart.o(i.UART_ITConfig)
    i.UART_Init                              0x00007f1c   Section        0  xcm32lxx_uart.o(i.UART_Init)
    i.UART_PTXREModeConfig                   0x000081d8   Section        0  xcm32lxx_uart.o(i.UART_PTXREModeConfig)
    i.UART_ReceiveData                       0x000081f0   Section        0  xcm32lxx_uart.o(i.UART_ReceiveData)
    i.UART_SendData                          0x000081f8   Section        0  xcm32lxx_uart.o(i.UART_SendData)
    i.UART_StructInit                        0x000081fe   Section        0  xcm32lxx_uart.o(i.UART_StructInit)
    i.WDT_ClearITFlag                        0x00008218   Section        0  xcm32lxx_wdt.o(i.WDT_ClearITFlag)
    i.WDT_Cmd                                0x00008228   Section        0  xcm32lxx_wdt.o(i.WDT_Cmd)
    i.WDT_Configure                          0x0000825c   Section        0  main.o(i.WDT_Configure)
    i.WDT_DeInit                             0x000082c8   Section        0  xcm32lxx_wdt.o(i.WDT_DeInit)
    i.WDT_GetITStatus                        0x000082f4   Section        0  xcm32lxx_wdt.o(i.WDT_GetITStatus)
    i.WDT_IRQHandler                         0x00008308   Section        0  main.o(i.WDT_IRQHandler)
    i.WDT_Init                               0x00008320   Section        0  xcm32lxx_wdt.o(i.WDT_Init)
    i.WDT_RestartCmd                         0x00008370   Section        0  xcm32lxx_wdt.o(i.WDT_RestartCmd)
    i.WDT_StructInit                         0x00008394   Section        0  xcm32lxx_wdt.o(i.WDT_StructInit)
    i.__0printf                              0x000083a4   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x000083c4   Section        0  printfa.o(i.__0snprintf)
    i.__0sprintf                             0x000083f0   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x00008418   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_common_switch8                   0x00008446   Section        0  cjson.o(i.__ARM_common_switch8)
    i.__ARM_fpclassify                       0x00008464   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x00008490   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x0000853c   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x00008550   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x00008558   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x00008568   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x0000857c   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x00008590   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0000859e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x000085a0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x000085b0   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x000085bc   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x000085bd   Thumb Code   316  printfa.o(i._fp_digits)
    i._printf_core                           0x00008714   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x00008715   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_post_padding                   0x00008e00   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x00008e01   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x00008e20   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x00008e21   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._snputc                                0x00008e4c   Section        0  printfa.o(i._snputc)
    _snputc                                  0x00008e4d   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x00008e62   Section        0  printfa.o(i._sputc)
    _sputc                                   0x00008e63   Thumb Code    10  printfa.o(i._sputc)
    i.cJSON_AddItemToArray                   0x00008e6c   Section        0  cjson.o(i.cJSON_AddItemToArray)
    i.cJSON_AddItemToObject                  0x00008e9c   Section        0  cjson.o(i.cJSON_AddItemToObject)
    i.cJSON_CreateNumber                     0x00008ed0   Section        0  cjson.o(i.cJSON_CreateNumber)
    i.cJSON_CreateObject                     0x00008ef6   Section        0  cjson.o(i.cJSON_CreateObject)
    i.cJSON_CreateString                     0x00008f0a   Section        0  cjson.o(i.cJSON_CreateString)
    i.cJSON_Delete                           0x00008f28   Section        0  cjson.o(i.cJSON_Delete)
    i.cJSON_GetObjectItem                    0x00008f90   Section        0  cjson.o(i.cJSON_GetObjectItem)
    i.cJSON_New_Item                         0x00008fb0   Section        0  cjson.o(i.cJSON_New_Item)
    cJSON_New_Item                           0x00008fb1   Thumb Code    28  cjson.o(i.cJSON_New_Item)
    i.cJSON_Parse                            0x00008fd0   Section        0  cjson.o(i.cJSON_Parse)
    i.cJSON_ParseWithOpts                    0x00008fe0   Section        0  cjson.o(i.cJSON_ParseWithOpts)
    i.cJSON_Print                            0x0000904c   Section        0  cjson.o(i.cJSON_Print)
    i.cJSON_strcasecmp                       0x0000905e   Section        0  cjson.o(i.cJSON_strcasecmp)
    cJSON_strcasecmp                         0x0000905f   Thumb Code    80  cjson.o(i.cJSON_strcasecmp)
    i.cJSON_strdup                           0x000090b0   Section        0  cjson.o(i.cJSON_strdup)
    cJSON_strdup                             0x000090b1   Thumb Code    40  cjson.o(i.cJSON_strdup)
    i.cJsonParseProtocol                     0x000090dc   Section        0  protol.o(i.cJsonParseProtocol)
    i.crc32                                  0x0000a6a4   Section        0  crc32.o(i.crc32)
    i.delay                                  0x0000a77c   Section        0  i2cuart.o(i.delay)
    i.delay_5us                              0x0000a792   Section        0  i2cuart.o(i.delay_5us)
    i.do_production_test_proc                0x0000a7ac   Section        0  user_main.o(i.do_production_test_proc)
    i.ensure                                 0x0000a838   Section        0  cjson.o(i.ensure)
    ensure                                   0x0000a839   Thumb Code   108  cjson.o(i.ensure)
    i.enter_stop_mode                        0x0000a8ac   Section        0  user_main.o(i.enter_stop_mode)
    i.floor                                  0x0000a970   Section        0  floor.o(i.floor)
    i.fputc                                  0x0000aa38   Section        0  main.o(i.fputc)
    i.free                                   0x0000aa70   Section        0  malloc.o(i.free)
    i.get_voltage                            0x0000aac4   Section        0  i2cuart.o(i.get_voltage)
    i.main                                   0x0000ab74   Section        0  main.o(i.main)
    i.malloc                                 0x0000ac00   Section        0  malloc.o(i.malloc)
    i.pack_data                              0x0000ac6c   Section        0  uart.o(i.pack_data)
    i.parse_array                            0x0000ad00   Section        0  cjson.o(i.parse_array)
    parse_array                              0x0000ad01   Thumb Code   162  cjson.o(i.parse_array)
    i.parse_hex4                             0x0000ada8   Section        0  cjson.o(i.parse_hex4)
    parse_hex4                               0x0000ada9   Thumb Code   276  cjson.o(i.parse_hex4)
    i.parse_number                           0x0000aebc   Section        0  cjson.o(i.parse_number)
    parse_number                             0x0000aebd   Thumb Code   384  cjson.o(i.parse_number)
    i.parse_object                           0x0000b048   Section        0  cjson.o(i.parse_object)
    parse_object                             0x0000b049   Thumb Code   266  cjson.o(i.parse_object)
    i.parse_string                           0x0000b158   Section        0  cjson.o(i.parse_string)
    parse_string                             0x0000b159   Thumb Code   472  cjson.o(i.parse_string)
    i.parse_value                            0x0000b344   Section        0  cjson.o(i.parse_value)
    parse_value                              0x0000b345   Thumb Code   164  cjson.o(i.parse_value)
    i.pow                                    0x0000b404   Section        0  pow.o(i.pow)
    i.pow2gt                                 0x0000be00   Section        0  cjson.o(i.pow2gt)
    pow2gt                                   0x0000be01   Thumb Code    28  cjson.o(i.pow2gt)
    i.print_array                            0x0000be1c   Section        0  cjson.o(i.print_array)
    print_array                              0x0000be1d   Thumb Code   596  cjson.o(i.print_array)
    i.print_number                           0x0000c07c   Section        0  cjson.o(i.print_number)
    print_number                             0x0000c07d   Thumb Code   360  cjson.o(i.print_number)
    i.print_object                           0x0000c224   Section        0  cjson.o(i.print_object)
    print_object                             0x0000c225   Thumb Code  1160  cjson.o(i.print_object)
    i.print_string                           0x0000c6ac   Section        0  cjson.o(i.print_string)
    print_string                             0x0000c6ad   Thumb Code    16  cjson.o(i.print_string)
    i.print_string_ptr                       0x0000c6bc   Section        0  cjson.o(i.print_string_ptr)
    print_string_ptr                         0x0000c6bd   Thumb Code   448  cjson.o(i.print_string_ptr)
    i.print_value                            0x0000c894   Section        0  cjson.o(i.print_value)
    print_value                              0x0000c895   Thumb Code   282  cjson.o(i.print_value)
    i.send_transparent_info                  0x0000c9c8   Section        0  protol.o(i.send_transparent_info)
    i.skip                                   0x0000cd04   Section        0  cjson.o(i.skip)
    skip                                     0x0000cd05   Thumb Code    22  cjson.o(i.skip)
    i.sqrt                                   0x0000cd1c   Section        0  sqrt.o(i.sqrt)
    i.startSnap                              0x0000cd64   Section        0  user_main.o(i.startSnap)
    i.suffix_object                          0x0000ce34   Section        0  cjson.o(i.suffix_object)
    suffix_object                            0x0000ce35   Thumb Code     6  cjson.o(i.suffix_object)
    i.t_BCD2HEX                              0x0000ce3a   Section        0  rtc.o(i.t_BCD2HEX)
    i.t_HEX2BCD                              0x0000ce88   Section        0  rtc.o(i.t_HEX2BCD)
    i.uart1_rx_mengmu_process                0x0000cf40   Section        0  uart.o(i.uart1_rx_mengmu_process)
    i.uart2_com_tx_data                      0x0000d25c   Section        0  uart.o(i.uart2_com_tx_data)
    i.uart2_find_head_and_tail               0x0000d2a0   Section        0  uart.o(i.uart2_find_head_and_tail)
    i.uart2_rx_process                       0x0000d3e4   Section        0  uart.o(i.uart2_rx_process)
    i.uart2_unpack_data                      0x0000d5e8   Section        0  uart.o(i.uart2_unpack_data)
    i.uf_DMA_UartRxReqInit                   0x0000d678   Section        0  uart.o(i.uf_DMA_UartRxReqInit)
    i.uf_GPIO_CMU_Init                       0x0000d6e8   Section        0  rtc.o(i.uf_GPIO_CMU_Init)
    i.uf_GPIO_Init                           0x0000d70c   Section        0  gpio.o(i.uf_GPIO_Init)
    i.uf_GPIO_RTC_Init                       0x0000d840   Section        0  rtc.o(i.uf_GPIO_RTC_Init)
    i.uf_RTC_Init                            0x0000d880   Section        0  rtc.o(i.uf_RTC_Init)
    i.update                                 0x0000d918   Section        0  cjson.o(i.update)
    update                                   0x0000d919   Thumb Code    36  cjson.o(i.update)
    i.user_main                              0x0000d93c   Section        0  user_main.o(i.user_main)
    .constdata                               0x0000e3d8   Section        7  cjson.o(.constdata)
    firstByteMark                            0x0000e3d8   Data           7  cjson.o(.constdata)
    .constdata                               0x0000e3e0   Section     1024  crc32.o(.constdata)
    crc_table                                0x0000e3e0   Data        1024  crc32.o(.constdata)
    .constdata                               0x0000e7e0   Section       55  main.o(.constdata)
    __FUNCTION__                             0x0000e7e0   Data          14  main.o(.constdata)
    __FUNCTION__                             0x0000e7ee   Data          13  main.o(.constdata)
    __FUNCTION__                             0x0000e7fb   Data          13  main.o(.constdata)
    __FUNCTION__                             0x0000e808   Data          15  main.o(.constdata)
    .constdata                               0x0000e817   Section       12  rtc.o(.constdata)
    .constdata                               0x0000e828   Section      136  pow.o(.constdata)
    bp                                       0x0000e828   Data          16  pow.o(.constdata)
    dp_h                                     0x0000e838   Data          16  pow.o(.constdata)
    dp_l                                     0x0000e848   Data          16  pow.o(.constdata)
    L                                        0x0000e858   Data          48  pow.o(.constdata)
    P                                        0x0000e888   Data          40  pow.o(.constdata)
    .constdata                               0x0000e8b0   Section        8  qnan.o(.constdata)
    .conststring                             0x0000e8b8   Section       26  user_main.o(.conststring)
    .data                                    0x20000000   Section       12  cjson.o(.data)
    ep                                       0x20000000   Data           4  cjson.o(.data)
    cJSON_malloc                             0x20000004   Data           4  cjson.o(.data)
    cJSON_free                               0x20000008   Data           4  cjson.o(.data)
    .data                                    0x2000000c   Section       41  protol.o(.data)
    .data                                    0x20000038   Section       40  i2cuart.o(.data)
    P1                                       0x20000038   Data           4  i2cuart.o(.data)
    P2                                       0x2000003c   Data           4  i2cuart.o(.data)
    P3                                       0x20000040   Data           4  i2cuart.o(.data)
    iVoltage                                 0x20000044   Data           4  i2cuart.o(.data)
    g_Voltage                                0x20000048   Data           4  i2cuart.o(.data)
    P1                                       0x2000004c   Data           4  i2cuart.o(.data)
    P2                                       0x20000050   Data           4  i2cuart.o(.data)
    P3                                       0x20000054   Data           4  i2cuart.o(.data)
    iVoltage                                 0x20000058   Data           4  i2cuart.o(.data)
    g_fVoltage                               0x2000005c   Data           4  i2cuart.o(.data)
    .data                                    0x20000060   Section        8  main.o(.data)
    .data                                    0x20000068   Section       32  rtc.o(.data)
    llTmpSecs                                0x20000080   Data           8  rtc.o(.data)
    .data                                    0x20000088   Section       14  uart.o(.data)
    s_seq                                    0x20000090   Data           2  uart.o(.data)
    s_seq                                    0x20000092   Data           2  uart.o(.data)
    s_seq                                    0x20000094   Data           2  uart.o(.data)
    .data                                    0x20000098   Section      194  user_main.o(.data)
    .data                                    0x2000015c   Section       28  xcm32lxx_port.o(.data)
    gvPortOut                                0x2000015c   Data          28  xcm32lxx_port.o(.data)
    .data                                    0x20000178   Section        4  stdout.o(.data)
    .data                                    0x2000017c   Section        4  mvars.o(.data)
    .data                                    0x20000180   Section        4  mvars.o(.data)
    .data                                    0x20000184   Section        4  errno.o(.data)
    _errno                                   0x20000184   Data           4  errno.o(.data)
    .bss                                     0x20000188   Section     3444  uart.o(.bss)
    .bss                                     0x20000f00   Section      528  user_main.o(.bss)
    HEAP                                     0x20001110   Section     3072  startup_xcm32l.o(HEAP)
    STACK                                    0x20001d10   Section     1024  startup_xcm32l.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000c0   Number         0  startup_xcm32l.o ABSOLUTE
    __Vectors                                0x00005000   Data           4  startup_xcm32l.o(RESET)
    __Vectors_End                            0x000050c0   Data           0  startup_xcm32l.o(RESET)
    __main                                   0x000050c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000050c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000050c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000050c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000050c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000050c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000050c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000050d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000050d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000050d5   Thumb Code     8  startup_xcm32l.o(.text)
    NMI_Handler                              0x000050dd   Thumb Code     2  startup_xcm32l.o(.text)
    HardFault_Handler                        0x000050df   Thumb Code     2  startup_xcm32l.o(.text)
    SVC_Handler                              0x000050e1   Thumb Code     2  startup_xcm32l.o(.text)
    PendSV_Handler                           0x000050e3   Thumb Code     2  startup_xcm32l.o(.text)
    ADC_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    BASETIMER_IRQHandler                     0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    I2C1_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    I2C2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    LCD_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    LVD_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P0_IRQHandler                            0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P1P2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P3P4_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    P5P6_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    PCA12_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    PCA34_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    RAM_IRQHandler                           0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SCI7816_IRQHandler                       0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI1_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI2_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI3_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    SPI4_IRQHandler                          0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER1_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER2_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER3_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    TIMER4_IRQHandler                        0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART3_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART5_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    UART6_IRQHandler                         0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    VC_IRQHandler                            0x000050e7   Thumb Code     0  startup_xcm32l.o(.text)
    __aeabi_idiv                             0x000050f1   Thumb Code     0  idiv.o(.text)
    __aeabi_idivmod                          0x000050f1   Thumb Code    40  idiv.o(.text)
    __aeabi_uldivmod                         0x00005119   Thumb Code    96  uldiv.o(.text)
    __aeabi_ldivmod                          0x00005179   Thumb Code    76  ldiv.o(.text)
    tolower                                  0x000051c5   Thumb Code    12  tolower.o(.text)
    abs                                      0x000051d1   Thumb Code     8  siabs.o(.text)
    __aeabi_memcpy                           0x000051d9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x000051d9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x000051d9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000051fd   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000051fd   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000051fd   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0000520b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0000520b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0000520b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0000520f   Thumb Code    18  memseta.o(.text)
    strchr                                   0x00005221   Thumb Code    20  strchr.o(.text)
    strlen                                   0x00005235   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x00005243   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x0000525f   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x00005271   Thumb Code    30  strncmp.o(.text)
    __aeabi_fdiv                             0x0000528f   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x0000530d   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x00005455   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00005461   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00005471   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x00005541   Thumb Code   234  ddiv.o(.text)
    __aeabi_ui2f                             0x00005631   Thumb Code    14  ffltui.o(.text)
    __aeabi_i2d                              0x00005641   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x00005669   Thumb Code    24  dfltui.o(.text)
    __aeabi_f2uiz                            0x00005685   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2iz                             0x000056ad   Thumb Code    62  dfixi.o(.text)
    __aeabi_d2uiz                            0x000056f5   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x00005731   Thumb Code    40  f2d.o(.text)
    __aeabi_cdcmpeq                          0x00005759   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x00005759   Thumb Code    38  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x00005781   Thumb Code    38  cdrcmple.o(.text)
    __aeabi_d2f                              0x000057a9   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x000057e1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x000057e1   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0000580d   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x0000580d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0000582d   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x0000582d   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0000584f   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0000584f   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x00005875   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00005875   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00005885   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000058f7   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00005911   Thumb Code   164  depilogue.o(.text)
    __ARM_scalbn                             0x000059b5   Thumb Code    44  dscalb.o(.text)
    scalbn                                   0x000059b5   Thumb Code     0  dscalb.o(.text)
    __aeabi_d2ulz                            0x000059e1   Thumb Code    54  dfixul.o(.text)
    __scatterload                            0x00005a21   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00005a21   Thumb Code     0  init.o(.text)
    _dsqrt                                   0x00005a45   Thumb Code   162  dsqrt.o(.text)
    __decompress                             0x00005ae7   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x00005ae7   Thumb Code    86  __dczerorl2.o(.text)
    Booster_DeInit                           0x00005b3d   Thumb Code   208  main.o(i.Booster_DeInit)
    Booster_Init                             0x00005c39   Thumb Code   118  main.o(i.Booster_Init)
    Booster_Start                            0x00005cdd   Thumb Code   112  main.o(i.Booster_Start)
    Booster_Stop                             0x00005d79   Thumb Code   202  main.o(i.Booster_Stop)
    CMU_APBPeriph0ClockCmd                   0x00005e6d   Thumb Code    28  xcm32lxx_cmu.o(i.CMU_APBPeriph0ClockCmd)
    CMU_APBPeriph1ClockCmd                   0x00005e8d   Thumb Code    28  xcm32lxx_cmu.o(i.CMU_APBPeriph1ClockCmd)
    CMU_GetSysClkFreq                        0x00005ead   Thumb Code    56  xcm32lxx_cmu.o(i.CMU_GetSysClkFreq)
    CMU_GetSysClkSource                      0x00005ef1   Thumb Code    58  xcm32lxx_cmu.o(i.CMU_GetSysClkSource)
    CMU_HCLKConfig                           0x00005f31   Thumb Code    48  xcm32lxx_cmu.o(i.CMU_HCLKConfig)
    CMU_HSIConfig                            0x00005f65   Thumb Code   118  xcm32lxx_cmu.o(i.CMU_HSIConfig)
    CMU_LSEConfig                            0x00005fe5   Thumb Code    92  xcm32lxx_cmu.o(i.CMU_LSEConfig)
    CMU_LSIConfig                            0x00006045   Thumb Code    90  xcm32lxx_cmu.o(i.CMU_LSIConfig)
    CMU_PCLKConfig                           0x000060a9   Thumb Code    48  xcm32lxx_cmu.o(i.CMU_PCLKConfig)
    CMU_RTCCLKConfig                         0x000060dd   Thumb Code    40  xcm32lxx_cmu.o(i.CMU_RTCCLKConfig)
    CMU_SysClkConfig                         0x00006109   Thumb Code    44  xcm32lxx_cmu.o(i.CMU_SysClkConfig)
    CMU_WDTCLKConfig                         0x00006139   Thumb Code    48  xcm32lxx_cmu.o(i.CMU_WDTCLKConfig)
    CMU_WaitForSysClkStartUp                 0x0000616d   Thumb Code   140  xcm32lxx_cmu.o(i.CMU_WaitForSysClkStartUp)
    DMAC_IRQHandler                          0x000061fd   Thumb Code    48  uart.o(i.DMAC_IRQHandler)
    DMA_ClearITFlag                          0x00006241   Thumb Code    16  xcm32lxx_dma.o(i.DMA_ClearITFlag)
    DMA_Cmd                                  0x00006255   Thumb Code    28  xcm32lxx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x00006275   Thumb Code    42  xcm32lxx_dma.o(i.DMA_DeInit)
    DMA_GetITStatus                          0x000062a5   Thumb Code    32  xcm32lxx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x000062c9   Thumb Code    40  xcm32lxx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x000062f5   Thumb Code   696  xcm32lxx_dma.o(i.DMA_Init)
    DMA_StructInit                           0x000065b9   Thumb Code    74  xcm32lxx_dma.o(i.DMA_StructInit)
    DelayMs                                  0x00006603   Thumb Code    30  main.o(i.DelayMs)
    Delay_ms                                 0x00006621   Thumb Code    26  main.o(i.Delay_ms)
    GetRtcSecond                             0x0000663d   Thumb Code   204  rtc.o(i.GetRtcSecond)
    GetSysTick                               0x00006755   Thumb Code     6  main.o(i.GetSysTick)
    I2C1_ReceiveData                         0x00006761   Thumb Code    76  i2cuart.o(i.I2C1_ReceiveData)
    I2C1_TransmitData                        0x000067ad   Thumb Code    66  i2cuart.o(i.I2C1_TransmitData)
    I2C_Start                                0x000067f1   Thumb Code    56  i2cuart.o(i.I2C_Start)
    I2C_Stop                                 0x0000682d   Thumb Code    56  i2cuart.o(i.I2C_Stop)
    I2C_init                                 0x00006869   Thumb Code    32  i2cuart.o(i.I2C_init)
    I2C_read_byte                            0x0000688d   Thumb Code   120  i2cuart.o(i.I2C_read_byte)
    I2C_send_byte                            0x00006909   Thumb Code   126  i2cuart.o(i.I2C_send_byte)
    Is_Leap_Year                             0x0000698d   Thumb Code    60  rtc.o(i.Is_Leap_Year)
    Master_ACK                               0x000069c9   Thumb Code   102  i2cuart.o(i.Master_ACK)
    MengMuRadarReceive                       0x00006a35   Thumb Code   154  user_main.o(i.MengMuRadarReceive)
    PMU_EnterDeepSleep3Mode                  0x00006d3d   Thumb Code    46  xcm32lxx_pmu.o(i.PMU_EnterDeepSleep3Mode)
    PORT_DeInit                              0x00006d71   Thumb Code    38  xcm32lxx_port.o(i.PORT_DeInit)
    PORT_DirSet                              0x00006d9d   Thumb Code    28  i2cuart.o(i.PORT_DirSet)
    PORT_Init                                0x00006db9   Thumb Code   112  xcm32lxx_port.o(i.PORT_Init)
    PORT_PinAFConfig                         0x00006e29   Thumb Code    74  xcm32lxx_port.o(i.PORT_PinAFConfig)
    PORT_ReadInputDataBit                    0x00006e73   Thumb Code    18  xcm32lxx_port.o(i.PORT_ReadInputDataBit)
    PORT_StructInit                          0x00006e85   Thumb Code    22  xcm32lxx_port.o(i.PORT_StructInit)
    PORT_ToggleBit                           0x00006ea1   Thumb Code    34  xcm32lxx_port.o(i.PORT_ToggleBit)
    PORT_WriteBit                            0x00006ecd   Thumb Code    54  xcm32lxx_port.o(i.PORT_WriteBit)
    RTC_ClearITFlag                          0x00006f0d   Thumb Code    14  xcm32lxx_rtc.o(i.RTC_ClearITFlag)
    RTC_Cmd                                  0x00006f21   Thumb Code    32  xcm32lxx_rtc.o(i.RTC_Cmd)
    RTC_DeInit                               0x00006f45   Thumb Code    38  xcm32lxx_rtc.o(i.RTC_DeInit)
    RTC_GetDay                               0x00006f71   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetDay)
    RTC_GetHour                              0x00006f81   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetHour)
    RTC_GetITStatus                          0x00006f91   Thumb Code    16  xcm32lxx_rtc.o(i.RTC_GetITStatus)
    RTC_GetMinute                            0x00006fa5   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetMinute)
    RTC_GetMonth                             0x00006fb5   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetMonth)
    RTC_GetSecond                            0x00006fc5   Thumb Code    10  xcm32lxx_rtc.o(i.RTC_GetSecond)
    RTC_GetYear                              0x00006fd5   Thumb Code    16  xcm32lxx_rtc.o(i.RTC_GetYear)
    RTC_IRQHandler                           0x00006fe9   Thumb Code    16  rtc.o(i.RTC_IRQHandler)
    RTC_ITConfig                             0x00006ff9   Thumb Code   124  xcm32lxx_rtc.o(i.RTC_ITConfig)
    RTC_Init                                 0x00007079   Thumb Code   222  xcm32lxx_rtc.o(i.RTC_Init)
    RTC_Run_Init                             0x0000715d   Thumb Code    34  rtc.o(i.RTC_Run_Init)
    RTC_SetTime                              0x0000717f   Thumb Code    92  rtc.o(i.RTC_SetTime)
    RTC_StructInit                           0x000071db   Thumb Code    34  xcm32lxx_rtc.o(i.RTC_StructInit)
    RTC_To_Sec                               0x000071fd   Thumb Code   204  rtc.o(i.RTC_To_Sec)
    RadarTurnOff                             0x000072dd   Thumb Code    46  user_main.o(i.RadarTurnOff)
    RadarTurnOn                              0x00007339   Thumb Code    52  user_main.o(i.RadarTurnOn)
    SnapModule_Off                           0x0000739d   Thumb Code    38  user_main.o(i.SnapModule_Off)
    SnapModule_On                            0x0000740d   Thumb Code    68  user_main.o(i.SnapModule_On)
    Switch_4G_Off                            0x00007489   Thumb Code    32  user_main.o(i.Switch_4G_Off)
    Switch_4G_On                             0x000074d9   Thumb Code    38  user_main.o(i.Switch_4G_On)
    SysTickConfigure                         0x00007535   Thumb Code    78  main.o(i.SysTickConfigure)
    SysTick_CLKSourceConfig                  0x00007589   Thumb Code    62  xcm32lxx_systick.o(i.SysTick_CLKSourceConfig)
    SysTick_Cmd                              0x000075cd   Thumb Code    32  xcm32lxx_systick.o(i.SysTick_Cmd)
    SysTick_Handler                          0x000075f1   Thumb Code    12  main.o(i.SysTick_Handler)
    SysTick_ITConfig                         0x00007601   Thumb Code    32  xcm32lxx_systick.o(i.SysTick_ITConfig)
    SysTick_SetReloadValue                   0x00007625   Thumb Code    10  xcm32lxx_systick.o(i.SysTick_SetReloadValue)
    SystemInit                               0x00007635   Thumb Code   236  system_xcm32l.o(i.SystemInit)
    TIMER_Cmd                                0x00007735   Thumb Code    34  xcm32lxx_timer.o(i.TIMER_Cmd)
    TIMER_DeInit                             0x00007759   Thumb Code   120  xcm32lxx_timer.o(i.TIMER_DeInit)
    TIMER_Init                               0x000077d9   Thumb Code   182  xcm32lxx_timer.o(i.TIMER_Init)
    TIMER_SetLoadCounter0                    0x00007899   Thumb Code     4  xcm32lxx_timer.o(i.TIMER_SetLoadCounter0)
    TIMER_SetLoadCounter1                    0x0000789d   Thumb Code    70  xcm32lxx_timer.o(i.TIMER_SetLoadCounter1)
    TIMER_StructInit                         0x000078ed   Thumb Code    12  xcm32lxx_timer.o(i.TIMER_StructInit)
    Test_ACK                                 0x000078f9   Thumb Code   104  i2cuart.o(i.Test_ACK)
    Timer1_PwmOut_Init                       0x00007965   Thumb Code    76  main.o(i.Timer1_PwmOut_Init)
    Timer2_PwmOut_Init                       0x000079b9   Thumb Code    74  main.o(i.Timer2_PwmOut_Init)
    UART1_IRQHandler                         0x00007a0d   Thumb Code   116  uart.o(i.UART1_IRQHandler)
    UART1_Init                               0x00007a89   Thumb Code   314  uart.o(i.UART1_Init)
    UART2_IRQHandler                         0x00007bcd   Thumb Code   104  uart.o(i.UART2_IRQHandler)
    UART2_Init                               0x00007c41   Thumb Code   212  uart.o(i.UART2_Init)
    UART4_IRQHandler                         0x00007d21   Thumb Code    54  uart.o(i.UART4_IRQHandler)
    UART4_Init                               0x00007d5d   Thumb Code   188  uart.o(i.UART4_Init)
    UART_DeInit                              0x00007e21   Thumb Code    88  xcm32lxx_uart.o(i.UART_DeInit)
    UART_FIFOModeConfig                      0x00007e8d   Thumb Code    84  xcm32lxx_uart.o(i.UART_FIFOModeConfig)
    UART_GetITStatus                         0x00007ee1   Thumb Code    20  xcm32lxx_uart.o(i.UART_GetITStatus)
    UART_GetLineStatus                       0x00007ef5   Thumb Code    18  xcm32lxx_uart.o(i.UART_GetLineStatus)
    UART_ITConfig                            0x00007f07   Thumb Code    20  xcm32lxx_uart.o(i.UART_ITConfig)
    UART_Init                                0x00007f1d   Thumb Code   680  xcm32lxx_uart.o(i.UART_Init)
    UART_PTXREModeConfig                     0x000081d9   Thumb Code    24  xcm32lxx_uart.o(i.UART_PTXREModeConfig)
    UART_ReceiveData                         0x000081f1   Thumb Code     8  xcm32lxx_uart.o(i.UART_ReceiveData)
    UART_SendData                            0x000081f9   Thumb Code     6  xcm32lxx_uart.o(i.UART_SendData)
    UART_StructInit                          0x000081ff   Thumb Code    24  xcm32lxx_uart.o(i.UART_StructInit)
    WDT_ClearITFlag                          0x00008219   Thumb Code    10  xcm32lxx_wdt.o(i.WDT_ClearITFlag)
    WDT_Cmd                                  0x00008229   Thumb Code    46  xcm32lxx_wdt.o(i.WDT_Cmd)
    WDT_Configure                            0x0000825d   Thumb Code   108  main.o(i.WDT_Configure)
    WDT_DeInit                               0x000082c9   Thumb Code    38  xcm32lxx_wdt.o(i.WDT_DeInit)
    WDT_GetITStatus                          0x000082f5   Thumb Code    16  xcm32lxx_wdt.o(i.WDT_GetITStatus)
    WDT_IRQHandler                           0x00008309   Thumb Code    22  main.o(i.WDT_IRQHandler)
    WDT_Init                                 0x00008321   Thumb Code    76  xcm32lxx_wdt.o(i.WDT_Init)
    WDT_RestartCmd                           0x00008371   Thumb Code    26  xcm32lxx_wdt.o(i.WDT_RestartCmd)
    WDT_StructInit                           0x00008395   Thumb Code    14  xcm32lxx_wdt.o(i.WDT_StructInit)
    __0printf                                0x000083a5   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x000083a5   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x000083a5   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x000083a5   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x000083a5   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x000083c5   Thumb Code    38  printfa.o(i.__0snprintf)
    __1snprintf                              0x000083c5   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x000083c5   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x000083c5   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x000083c5   Thumb Code     0  printfa.o(i.__0snprintf)
    __0sprintf                               0x000083f1   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x000083f1   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x000083f1   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x000083f1   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x000083f1   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x00008419   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_common_switch8                     0x00008447   Thumb Code    28  cjson.o(i.__ARM_common_switch8)
    __ARM_fpclassify                         0x00008465   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x00008491   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x0000853d   Thumb Code    16  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x00008551   Thumb Code     8  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x00008559   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x00008569   Thumb Code    16  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x0000857d   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x00008591   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0000859f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x000085a1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x000085b1   Thumb Code     6  errno.o(i.__set_errno)
    cJSON_AddItemToArray                     0x00008e6d   Thumb Code    48  cjson.o(i.cJSON_AddItemToArray)
    cJSON_AddItemToObject                    0x00008e9d   Thumb Code    48  cjson.o(i.cJSON_AddItemToObject)
    cJSON_CreateNumber                       0x00008ed1   Thumb Code    38  cjson.o(i.cJSON_CreateNumber)
    cJSON_CreateObject                       0x00008ef7   Thumb Code    20  cjson.o(i.cJSON_CreateObject)
    cJSON_CreateString                       0x00008f0b   Thumb Code    30  cjson.o(i.cJSON_CreateString)
    cJSON_Delete                             0x00008f29   Thumb Code   100  cjson.o(i.cJSON_Delete)
    cJSON_GetObjectItem                      0x00008f91   Thumb Code    32  cjson.o(i.cJSON_GetObjectItem)
    cJSON_Parse                              0x00008fd1   Thumb Code    16  cjson.o(i.cJSON_Parse)
    cJSON_ParseWithOpts                      0x00008fe1   Thumb Code   104  cjson.o(i.cJSON_ParseWithOpts)
    cJSON_Print                              0x0000904d   Thumb Code    18  cjson.o(i.cJSON_Print)
    cJsonParseProtocol                       0x000090dd   Thumb Code  5530  protol.o(i.cJsonParseProtocol)
    crc32                                    0x0000a6a5   Thumb Code   210  crc32.o(i.crc32)
    delay                                    0x0000a77d   Thumb Code    22  i2cuart.o(i.delay)
    delay_5us                                0x0000a793   Thumb Code    26  i2cuart.o(i.delay_5us)
    do_production_test_proc                  0x0000a7ad   Thumb Code   130  user_main.o(i.do_production_test_proc)
    enter_stop_mode                          0x0000a8ad   Thumb Code   108  user_main.o(i.enter_stop_mode)
    floor                                    0x0000a971   Thumb Code   180  floor.o(i.floor)
    fputc                                    0x0000aa39   Thumb Code    50  main.o(i.fputc)
    free                                     0x0000aa71   Thumb Code    80  malloc.o(i.free)
    get_voltage                              0x0000aac5   Thumb Code   138  i2cuart.o(i.get_voltage)
    main                                     0x0000ab75   Thumb Code   132  main.o(i.main)
    malloc                                   0x0000ac01   Thumb Code    92  malloc.o(i.malloc)
    pack_data                                0x0000ac6d   Thumb Code   142  uart.o(i.pack_data)
    pow                                      0x0000b405   Thumb Code  2548  pow.o(i.pow)
    send_transparent_info                    0x0000c9c9   Thumb Code   510  protol.o(i.send_transparent_info)
    sqrt                                     0x0000cd1d   Thumb Code    66  sqrt.o(i.sqrt)
    startSnap                                0x0000cd65   Thumb Code   110  user_main.o(i.startSnap)
    t_BCD2HEX                                0x0000ce3b   Thumb Code    76  rtc.o(i.t_BCD2HEX)
    t_HEX2BCD                                0x0000ce89   Thumb Code   168  rtc.o(i.t_HEX2BCD)
    uart1_rx_mengmu_process                  0x0000cf41   Thumb Code   772  uart.o(i.uart1_rx_mengmu_process)
    uart2_com_tx_data                        0x0000d25d   Thumb Code    64  uart.o(i.uart2_com_tx_data)
    uart2_find_head_and_tail                 0x0000d2a1   Thumb Code   324  uart.o(i.uart2_find_head_and_tail)
    uart2_rx_process                         0x0000d3e5   Thumb Code   496  uart.o(i.uart2_rx_process)
    uart2_unpack_data                        0x0000d5e9   Thumb Code   116  uart.o(i.uart2_unpack_data)
    uf_DMA_UartRxReqInit                     0x0000d679   Thumb Code   108  uart.o(i.uf_DMA_UartRxReqInit)
    uf_GPIO_CMU_Init                         0x0000d6e9   Thumb Code    30  rtc.o(i.uf_GPIO_CMU_Init)
    uf_GPIO_Init                             0x0000d70d   Thumb Code   294  gpio.o(i.uf_GPIO_Init)
    uf_GPIO_RTC_Init                         0x0000d841   Thumb Code    58  rtc.o(i.uf_GPIO_RTC_Init)
    uf_RTC_Init                              0x0000d881   Thumb Code   148  rtc.o(i.uf_RTC_Init)
    user_main                                0x0000d93d   Thumb Code  2250  user_main.o(i.user_main)
    mon_table                                0x0000e817   Data          12  rtc.o(.constdata)
    __mathlib_zero                           0x0000e8b0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0000e8d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0000e8f4   Number         0  anon$$obj.o(Region$$Table)
    g_sendFaild                              0x2000000c   Data           4  protol.o(.data)
    g_sendSuccess                            0x20000010   Data           4  protol.o(.data)
    firstretry                               0x20000014   Data           1  protol.o(.data)
    g_red_led_status                         0x20000018   Data           4  protol.o(.data)
    g_blue_led_status                        0x2000001c   Data           4  protol.o(.data)
    g_green_led_status                       0x20000020   Data           4  protol.o(.data)
    g_HaveCar                                0x20000024   Data           4  protol.o(.data)
    g_CurrentCarST                           0x20000028   Data           4  protol.o(.data)
    g_snapfinished                           0x2000002c   Data           4  protol.o(.data)
    startUpPeriod                            0x20000030   Data           4  protol.o(.data)
    g_retryCnt                               0x20000034   Data           1  protol.o(.data)
    TimingDelay                              0x20000060   Data           4  main.o(.data)
    uiSysTickCnt                             0x20000064   Data           4  main.o(.data)
    Milli_Flag                               0x20000068   Data           2  rtc.o(.data)
    RTCTime                                  0x2000006a   Data           9  rtc.o(.data)
    MSEC                                     0x20000073   Data           1  rtc.o(.data)
    SEC                                      0x20000074   Data           1  rtc.o(.data)
    MIN                                      0x20000075   Data           1  rtc.o(.data)
    HOUR                                     0x20000076   Data           1  rtc.o(.data)
    DAY                                      0x20000077   Data           1  rtc.o(.data)
    MONTH                                    0x20000078   Data           1  rtc.o(.data)
    YEAR                                     0x2000007a   Data           2  rtc.o(.data)
    g_UpdateTime                             0x20000088   Data           8  uart.o(.data)
    g_Test_ON                                0x20000098   Data           1  user_main.o(.data)
    gVersion                                 0x2000009c   Data           4  user_main.o(.data)
    mcuFirstOn                               0x200000a0   Data           1  user_main.o(.data)
    g_iVoltage                               0x200000a4   Data           4  user_main.o(.data)
    g_Time                                   0x200000a8   Data           8  user_main.o(.data)
    gPowerOffTime                            0x200000b0   Data           8  user_main.o(.data)
    gPeriodSetTime                           0x200000b8   Data           8  user_main.o(.data)
    gPeriodTrig4GTime                        0x200000c0   Data           8  user_main.o(.data)
    g_hi3516on                               0x200000c8   Data           4  user_main.o(.data)
    UploadSrvFlag                            0x200000cc   Data           1  user_main.o(.data)
    g_lastTriptype                           0x200000d0   Data           4  user_main.o(.data)
    g_UltraSonicOn                           0x200000d4   Data           4  user_main.o(.data)
    g_Status                                 0x200000d8   Data           4  user_main.o(.data)
    g_Radar_Status                           0x200000dc   Data           4  user_main.o(.data)
    g_RadarStopSTTime                        0x200000e0   Data           8  user_main.o(.data)
    g_trigtype                               0x200000e8   Data           4  user_main.o(.data)
    g_LastDistance                           0x200000ec   Data           4  user_main.o(.data)
    g_lastHaveCar                            0x200000f0   Data           4  user_main.o(.data)
    g_trigDis                                0x200000f4   Data           4  user_main.o(.data)
    g_firstQueryDis                          0x200000f8   Data           4  user_main.o(.data)
    g_4GOnTime                               0x20000100   Data           8  user_main.o(.data)
    g_XijieRadarIdx                          0x20000108   Data           4  user_main.o(.data)
    g_subIdx                                 0x2000010c   Data           4  user_main.o(.data)
    g_3516OnTime                             0x20000110   Data           8  user_main.o(.data)
    g_trigStableCnt                          0x20000118   Data           4  user_main.o(.data)
    g_idelCnt                                0x2000011c   Data           4  user_main.o(.data)
    g_ideaTrigDis                            0x20000120   Data           4  user_main.o(.data)
    g_TrigInterval                           0x20000124   Data           1  user_main.o(.data)
    g_energe                                 0x20000126   Data           2  user_main.o(.data)
    g_distance                               0x20000128   Data           2  user_main.o(.data)
    g_4GOn                                   0x2000012c   Data           4  user_main.o(.data)
    g_RadarOnTime                            0x20000130   Data           8  user_main.o(.data)
    g_RadarOffTime                           0x20000138   Data           8  user_main.o(.data)
    g_print_time                             0x20000140   Data           8  user_main.o(.data)
    do_production_test                       0x20000148   Data           1  user_main.o(.data)
    g_setCarST                               0x20000149   Data           1  user_main.o(.data)
    g_forceOut                               0x2000014a   Data           1  user_main.o(.data)
    g_peridCnt                               0x2000014c   Data           4  user_main.o(.data)
    g_trigCnt                                0x20000150   Data           4  user_main.o(.data)
    g_over3minCnt                            0x20000154   Data           4  user_main.o(.data)
    g_needRetry                              0x20000158   Data           1  user_main.o(.data)
    g_stopBoost                              0x20000159   Data           1  user_main.o(.data)
    __stdout                                 0x20000178   Data           4  stdout.o(.data)
    __microlib_freelist                      0x2000017c   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000180   Data           4  mvars.o(.data)
    RxReadPos                                0x20000188   Data          16  uart.o(.bss)
    RxWritePos                               0x20000198   Data          16  uart.o(.bss)
    uart2_RxBuf                              0x200001a8   Data        1024  uart.o(.bss)
    Uart1_RxBuf                              0x200005a8   Data         340  uart.o(.bss)
    uart_proc_buf                            0x200006fc   Data        1024  uart.o(.bss)
    uart_proced_buf                          0x20000afc   Data        1024  uart.o(.bss)
    g_XijieRadarInfo                         0x20000f00   Data         128  user_main.o(.bss)
    g_tmpval                                 0x20000f80   Data         400  user_main.o(.bss)
    __heap_base                              0x20001110   Data           0  startup_xcm32l.o(HEAP)
    __heap_limit                             0x20001d10   Data           0  startup_xcm32l.o(HEAP)
    __initial_sp                             0x20002110   Data           0  startup_xcm32l.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000050c1

  Load Region LR_IROM1 (Base: 0x00005000, Size: 0x00009a7c, Max: 0x0003b000, ABSOLUTE, COMPRESSED[0x00009930])

    Execution Region ER_IROM1 (Base: 0x00005000, Size: 0x000098f4, Max: 0x0003b000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00005000   0x000000c0   Data   RO            3    RESET               startup_xcm32l.o
    0x000050c0   0x00000000   Code   RO         3370  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000050c0   0x00000004   Code   RO         3755    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000050c4   0x00000004   Code   RO         3758    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000050c8   0x00000000   Code   RO         3760    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000050c8   0x00000000   Code   RO         3762    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000050c8   0x00000008   Code   RO         3763    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000050d0   0x00000000   Code   RO         3765    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000050d0   0x00000000   Code   RO         3767    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000050d0   0x00000004   Code   RO         3756    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000050d4   0x0000001c   Code   RO            4    .text               startup_xcm32l.o
    0x000050f0   0x00000028   Code   RO         3383    .text               mc_p.l(idiv.o)
    0x00005118   0x00000060   Code   RO         3385    .text               mc_p.l(uldiv.o)
    0x00005178   0x0000004c   Code   RO         3387    .text               mc_p.l(ldiv.o)
    0x000051c4   0x0000000c   Code   RO         3389    .text               mc_p.l(tolower.o)
    0x000051d0   0x00000008   Code   RO         3391    .text               mc_p.l(siabs.o)
    0x000051d8   0x00000024   Code   RO         3395    .text               mc_p.l(memcpya.o)
    0x000051fc   0x00000024   Code   RO         3397    .text               mc_p.l(memseta.o)
    0x00005220   0x00000014   Code   RO         3399    .text               mc_p.l(strchr.o)
    0x00005234   0x0000000e   Code   RO         3401    .text               mc_p.l(strlen.o)
    0x00005242   0x0000001c   Code   RO         3403    .text               mc_p.l(strcmp.o)
    0x0000525e   0x00000012   Code   RO         3405    .text               mc_p.l(strcpy.o)
    0x00005270   0x0000001e   Code   RO         3407    .text               mc_p.l(strncmp.o)
    0x0000528e   0x0000007c   Code   RO         3700    .text               mf_p.l(fdiv.o)
    0x0000530a   0x00000002   PAD
    0x0000530c   0x00000164   Code   RO         3702    .text               mf_p.l(dadd.o)
    0x00005470   0x000000d0   Code   RO         3704    .text               mf_p.l(dmul.o)
    0x00005540   0x000000f0   Code   RO         3706    .text               mf_p.l(ddiv.o)
    0x00005630   0x0000000e   Code   RO         3708    .text               mf_p.l(ffltui.o)
    0x0000563e   0x00000002   PAD
    0x00005640   0x00000028   Code   RO         3710    .text               mf_p.l(dflti.o)
    0x00005668   0x0000001c   Code   RO         3712    .text               mf_p.l(dfltui.o)
    0x00005684   0x00000028   Code   RO         3714    .text               mf_p.l(ffixui.o)
    0x000056ac   0x00000048   Code   RO         3716    .text               mf_p.l(dfixi.o)
    0x000056f4   0x0000003c   Code   RO         3718    .text               mf_p.l(dfixui.o)
    0x00005730   0x00000028   Code   RO         3720    .text               mf_p.l(f2d.o)
    0x00005758   0x00000028   Code   RO         3722    .text               mf_p.l(cdcmple.o)
    0x00005780   0x00000028   Code   RO         3724    .text               mf_p.l(cdrcmple.o)
    0x000057a8   0x00000038   Code   RO         3726    .text               mf_p.l(d2f.o)
    0x000057e0   0x0000002c   Code   RO         3781    .text               mc_p.l(uidiv.o)
    0x0000580c   0x00000020   Code   RO         3783    .text               mc_p.l(llshl.o)
    0x0000582c   0x00000022   Code   RO         3785    .text               mc_p.l(llushr.o)
    0x0000584e   0x00000026   Code   RO         3787    .text               mc_p.l(llsshr.o)
    0x00005874   0x00000000   Code   RO         3798    .text               mc_p.l(iusefp.o)
    0x00005874   0x00000082   Code   RO         3799    .text               mf_p.l(fepilogue.o)
    0x000058f6   0x000000be   Code   RO         3801    .text               mf_p.l(depilogue.o)
    0x000059b4   0x0000002c   Code   RO         3805    .text               mf_p.l(dscalb.o)
    0x000059e0   0x00000040   Code   RO         3807    .text               mf_p.l(dfixul.o)
    0x00005a20   0x00000024   Code   RO         3809    .text               mc_p.l(init.o)
    0x00005a44   0x000000a2   Code   RO         3813    .text               mf_p.l(dsqrt.o)
    0x00005ae6   0x00000056   Code   RO         3823    .text               mc_p.l(__dczerorl2.o)
    0x00005b3c   0x000000fc   Code   RO          757    i.Booster_DeInit    main.o
    0x00005c38   0x000000a4   Code   RO          758    i.Booster_Init      main.o
    0x00005cdc   0x0000009c   Code   RO          759    i.Booster_Start     main.o
    0x00005d78   0x000000f4   Code   RO          760    i.Booster_Stop      main.o
    0x00005e6c   0x00000020   Code   RO         1455    i.CMU_APBPeriph0ClockCmd  xcm32lxx_cmu.o
    0x00005e8c   0x00000020   Code   RO         1456    i.CMU_APBPeriph1ClockCmd  xcm32lxx_cmu.o
    0x00005eac   0x00000044   Code   RO         1460    i.CMU_GetSysClkFreq  xcm32lxx_cmu.o
    0x00005ef0   0x00000040   Code   RO         1461    i.CMU_GetSysClkSource  xcm32lxx_cmu.o
    0x00005f30   0x00000034   Code   RO         1462    i.CMU_HCLKConfig    xcm32lxx_cmu.o
    0x00005f64   0x00000080   Code   RO         1464    i.CMU_HSIConfig     xcm32lxx_cmu.o
    0x00005fe4   0x00000060   Code   RO         1468    i.CMU_LSEConfig     xcm32lxx_cmu.o
    0x00006044   0x00000064   Code   RO         1469    i.CMU_LSIConfig     xcm32lxx_cmu.o
    0x000060a8   0x00000034   Code   RO         1471    i.CMU_PCLKConfig    xcm32lxx_cmu.o
    0x000060dc   0x0000002c   Code   RO         1473    i.CMU_RTCCLKConfig  xcm32lxx_cmu.o
    0x00006108   0x00000030   Code   RO         1474    i.CMU_SysClkConfig  xcm32lxx_cmu.o
    0x00006138   0x00000034   Code   RO         1475    i.CMU_WDTCLKConfig  xcm32lxx_cmu.o
    0x0000616c   0x00000090   Code   RO         1476    i.CMU_WaitForSysClkStartUp  xcm32lxx_cmu.o
    0x000061fc   0x00000044   Code   RO         1028    i.DMAC_IRQHandler   uart.o
    0x00006240   0x00000014   Code   RO         1737    i.DMA_ClearITFlag   xcm32lxx_dma.o
    0x00006254   0x00000020   Code   RO         1738    i.DMA_Cmd           xcm32lxx_dma.o
    0x00006274   0x00000030   Code   RO         1739    i.DMA_DeInit        xcm32lxx_dma.o
    0x000062a4   0x00000024   Code   RO         1742    i.DMA_GetITStatus   xcm32lxx_dma.o
    0x000062c8   0x0000002c   Code   RO         1744    i.DMA_ITConfig      xcm32lxx_dma.o
    0x000062f4   0x000002c4   Code   RO         1745    i.DMA_Init          xcm32lxx_dma.o
    0x000065b8   0x0000004a   Code   RO         1746    i.DMA_StructInit    xcm32lxx_dma.o
    0x00006602   0x0000001e   Code   RO          761    i.DelayMs           main.o
    0x00006620   0x0000001a   Code   RO          762    i.Delay_ms          main.o
    0x0000663a   0x00000002   PAD
    0x0000663c   0x00000118   Code   RO          919    i.GetRtcSecond      rtc.o
    0x00006754   0x0000000c   Code   RO          763    i.GetSysTick        main.o
    0x00006760   0x0000004c   Code   RO          652    i.I2C1_ReceiveData  i2cuart.o
    0x000067ac   0x00000042   Code   RO          653    i.I2C1_TransmitData  i2cuart.o
    0x000067ee   0x00000002   PAD
    0x000067f0   0x0000003c   Code   RO          654    i.I2C_Start         i2cuart.o
    0x0000682c   0x0000003c   Code   RO          655    i.I2C_Stop          i2cuart.o
    0x00006868   0x00000024   Code   RO          656    i.I2C_init          i2cuart.o
    0x0000688c   0x0000007c   Code   RO          657    i.I2C_read_byte     i2cuart.o
    0x00006908   0x00000084   Code   RO          658    i.I2C_send_byte     i2cuart.o
    0x0000698c   0x0000003c   Code   RO          920    i.Is_Leap_Year      rtc.o
    0x000069c8   0x0000006c   Code   RO          659    i.Master_ACK        i2cuart.o
    0x00006a34   0x000000ec   Code   RO         1218    i.MengMuRadarReceive  user_main.o
    0x00006b20   0x00000014   Code   RO          764    i.NVIC_ClearPendingIRQ  main.o
    0x00006b34   0x00000014   Code   RO         1029    i.NVIC_ClearPendingIRQ  uart.o
    0x00006b48   0x00000014   Code   RO          765    i.NVIC_DisableIRQ   main.o
    0x00006b5c   0x00000014   Code   RO         1030    i.NVIC_DisableIRQ   uart.o
    0x00006b70   0x00000014   Code   RO          766    i.NVIC_EnableIRQ    main.o
    0x00006b84   0x00000014   Code   RO         1031    i.NVIC_EnableIRQ    uart.o
    0x00006b98   0x0000003c   Code   RO          921    i.NVIC_Init_rtc     rtc.o
    0x00006bd4   0x00000078   Code   RO          767    i.NVIC_SetPriority  main.o
    0x00006c4c   0x00000078   Code   RO          922    i.NVIC_SetPriority  rtc.o
    0x00006cc4   0x00000078   Code   RO         1032    i.NVIC_SetPriority  uart.o
    0x00006d3c   0x00000034   Code   RO         2384    i.PMU_EnterDeepSleep3Mode  xcm32lxx_pmu.o
    0x00006d70   0x0000002c   Code   RO         2428    i.PORT_DeInit       xcm32lxx_port.o
    0x00006d9c   0x0000001c   Code   RO          660    i.PORT_DirSet       i2cuart.o
    0x00006db8   0x00000070   Code   RO         2434    i.PORT_Init         xcm32lxx_port.o
    0x00006e28   0x0000004a   Code   RO         2435    i.PORT_PinAFConfig  xcm32lxx_port.o
    0x00006e72   0x00000012   Code   RO         2437    i.PORT_ReadInputDataBit  xcm32lxx_port.o
    0x00006e84   0x0000001c   Code   RO         2442    i.PORT_StructInit   xcm32lxx_port.o
    0x00006ea0   0x0000002c   Code   RO         2443    i.PORT_ToggleBit    xcm32lxx_port.o
    0x00006ecc   0x00000040   Code   RO         2445    i.PORT_WriteBit     xcm32lxx_port.o
    0x00006f0c   0x00000014   Code   RO         2763    i.RTC_ClearITFlag   xcm32lxx_rtc.o
    0x00006f20   0x00000024   Code   RO         2764    i.RTC_Cmd           xcm32lxx_rtc.o
    0x00006f44   0x0000002c   Code   RO         2765    i.RTC_DeInit        xcm32lxx_rtc.o
    0x00006f70   0x00000010   Code   RO         2766    i.RTC_GetDay        xcm32lxx_rtc.o
    0x00006f80   0x00000010   Code   RO         2767    i.RTC_GetHour       xcm32lxx_rtc.o
    0x00006f90   0x00000014   Code   RO         2768    i.RTC_GetITStatus   xcm32lxx_rtc.o
    0x00006fa4   0x00000010   Code   RO         2769    i.RTC_GetMinute     xcm32lxx_rtc.o
    0x00006fb4   0x00000010   Code   RO         2770    i.RTC_GetMonth      xcm32lxx_rtc.o
    0x00006fc4   0x00000010   Code   RO         2771    i.RTC_GetSecond     xcm32lxx_rtc.o
    0x00006fd4   0x00000014   Code   RO         2773    i.RTC_GetYear       xcm32lxx_rtc.o
    0x00006fe8   0x00000010   Code   RO          924    i.RTC_IRQHandler    rtc.o
    0x00006ff8   0x00000080   Code   RO         2776    i.RTC_ITConfig      xcm32lxx_rtc.o
    0x00007078   0x000000e4   Code   RO         2777    i.RTC_Init          xcm32lxx_rtc.o
    0x0000715c   0x00000022   Code   RO          925    i.RTC_Run_Init      rtc.o
    0x0000717e   0x0000005c   Code   RO          926    i.RTC_SetTime       rtc.o
    0x000071da   0x00000022   Code   RO         2779    i.RTC_StructInit    xcm32lxx_rtc.o
    0x000071fc   0x000000e0   Code   RO          927    i.RTC_To_Sec        rtc.o
    0x000072dc   0x0000005c   Code   RO         1219    i.RadarTurnOff      user_main.o
    0x00007338   0x00000060   Code   RO         1220    i.RadarTurnOn       user_main.o
    0x00007398   0x00000002   Code   RO           12    i.SetSysClock       system_xcm32l.o
    0x0000739a   0x00000002   PAD
    0x0000739c   0x00000070   Code   RO         1221    i.SnapModule_Off    user_main.o
    0x0000740c   0x0000007c   Code   RO         1222    i.SnapModule_On     user_main.o
    0x00007488   0x00000050   Code   RO         1223    i.Switch_4G_Off     user_main.o
    0x000074d8   0x0000005c   Code   RO         1224    i.Switch_4G_On      user_main.o
    0x00007534   0x00000054   Code   RO          769    i.SysTickConfigure  main.o
    0x00007588   0x00000044   Code   RO         2985    i.SysTick_CLKSourceConfig  xcm32lxx_systick.o
    0x000075cc   0x00000024   Code   RO         2987    i.SysTick_Cmd       xcm32lxx_systick.o
    0x000075f0   0x00000010   Code   RO          770    i.SysTick_Handler   main.o
    0x00007600   0x00000024   Code   RO         2991    i.SysTick_ITConfig  xcm32lxx_systick.o
    0x00007624   0x00000010   Code   RO         2993    i.SysTick_SetReloadValue  xcm32lxx_systick.o
    0x00007634   0x00000100   Code   RO           14    i.SystemInit        system_xcm32l.o
    0x00007734   0x00000022   Code   RO         3055    i.TIMER_Cmd         xcm32lxx_timer.o
    0x00007756   0x00000002   PAD
    0x00007758   0x00000080   Code   RO         3056    i.TIMER_DeInit      xcm32lxx_timer.o
    0x000077d8   0x000000c0   Code   RO         3062    i.TIMER_Init        xcm32lxx_timer.o
    0x00007898   0x00000004   Code   RO         3063    i.TIMER_SetLoadCounter0  xcm32lxx_timer.o
    0x0000789c   0x00000050   Code   RO         3064    i.TIMER_SetLoadCounter1  xcm32lxx_timer.o
    0x000078ec   0x0000000c   Code   RO         3065    i.TIMER_StructInit  xcm32lxx_timer.o
    0x000078f8   0x0000006c   Code   RO          661    i.Test_ACK          i2cuart.o
    0x00007964   0x00000054   Code   RO          771    i.Timer1_PwmOut_Init  main.o
    0x000079b8   0x00000054   Code   RO          772    i.Timer2_PwmOut_Init  main.o
    0x00007a0c   0x0000007c   Code   RO         1034    i.UART1_IRQHandler  uart.o
    0x00007a88   0x00000144   Code   RO         1036    i.UART1_Init        uart.o
    0x00007bcc   0x00000074   Code   RO         1038    i.UART2_IRQHandler  uart.o
    0x00007c40   0x000000e0   Code   RO         1039    i.UART2_Init        uart.o
    0x00007d20   0x0000003c   Code   RO         1041    i.UART4_IRQHandler  uart.o
    0x00007d5c   0x000000c4   Code   RO         1042    i.UART4_Init        uart.o
    0x00007e20   0x0000006c   Code   RO         3145    i.UART_DeInit       xcm32lxx_uart.o
    0x00007e8c   0x00000054   Code   RO         3146    i.UART_FIFOModeConfig  xcm32lxx_uart.o
    0x00007ee0   0x00000014   Code   RO         3147    i.UART_GetITStatus  xcm32lxx_uart.o
    0x00007ef4   0x00000012   Code   RO         3148    i.UART_GetLineStatus  xcm32lxx_uart.o
    0x00007f06   0x00000014   Code   RO         3150    i.UART_ITConfig     xcm32lxx_uart.o
    0x00007f1a   0x00000002   PAD
    0x00007f1c   0x000002bc   Code   RO         3151    i.UART_Init         xcm32lxx_uart.o
    0x000081d8   0x00000018   Code   RO         3152    i.UART_PTXREModeConfig  xcm32lxx_uart.o
    0x000081f0   0x00000008   Code   RO         3153    i.UART_ReceiveData  xcm32lxx_uart.o
    0x000081f8   0x00000006   Code   RO         3154    i.UART_SendData     xcm32lxx_uart.o
    0x000081fe   0x00000018   Code   RO         3155    i.UART_StructInit   xcm32lxx_uart.o
    0x00008216   0x00000002   PAD
    0x00008218   0x00000010   Code   RO         3296    i.WDT_ClearITFlag   xcm32lxx_wdt.o
    0x00008228   0x00000034   Code   RO         3297    i.WDT_Cmd           xcm32lxx_wdt.o
    0x0000825c   0x0000006c   Code   RO          773    i.WDT_Configure     main.o
    0x000082c8   0x0000002c   Code   RO         3298    i.WDT_DeInit        xcm32lxx_wdt.o
    0x000082f4   0x00000014   Code   RO         3300    i.WDT_GetITStatus   xcm32lxx_wdt.o
    0x00008308   0x00000016   Code   RO          775    i.WDT_IRQHandler    main.o
    0x0000831e   0x00000002   PAD
    0x00008320   0x00000050   Code   RO         3301    i.WDT_Init          xcm32lxx_wdt.o
    0x00008370   0x00000024   Code   RO         3302    i.WDT_RestartCmd    xcm32lxx_wdt.o
    0x00008394   0x0000000e   Code   RO         3303    i.WDT_StructInit    xcm32lxx_wdt.o
    0x000083a2   0x00000002   PAD
    0x000083a4   0x00000020   Code   RO         3644    i.__0printf         mc_p.l(printfa.o)
    0x000083c4   0x0000002c   Code   RO         3645    i.__0snprintf       mc_p.l(printfa.o)
    0x000083f0   0x00000028   Code   RO         3646    i.__0sprintf        mc_p.l(printfa.o)
    0x00008418   0x0000002e   Code   RO         3803    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00008446   0x0000001c   Code   RO          435    i.__ARM_common_switch8  cjson.o
    0x00008462   0x00000002   PAD
    0x00008464   0x0000002c   Code   RO         3742    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00008490   0x000000ac   Code   RO         3744    i.__kernel_poly     m_ps.l(poly.o)
    0x0000853c   0x00000014   Code   RO         3728    i.__mathlib_dbl_divzero  m_ps.l(dunder.o)
    0x00008550   0x00000008   Code   RO         3730    i.__mathlib_dbl_infnan2  m_ps.l(dunder.o)
    0x00008558   0x00000010   Code   RO         3731    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x00008568   0x00000014   Code   RO         3732    i.__mathlib_dbl_overflow  m_ps.l(dunder.o)
    0x0000857c   0x00000014   Code   RO         3734    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x00008590   0x0000000e   Code   RO         3817    i.__scatterload_copy  mc_p.l(handlers.o)
    0x0000859e   0x00000002   Code   RO         3818    i.__scatterload_null  mc_p.l(handlers.o)
    0x000085a0   0x0000000e   Code   RO         3819    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x000085ae   0x00000002   PAD
    0x000085b0   0x0000000c   Code   RO         3793    i.__set_errno       mc_p.l(errno.o)
    0x000085bc   0x00000158   Code   RO         3651    i._fp_digits        mc_p.l(printfa.o)
    0x00008714   0x000006ec   Code   RO         3652    i._printf_core      mc_p.l(printfa.o)
    0x00008e00   0x00000020   Code   RO         3653    i._printf_post_padding  mc_p.l(printfa.o)
    0x00008e20   0x0000002c   Code   RO         3654    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00008e4c   0x00000016   Code   RO         3655    i._snputc           mc_p.l(printfa.o)
    0x00008e62   0x0000000a   Code   RO         3656    i._sputc            mc_p.l(printfa.o)
    0x00008e6c   0x00000030   Code   RO          104    i.cJSON_AddItemToArray  cjson.o
    0x00008e9c   0x00000034   Code   RO          105    i.cJSON_AddItemToObject  cjson.o
    0x00008ed0   0x00000026   Code   RO          114    i.cJSON_CreateNumber  cjson.o
    0x00008ef6   0x00000014   Code   RO          115    i.cJSON_CreateObject  cjson.o
    0x00008f0a   0x0000001e   Code   RO          116    i.cJSON_CreateString  cjson.o
    0x00008f28   0x00000068   Code   RO          119    i.cJSON_Delete      cjson.o
    0x00008f90   0x00000020   Code   RO          128    i.cJSON_GetObjectItem  cjson.o
    0x00008fb0   0x00000020   Code   RO          132    i.cJSON_New_Item    cjson.o
    0x00008fd0   0x00000010   Code   RO          133    i.cJSON_Parse       cjson.o
    0x00008fe0   0x0000006c   Code   RO          134    i.cJSON_ParseWithOpts  cjson.o
    0x0000904c   0x00000012   Code   RO          135    i.cJSON_Print       cjson.o
    0x0000905e   0x00000050   Code   RO          140    i.cJSON_strcasecmp  cjson.o
    0x000090ae   0x00000002   PAD
    0x000090b0   0x0000002c   Code   RO          141    i.cJSON_strdup      cjson.o
    0x000090dc   0x000015c8   Code   RO          468    i.cJsonParseProtocol  protol.o
    0x0000a6a4   0x000000d8   Code   RO          573    i.crc32             crc32.o
    0x0000a77c   0x00000016   Code   RO          662    i.delay             i2cuart.o
    0x0000a792   0x0000001a   Code   RO          663    i.delay_5us         i2cuart.o
    0x0000a7ac   0x0000008c   Code   RO         1225    i.do_production_test_proc  user_main.o
    0x0000a838   0x00000074   Code   RO          143    i.ensure            cjson.o
    0x0000a8ac   0x000000c4   Code   RO         1226    i.enter_stop_mode   user_main.o
    0x0000a970   0x000000c8   Code   RO         3357    i.floor             m_ps.l(floor.o)
    0x0000aa38   0x00000038   Code   RO          776    i.fputc             main.o
    0x0000aa70   0x00000054   Code   RO         3672    i.free              mc_p.l(malloc.o)
    0x0000aac4   0x000000b0   Code   RO          664    i.get_voltage       i2cuart.o
    0x0000ab74   0x0000008c   Code   RO          779    i.main              main.o
    0x0000ac00   0x0000006c   Code   RO         3673    i.malloc            mc_p.l(malloc.o)
    0x0000ac6c   0x00000094   Code   RO         1043    i.pack_data         uart.o
    0x0000ad00   0x000000a8   Code   RO          144    i.parse_array       cjson.o
    0x0000ada8   0x00000114   Code   RO          145    i.parse_hex4        cjson.o
    0x0000aebc   0x0000018c   Code   RO          146    i.parse_number      cjson.o
    0x0000b048   0x00000110   Code   RO          147    i.parse_object      cjson.o
    0x0000b158   0x000001ec   Code   RO          148    i.parse_string      cjson.o
    0x0000b344   0x000000c0   Code   RO          149    i.parse_value       cjson.o
    0x0000b404   0x000009fc   Code   RO         3361    i.pow               m_ps.l(pow.o)
    0x0000be00   0x0000001c   Code   RO          150    i.pow2gt            cjson.o
    0x0000be1c   0x00000260   Code   RO          151    i.print_array       cjson.o
    0x0000c07c   0x000001a8   Code   RO          152    i.print_number      cjson.o
    0x0000c224   0x00000488   Code   RO          153    i.print_object      cjson.o
    0x0000c6ac   0x00000010   Code   RO          154    i.print_string      cjson.o
    0x0000c6bc   0x000001d8   Code   RO          155    i.print_string_ptr  cjson.o
    0x0000c894   0x00000134   Code   RO          156    i.print_value       cjson.o
    0x0000c9c8   0x0000033c   Code   RO          476    i.send_transparent_info  protol.o
    0x0000cd04   0x00000016   Code   RO          157    i.skip              cjson.o
    0x0000cd1a   0x00000002   PAD
    0x0000cd1c   0x00000048   Code   RO         3748    i.sqrt              m_ps.l(sqrt.o)
    0x0000cd64   0x000000d0   Code   RO         1227    i.startSnap         user_main.o
    0x0000ce34   0x00000006   Code   RO          158    i.suffix_object     cjson.o
    0x0000ce3a   0x0000004c   Code   RO          928    i.t_BCD2HEX         rtc.o
    0x0000ce86   0x00000002   PAD
    0x0000ce88   0x000000b8   Code   RO          929    i.t_HEX2BCD         rtc.o
    0x0000cf40   0x0000031c   Code   RO         1048    i.uart1_rx_mengmu_process  uart.o
    0x0000d25c   0x00000044   Code   RO         1049    i.uart2_com_tx_data  uart.o
    0x0000d2a0   0x00000144   Code   RO         1050    i.uart2_find_head_and_tail  uart.o
    0x0000d3e4   0x00000204   Code   RO         1051    i.uart2_rx_process  uart.o
    0x0000d5e8   0x00000090   Code   RO         1052    i.uart2_unpack_data  uart.o
    0x0000d678   0x00000070   Code   RO         1053    i.uf_DMA_UartRxReqInit  uart.o
    0x0000d6e8   0x00000024   Code   RO          930    i.uf_GPIO_CMU_Init  rtc.o
    0x0000d70c   0x00000134   Code   RO          622    i.uf_GPIO_Init      gpio.o
    0x0000d840   0x00000040   Code   RO          931    i.uf_GPIO_RTC_Init  rtc.o
    0x0000d880   0x00000098   Code   RO          933    i.uf_RTC_Init       rtc.o
    0x0000d918   0x00000024   Code   RO          159    i.update            cjson.o
    0x0000d93c   0x00000a9c   Code   RO         1228    i.user_main         user_main.o
    0x0000e3d8   0x00000007   Data   RO          160    .constdata          cjson.o
    0x0000e3df   0x00000001   PAD
    0x0000e3e0   0x00000400   Data   RO          574    .constdata          crc32.o
    0x0000e7e0   0x00000037   Data   RO          780    .constdata          main.o
    0x0000e817   0x0000000c   Data   RO          934    .constdata          rtc.o
    0x0000e823   0x00000005   PAD
    0x0000e828   0x00000088   Data   RO         3362    .constdata          m_ps.l(pow.o)
    0x0000e8b0   0x00000008   Data   RO         3746    .constdata          m_ps.l(qnan.o)
    0x0000e8b8   0x0000001a   Data   RO         1230    .conststring        user_main.o
    0x0000e8d2   0x00000002   PAD
    0x0000e8d4   0x00000020   Data   RO         3815    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00002110, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x0000003c])

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0000000c   Data   RW          161    .data               cjson.o
    0x2000000c   0x00000029   Data   RW          479    .data               protol.o
    0x20000035   0x00000003   PAD
    0x20000038   0x00000028   Data   RW          667    .data               i2cuart.o
    0x20000060   0x00000008   Data   RW          781    .data               main.o
    0x20000068   0x00000020   Data   RW          935    .data               rtc.o
    0x20000088   0x0000000e   Data   RW         1056    .data               uart.o
    0x20000096   0x00000002   PAD
    0x20000098   0x000000c2   Data   RW         1231    .data               user_main.o
    0x2000015a   0x00000002   PAD
    0x2000015c   0x0000001c   Data   RW         2446    .data               xcm32lxx_port.o
    0x20000178   0x00000004   Data   RW         3780    .data               mc_p.l(stdout.o)
    0x2000017c   0x00000004   Data   RW         3789    .data               mc_p.l(mvars.o)
    0x20000180   0x00000004   Data   RW         3790    .data               mc_p.l(mvars.o)
    0x20000184   0x00000004   Data   RW         3794    .data               mc_p.l(errno.o)
    0x20000188   0x00000d74   Zero   RW         1055    .bss                uart.o
    0x20000efc   0x00000004   PAD
    0x20000f00   0x00000210   Zero   RW         1229    .bss                user_main.o
    0x20001110   0x00000c00   Zero   RW            2    HEAP                startup_xcm32l.o
    0x20001d10   0x00000400   Zero   RW            1    STACK               startup_xcm32l.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      5642        256          7         12          0      25001   cjson.o
       216          6       1024          0          0       1268   crc32.o
       308         14          0          0          0        575   gpio.o
      1022         70          0         40          0       7828   i2cuart.o
      1658        252         55          8          0      15304   main.o
      6404       1900          0         41          0      10948   protol.o
      1398        146         12         32          0      13981   rtc.o
        28          8        192          0       4096        640   startup_xcm32l.o
       258         20          0          0          0      84273   system_xcm32l.o
      3400        190          0         14       3444      18228   uart.o
      4092       1496         26        194        528       8682   user_main.o
       912         74          0          0          0       7690   xcm32lxx_cmu.o
       962         34          0          0          0       4258   xcm32lxx_dma.o
        52          6          0          0          0        498   xcm32lxx_pmu.o
       384         32          0         28          0       4484   xcm32lxx_port.o
       610         64          0          0          0       6354   xcm32lxx_rtc.o
       156         20          0          0          0       1948   xcm32lxx_systick.o
       450         28          0          0          0       3747   xcm32lxx_timer.o
      1012         40          0          0          0       6304   xcm32lxx_uart.o
       262         36          0          0          0       3556   xcm32lxx_wdt.o

    ----------------------------------------------------------------------
     29250       <USER>       <GROUP>        376       8072     225567   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          8          7          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        84         14          0          0          0        340   dunder.o
       200         20          0          0          0         76   floor.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
      2556        196        136          0          0        240   pow.o
         0          0          8          0          0          0   qnan.o
        72          6          0          0          0         76   sqrt.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         60   errno.o
        30          0          0          0          0          0   handlers.o
        40          0          0          0          0         72   idiv.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        76          0          0          0          0         76   ldiv.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
       192         20          0          0          0        144   malloc.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
         0          0          0          8          0          0   mvars.o
      2340        108          0          0          0        700   printfa.o
         8          0          0          0          0         60   siabs.o
         0          0          0          4          0          0   stdout.o
        20          0          0          0          0         60   strchr.o
        28          0          0          0          0         68   strcmp.o
        18          0          0          0          0         60   strcpy.o
        14          0          0          0          0         60   strlen.o
        30          0          0          0          0         72   strncmp.o
        12          0          0          0          0         60   tolower.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdcmple.o
        40          2          0          0          0         68   cdrcmple.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        60         10          0          0          0         68   dfixui.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
        28          4          0          0          0         68   dfltui.o
       208          6          0          0          0         88   dmul.o
        44          0          0          0          0         72   dscalb.o
       162          0          0          0          0         80   dsqrt.o
        40          0          0          0          0         60   f2d.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        40          0          0          0          0         60   ffixui.o
        14          0          0          0          0         68   ffltui.o

    ----------------------------------------------------------------------
      8406        <USER>        <GROUP>         16          0       4580   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3128        240        144          0          0        868   m_ps.l
      3278        150          0         16          0       2080   mc_p.l
      1994         60          0          0          0       1632   mf_p.l

    ----------------------------------------------------------------------
      8406        <USER>        <GROUP>         16          0       4580   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     37656       5142       1500        392       8072     220235   Grand Totals
     37656       5142       1500         60       8072     220235   ELF Image Totals (compressed)
     37656       5142       1500         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                39156 (  38.24kB)
    Total RW  Size (RW Data + ZI Data)              8464 (   8.27kB)
    Total ROM Size (Code + RO Data + RW Data)      39216 (  38.30kB)

==============================================================================

