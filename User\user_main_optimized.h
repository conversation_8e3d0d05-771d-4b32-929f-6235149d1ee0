/**
 * @file    user_main_optimized.h
 * @brief   Optimized user main application header file
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _USER_MAIN_OPTIMIZED_H_
#define _USER_MAIN_OPTIMIZED_H_

/* Includes ------------------------------------------------------------------*/
#include "system_config.h"

/* Exported function prototypes ----------------------------------------------*/

/* Main application function */
/**
 * @brief  Main user application function
 * @param  None
 * @retval None
 */
void user_main(void);

/* Device control functions */
/**
 * @brief  Turn on radar
 * @param  None
 * @retval None
 */
void RadarTurnOn(void);

/**
 * @brief  Turn off radar
 * @param  None
 * @retval None
 */
void RadarTurnOff(void);

/**
 * @brief  Turn on 4G module
 * @param  None
 * @retval None
 */
void Switch_4G_On(void);

/**
 * @brief  Turn off 4G module
 * @param  None
 * @retval None
 */
void Switch_4G_Off(void);

/**
 * @brief  Turn on snap module (HI3516)
 * @param  None
 * @retval None
 */
void SnapModule_On(void);

/**
 * @brief  Turn off snap module (HI3516)
 * @param  None
 * @retval None
 */
void SnapModule_Off(void);

/* Radar processing functions */
/**
 * @brief  Process radar data reception
 * @param  None
 * @retval None
 */
void MengMuRadarReceive(void);

/**
 * @brief  Process radar data from UART1
 * @param  pEnerge: pointer to energy value
 * @param  distance: pointer to distance value
 * @retval 0 if data processed, -1 if no data
 */
int uart1_rx_mengmu_process(unsigned short *pEnerge, unsigned short *distance);

/* Communication functions */
/**
 * @brief  Process received data from UART2
 * @param  type: pointer to device type
 * @param  buffer: pointer to data buffer pointer
 * @param  len: pointer to data length
 * @retval 0 if data processed, -1 if no data
 */
int uart2_rx_process(int *type, unsigned char **buffer, int *len);

/* Utility functions */
/**
 * @brief  Get battery voltage
 * @param  None
 * @retval Battery voltage in volts
 */
float get_voltage(void);

/**
 * @brief  Convert HEX to BCD
 * @param  hex_val: HEX value to convert
 * @retval BCD value
 */
uint8_t t_HEX2BCD(uint8_t hex_val);

/**
 * @brief  Convert BCD to HEX
 * @param  bcd_val: BCD value to convert
 * @retval HEX value
 */
uint8_t t_BCD2HEX(uint8_t bcd_val);

/**
 * @brief  Get RTC time in seconds
 * @param  None
 * @retval RTC time in seconds since epoch
 */
long long GetRtcSecond(void);

/* Data packing functions */
/**
 * @brief  Pack data for transmission
 * @param  dev_type: device type
 * @param  pack_type: packet type
 * @param  total_packages: total packages
 * @param  seq: sequence number
 * @param  data: input data buffer
 * @param  data_len: input data length
 * @param  packed_data: output packed data buffer
 * @param  packed_len: pointer to packed data length
 * @retval 0 if successful, -1 if failed
 */
int pack_data(int dev_type, int pack_type, int total_packages, int seq,
              unsigned char* data, int data_len, 
              unsigned char* packed_data, int *packed_len);

/* Power management functions */
/**
 * @brief  Enter deep sleep mode 3
 * @param  mode: enter mode
 * @retval None
 */
void PMU_EnterDeepSleep3Mode(int mode);

/* Constants for power management */
#define PMU_EnterMode_Now           0

#endif /* _USER_MAIN_OPTIMIZED_H_ */
