#ifndef _UART_C_
#define _UART_C_

#include "XCM32Lxx_conf.h"
#include <stdarg.h>
#include <stdio.h>
#include "user_main.h"
#include "uart.h"
#include "XCM32Lxx_dma.h"
#include "main.h"

#define 	_____UART1_Define

extern int g_subIdx;
extern int g_XijieRadarIdx;
extern unsigned short g_energe;
extern unsigned short g_distance;
extern long long g_Time;
#define uart2_tx_TimeOut  (1000)

void uf_DMA_UartRxReqInit(void)
{
  DMA_InitTypeDef DMA_InitStruct;
  
  CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_DMA, ENABLE);
  DMA_DeInit();
  DMA_StructInit(&DMA_InitStruct);
  DMA_InitStruct.DMA_DstAddr = (uint32_t)Uart1_RxBuf;
  DMA_InitStruct.DMA_SrcAddr = (uint32_t)&(UART_1->RXR);
  DMA_InitStruct.DMA_TransDir = DMA_TransDir_P2M;
  DMA_InitStruct.DMA_Mode = DMA_Mode_Normal;
  DMA_InitStruct.DMA_SrcBurstLen = DMA_BurstLen_1;
  DMA_InitStruct.DMA_DstBurstLen = DMA_BurstLen_1;
  DMA_InitStruct.DMA_DstAddrDir = DMA_AddrDir_Inc;
  DMA_InitStruct.DMA_SrcAddrDir = DMA_AddrDir_Hold;	
  DMA_InitStruct.DMA_SrcTransDataSize = DMA_TransDataSize_8Bits;
  DMA_InitStruct.DMA_DstTransDataSize = DMA_TransDataSize_8Bits;
  DMA_InitStruct.DMA_TransBlkSize = 85;	
  DMA_InitStruct.DMA_DstHandShakeSel = DMA_HandShakeSel_SoftWare;
  DMA_InitStruct.DMA_SrcHandShakeSel = DMA_HandShakeSel_HardWare;
  DMA_InitStruct.DMA_Priority = DMA_Priority_High;	
  DMA_InitStruct.DMA_SrcPerReq = 0x1;
  DMA_InitStruct.DMA_SrcPerHandShakeInput = DMA_PerHandShakeInput_HS1;
  DMA_Init(DMA_ChannelSel_CH1, &DMA_InitStruct);
  
  DMA_ITConfig(DMA_ChannelSel_CH1, DMA_ITFlag_BlkTc, ENABLE);
  
  DMA_Cmd(DMA_ChannelSel_CH1, DISABLE);
}

void uf_DMA_UartTxReqInit(void)
{
  DMA_InitTypeDef DMA_InitStruct;
  
  CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_DMA, ENABLE);
  
  DMA_StructInit(&DMA_InitStruct);
  DMA_InitStruct.DMA_SrcAddr = (uint32_t)Uart1_RxBuf;
  DMA_InitStruct.DMA_DstAddr = (uint32_t)&(UART_1->TXR);
  DMA_InitStruct.DMA_TransDir = DMA_TransDir_M2P;
  DMA_InitStruct.DMA_Mode = DMA_Mode_Normal;
  DMA_InitStruct.DMA_SrcBurstLen = DMA_BurstLen_1;
  DMA_InitStruct.DMA_DstBurstLen = DMA_BurstLen_1;
  DMA_InitStruct.DMA_SrcAddrDir = DMA_AddrDir_Inc;
  DMA_InitStruct.DMA_DstAddrDir = DMA_AddrDir_Hold;	
  DMA_InitStruct.DMA_SrcTransDataSize = DMA_TransDataSize_8Bits;
  DMA_InitStruct.DMA_DstTransDataSize = DMA_TransDataSize_8Bits;
  DMA_InitStruct.DMA_TransBlkSize = 1024;	
  DMA_InitStruct.DMA_SrcHandShakeSel = DMA_HandShakeSel_SoftWare;
  DMA_InitStruct.DMA_DstHandShakeSel = DMA_HandShakeSel_HardWare;
  DMA_InitStruct.DMA_Priority = DMA_Priority_Low;	
  DMA_InitStruct.DMA_DstPerReq = 0x0;
  DMA_InitStruct.DMA_DstPerHandShakeInput = DMA_PerHandShakeInput_HS0;
  DMA_Init(DMA_ChannelSel_CH0, &DMA_InitStruct);
  
  DMA_ITConfig(DMA_ChannelSel_CH0, DMA_ITFlag_BlkTc, ENABLE);
  
  DMA_Cmd(DMA_ChannelSel_CH0, DISABLE);	
}

void UART1_DeInit(void)
{
	__disable_irq();
	NVIC_DisableIRQ(UART1_IRQn);
	NVIC_ClearPendingIRQ(UART1_IRQn);
	__enable_irq();
	UART_DeInit(UART_1);

}

//*******************UART1***************
//connect with radar 
void UART1_Init(uint32_t BaudRate,uint8_t dmaEnable)
{
//gpio	configure
	PORT_InitTypeDef PORT_InitStruct;
	CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);
	
	PORT_InitStruct.PORT_Pin = PORT_Pin_6; // RX = P1.6
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
	PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal; 
	PORT_Init(PORT_1, &PORT_InitStruct);
	
	PORT_InitStruct.PORT_Pin = PORT_Pin_7; // TX = P1.7
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
	PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
	PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal; 
	PORT_Init(PORT_1, &PORT_InitStruct);
	
	PORT_PinAFConfig(PORT_1, PORT_PinSource6, PORT_AF_1); // Port alternative function
	PORT_PinAFConfig(PORT_1, PORT_PinSource7, PORT_AF_1);		
//------------uart configure--------------------

	UART_InitTypeDef UART_InitStruct;
	CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART1, ENABLE);
	UART_DeInit(UART_1);
	UART_InitStruct.UART_BaudRate = BaudRate;
	UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
	UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
	UART_InitStruct.UART_Parity = UART_Parity_None;
	UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;	  
	UART_Init(UART_1, &UART_InitStruct);
	
	memset(Uart1_RxBuf, 0, sizeof(Uart1_RxBuf));
//--------------------------------------------------------
	if(dmaEnable)
	{
		UART_ITConfig(UART_1, (UART_IT_TXREmptyIE | UART_IT_ReceiveData_RXFIFOTimeOutIE), DISABLE);
		UART_FIFOModeConfig(UART_1, UART_TXFIFOThreshValue_0_32FULL, UART_RXFIFOThreshValue_1_32FULL, DISABLE);   
		
		//ʹ��dma ��ȡuart ����
		uf_DMA_UartRxReqInit();
		
		__disable_irq();
		NVIC_DisableIRQ(UART1_IRQn);
		NVIC_ClearPendingIRQ(UART1_IRQn);
		NVIC_SetPriority(UART1_IRQn, 0x00);   
		//	  NVIC_EnableIRQ(UART1_IRQn);
		
		NVIC_DisableIRQ(DMAC_IRQn);
		NVIC_ClearPendingIRQ(DMAC_IRQn);
		NVIC_SetPriority(DMAC_IRQn, 0x01);	  
		NVIC_EnableIRQ(DMAC_IRQn);
		__enable_irq();

		DMA_Cmd(DMA_ChannelSel_CH1, ENABLE);
	}
	else
	{
		// Config FIFO
		UART_FIFOModeConfig(UART_1, UART_TXFIFOThreshValue_2_32FULL, UART_RXFIFOThreshValue_30_32FULL, ENABLE);
		UART_PTXREModeConfig(UART_1, ENABLE);
		
		UART_ITConfig(UART_1, UART_IT_TXREmptyIE , DISABLE);   
		UART_ITConfig(UART_1, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE); 
		
		//nvic init 
		__disable_irq(); 
		NVIC_DisableIRQ(UART1_IRQn);
		NVIC_ClearPendingIRQ(UART1_IRQn);
		NVIC_SetPriority(UART1_IRQn, 0x0);
		//NVIC_DisableIRQ(UART1_IRQn);
		
		NVIC_EnableIRQ(UART1_IRQn);
		__enable_irq();
	}

}


void DMAC_IRQHandler(void)
{

  if (DMA_GetITStatus(DMA_ChannelSel_CH1, DMA_ITFlag_BlkTc) == SET)
  {
    DMA_ClearITFlag(DMA_ChannelSel_CH1, DMA_ITFlag_BlkTc);
	blue_led_Toggle;
	printf("dma irq----\r\n");
    
	DMA_Cmd(DMA_ChannelSel_CH1, ENABLE);
  }
}


void UART1_IRQ_SET(u8 uEnSta)
{
	if(uEnSta > 0)
	{
		__disable_irq();
		NVIC_DisableIRQ(UART1_IRQn);
		NVIC_ClearPendingIRQ(UART1_IRQn);
		NVIC_SetPriority(UART1_IRQn, 0x0);
		NVIC_EnableIRQ(UART1_IRQn);
		__enable_irq();
	}
	else
	{
		__disable_irq();
		NVIC_DisableIRQ(UART1_IRQn);
		__enable_irq();
	}
}
void UART1_IRQHandler(void)
{
	volatile uint8_t u8Tmp =0;
	volatile int pos =0;
	
	if(UART_GetITStatus(UART_1, UART_IntID_TXREmpty) != RESET)
	{
		
	}

	if( (UART_GetITStatus(UART_1, UART_IntID_ReceiveData) == SET) \
		|| (UART_GetITStatus(UART_1, UART_IntID_RXFIFOTimeOut) == SET) )
	{
		while (UART_GetLineStatus(UART_1, UART_LSFLAG_DataReady) != RESET )
		{
			u8Tmp = UART_ReceiveData(UART_1);			
			pos = RxWritePos[UART1_POS] % UART1_RX_BUF_SIZE;
			Uart1_RxBuf[pos] = u8Tmp;
			RxWritePos[UART1_POS]++;
			//printf("D: %02X \r\n",u8Tmp);
		}
	}
}


//��������
bool uart1_com_tx_data(u8 *bData, u8 bLenth)
{
	u8 j=0;
	for(j=0;j<bLenth;j++)							//ѭ����������
	{
		//uint32_t tickstart = GetSysTick();
		while (UART_GetLineStatus(UART_1, UART_LSFLAG_TXREmpty_TXFIFOFull) == SET) // && (GetSysTick() - tickstart) < uart1_tx_TimeOut)
		{
		  
		}
		UART_SendData(UART_1, (u8)bData[j]);   
	}
	
	return 0;
}

bool uart1_com_Receive(uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
	int i =0;
	uint16_t sTmpSize =0;
	uint8_t  *pdata8bits;
	uint32_t tickstart = 0U;

    if ((pData == NULL) || (Size == 0U))
    {
      return  FALSE;
    }

	pdata8bits = pData;
    /* Check the remain data to be received */
    for(i=0; i< Timeout; i++)
    {
	    if(UART_GetLineStatus(UART_1, UART_LNSR_DATARDY) == SET)
	    {
	    	*pdata8bits = UART_ReceiveData(UART_1);
	        pdata8bits++;
	        sTmpSize++;
	    }
	    
	    if( sTmpSize == Size)
	    {
	    	return TRUE;
	    }
	    
	    Delay_ms(1);
	}
    return FALSE;
}


/******************
	 ������Ŀ�״����ݻ�ȡ�״�ľ��������ֵ

*******************/
int uart1_rx_mengmu_process(unsigned short *pEnerge,unsigned short *distance)
{
	char type = 0;
	unsigned long long pos_start;
	unsigned long long pos_end;
	unsigned long long i;	
	unsigned long long j;		
	unsigned short energeVal;
	unsigned short distanceVal;
	unsigned short shutter;
	unsigned char xorVal = 0;
	pos_start = RxReadPos[UART1_POS];
	pos_end   = RxWritePos[UART1_POS];

	if(pos_start < pos_end)
	{
		if ( pos_end - pos_start <= UART1_RX_BUF_SIZE)
		{

			if ( pos_end   - pos_start >= 11)
			{
				for(i = pos_start; i < pos_end; i++)
				{
					if (  pos_end - i >= 11 && Uart1_RxBuf[i%UART1_RX_BUF_SIZE] == 0x89 && Uart1_RxBuf[(i+1)%UART1_RX_BUF_SIZE] == 0x81)
					{
						xorVal = 0;
						for (j = i;j < i + 10;j ++ )
						{
							xorVal = xorVal ^ Uart1_RxBuf[j%UART1_RX_BUF_SIZE];
						}
						if (xorVal != Uart1_RxBuf[j%UART1_RX_BUF_SIZE])
						{
							continue;
						}
						type = Uart1_RxBuf[(i+9)%UART1_RX_BUF_SIZE];
						distanceVal = Uart1_RxBuf[(i+2)%UART1_RX_BUF_SIZE] | ( Uart1_RxBuf[(i+3)%UART1_RX_BUF_SIZE]<<8 );
						energeVal = Uart1_RxBuf[(i+4)%UART1_RX_BUF_SIZE] | ( Uart1_RxBuf[(i+5)%UART1_RX_BUF_SIZE]<<8 );
						shutter = Uart1_RxBuf[(i+7)%UART1_RX_BUF_SIZE] | ( Uart1_RxBuf[(i+8)%UART1_RX_BUF_SIZE]<<8 );
						if(type == 0 || type == 1 )
						{	
							if (shutter < 1000 && energeVal < 1000)
							{
								RxReadPos[UART1_POS] = i + 11;	
								i += 10;
							}
							else
							{
								if (shutter == 1000 && energeVal < 100)
								{
									distanceVal = 12000;
								}
								if (distanceVal > 4500) // �����޸������״ﴦ����������
								{
									distanceVal = 4500;
								}
								*pEnerge = energeVal;
								*distance = distanceVal;
								RxReadPos[UART1_POS] = i + 11;
								return 0;
							}
						}
						else if (type == 2 || type == 3 || type == 6)
						{
							if (shutter == 20 )
							{
								distanceVal = 601;											
								*pEnerge = energeVal;
								*distance = distanceVal;
								RxReadPos[UART1_POS] = i + 11;
								return 0;										
							}
							else
							{
								RxReadPos[UART1_POS] = i + 11;	
								i += 10;
							}

						}
						else
						{
							RxReadPos[UART1_POS] = i + 11;	
							i += 10;
						}

					}
				}
			}
		}
		else
		{
				RxReadPos[UART1_POS] = RxWritePos[UART1_POS];				
		}

	}
	else
	{
			RxReadPos[UART1_POS] = RxWritePos[UART1_POS];
	}
	return -1;
}


int pack_luna_data(int dev_type, int pack_type, int total_packages, int seq, unsigned short usEnerge, unsigned short usDistance, unsigned char* packed_data, int *packed_len)
{
#if 1
	static unsigned short s_seq = 0;
	long long  tail_pos = 0;
	int data_len = sizeof(unsigned short ) * 2;

	packed_data[0] = 0x11;
	packed_data[1] = 0x22;
	packed_data[2] = 0x33;
	packed_data[3] = 0x55;
	s_seq ++;
	*(unsigned short *)(packed_data + 4) = total_packages;
	*(unsigned short *)(packed_data + 6) = s_seq;

	*(unsigned int *)(packed_data + 8) = 4;
	packed_data[12] = pack_type;
	packed_data[13] = dev_type;

	packed_data[14] = 0;//reserved
	packed_data[15] = 0;//reserved


	memcpy((void*)(packed_data + 20), (void*)&usEnerge,  sizeof(unsigned short) );
	memcpy((void*)(packed_data + 22), (void*)&usDistance,  sizeof(unsigned short) );
	*(unsigned int *)(packed_data + 16) = crc32(0xffffffff, (void*)(packed_data + 20), data_len);		
	//tail_pos = 20 + ((data_len>>2)<<2) + (((data_len&0x3)>0)?4:0);
	tail_pos = 20;
	tail_pos += ((data_len>>2)<<2);
	tail_pos += ((data_len&0x3)>0)?4:0;
	packed_data[tail_pos + 0] = 0x99;
	packed_data[tail_pos + 1] = 0x88;
	packed_data[tail_pos + 2] = 0x77;
	packed_data[tail_pos + 3] = 0x55;

	*packed_len = tail_pos + 4;
#endif		
}


//************* UART2 *************
#define 	______UART2_Define

void UART2_DeInit(void)
{
	//nvic init 
	__disable_irq();
	NVIC_DisableIRQ(UART2_IRQn);
	NVIC_ClearPendingIRQ(UART2_IRQn);
	__enable_irq();
	UART_DeInit(UART_2);
}


//connect with FH module
void UART2_Init(void)
{
//------------------------------------------
	PORT_InitTypeDef PORT_InitStruct;
	CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);

	PORT_InitStruct.PORT_Pin = PORT_Pin_0; // RX = P2.0
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
	PORT_Init(PORT_2, &PORT_InitStruct);

	PORT_InitStruct.PORT_Pin = PORT_Pin_1; // TX = P2.1
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
	PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
	PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal; 
	PORT_Init(PORT_2, &PORT_InitStruct);

	PORT_PinAFConfig(PORT_2, PORT_PinSource0, PORT_AF_1); // Port alternative function
	PORT_PinAFConfig(PORT_2, PORT_PinSource1, PORT_AF_1);	  
//--------------------------------------------------------------------
	CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART2, ENABLE);
	UART_DeInit(UART_2);
	
	UART_InitTypeDef UART_InitStruct;
	UART_InitStruct.UART_BaudRate = 9600;
	UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
	UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
	UART_InitStruct.UART_Parity = UART_Parity_None;
	UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;	
	UART_Init(UART_2, &UART_InitStruct);

	// Config FIFO
	UART_FIFOModeConfig(UART_2, UART_TXFIFOThreshValue_16_32FULL, UART_RXFIFOThreshValue_30_32FULL, ENABLE);
	UART_PTXREModeConfig(UART_2, ENABLE);
	memset(uart2_RxBuf, 0, sizeof(uart2_RxBuf));
	UART_ITConfig(UART_2, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE); 
	UART_ITConfig(UART_2, (UART_IT_TXREmptyIE ), DISABLE);		

	//nvic init 
	__disable_irq();
	NVIC_DisableIRQ(UART2_IRQn);
	NVIC_ClearPendingIRQ(UART2_IRQn);
	NVIC_SetPriority(UART2_IRQn, 0x1);
	NVIC_EnableIRQ(UART2_IRQn);
	__enable_irq();

}


void UART2_IRQHandler(void)
{
	uint8_t u8Tmp =0;
	int pos=0;

	if(UART_GetITStatus(UART_2, UART_IntID_TXREmpty) != RESET)
	{
		
	}

	if( (UART_GetITStatus(UART_2, UART_IntID_ReceiveData) == SET) \
		|| (UART_GetITStatus(UART_2, UART_IntID_RXFIFOTimeOut) == SET) )
	{
		while (UART_GetLineStatus(UART_2, UART_LSFLAG_DataReady) != RESET )
		{
			u8Tmp = UART_ReceiveData(UART_2);
			pos = RxWritePos[UART2_POS] % UART2_RX_BUF_SIZE;
			uart2_RxBuf[pos] = u8Tmp;
			RxWritePos[UART2_POS]++;
			//printf("--uart2  recv data: %X ,pos:%d\r\n",u8Tmp,pos);
		}
	}
}


//FH module info process
int uart2_rx_process(int *type, unsigned char **buffer, int *len)
{
	unsigned long long pos_start;
	unsigned long long pos_end;

	pos_start = RxReadPos[UART2_POS];
	pos_end   = RxWritePos[UART2_POS];

	if(pos_start < pos_end)
	{
		#if 1
			if(pos_end - pos_start < UART2_RX_BUF_SIZE)
			{
				long long i;
				long long  head_pos = -1;
				long long  tail_pos = -1;

				int dev_type;
				int pack_type;
				int total_packages; 
				int seq; 
				unsigned char data; 
				int data_len; 
				unsigned char unpacked_data;
				int unpacked_len;
				int ret = -1;				
				uart2_find_head_and_tail(uart2_RxBuf, UART2_RX_BUF_SIZE, pos_start, pos_end, &head_pos, &tail_pos);
				if((-1 != tail_pos)&&(-1!= head_pos))
				{
					if (tail_pos   - head_pos > 4)
					{
						for(i = head_pos; i < tail_pos; i++)
						{
							uart_proc_buf[i - head_pos] = uart2_RxBuf[i%UART2_RX_BUF_SIZE];
						}

						RxReadPos[UART2_POS] =  (tail_pos + 4);
						
						ret = uart2_unpack_data(&dev_type, &pack_type, &total_packages, &seq, uart_proc_buf, tail_pos - head_pos, uart_proced_buf, &unpacked_len);

						if(ret >= 0)
						{
							*buffer = uart_proced_buf;
							*len = unpacked_len;
							*type = dev_type;
						}
						else
						{
							*type = -1;
							*len = 0;
							*buffer = NULL;
						}
					}
					else
					{
						*type = -1;
						*len = 0;
						*buffer = NULL;
						RxReadPos[UART2_POS] =  (tail_pos + 4);
					}
				}
				else if (head_pos == -1 && tail_pos != -1 )
				{
					*type = -1;
					*len = 0;
					*buffer = NULL;
					RxReadPos[UART2_POS] =  (tail_pos + 4);
					
				}					
				else if (head_pos != -1 && tail_pos == -1 )
				{
					*type = -1;
					*len = 0;
					*buffer = NULL;
				}
				else  
				{
					*type = -1;
					*len = 0;
					*buffer = NULL;					
				}
			}
			else
			{
				/* cant catch */
				*type = -1;
				*len = 0;
				*buffer = NULL;							
				RxReadPos[UART2_POS] = RxWritePos[UART2_POS];
			}
			#endif
	}
	return 0;
}

int uart2_find_head_and_tail(unsigned char *buf, int buf_width, long long pos_start, long long pos_end, long long  *head_pos, long long  *tail_pos)
{
	long long i;
	
	if(tail_pos == NULL)
	{
		return -1;
	}
	
	if(tail_pos == NULL)
	{
		return -1;
	}

	*head_pos = -1;
	*tail_pos = -1;
	
	/* head flag 0x11223355 or tail flag 0x99887755 is at least 4*/
	if(pos_end - pos_start >= 4)
	{
		for(i = pos_start; i < pos_end ; i++)
		{
			unsigned char tmp0;
			unsigned char tmp1;
			unsigned char tmp2;
			unsigned char tmp3;
			int pos;
			
			pos = (i+0)%buf_width;
			tmp0 = buf[pos];
			pos = (i+1)%buf_width;
			tmp1 = buf[pos];
			pos = (i+2)%buf_width;
			tmp2 = buf[pos];
			pos = (i+3)%buf_width;
			tmp3 = buf[pos];
			
			if(
				(0x11 ==tmp0)
			&&(0x22 ==tmp1)
			&&(0x33 ==tmp2)
			&&(0x55 ==tmp3)
				)
			{
				if(-1 == *head_pos)
				{
					*head_pos = i;
				}
			}
			
			if(
				(0x99 ==tmp0)
			&&(0x88 ==tmp1)
			&&(0x77 ==tmp2)
			&&(0x55 ==tmp3)
				)
			{
				if(-1 != *head_pos)
				{
					*tail_pos = i;
					return 1;
				}
			}
		}
	}
}

int uart2_unpack_data(int *dev_type, int *pack_type, int *total_packages, int *seq, unsigned char* data, int data_len, unsigned char* unpacked_data, unsigned int *unpacked_len)
{
	long long  tail_pos = 0;
	unsigned crc32_read = 0;
	unsigned crc32_cal = 0;

	*total_packages = *(unsigned short *)(data + 4);
	*seq = *(unsigned short *)(data + 6);

	*unpacked_len = *(unsigned int *)(data + 8);
	*pack_type = data[12];
	*dev_type = data[13];
  
	crc32_read = *(unsigned int *)(data + 16);

	/* header len 20 bytes */
	if (((*unpacked_len +3 )&0xFFFC) != data_len -20 )
	{
		printf("*unpacked_len = %d\r\n", *unpacked_len);
		*unpacked_len = 0;
		return -1;
	}

	if(*unpacked_len > 1000)
	{
		*unpacked_len = 0;
		return -1;
	}

	memcpy((void*)(unpacked_data), (void*)(data + 20), (unsigned int)*unpacked_len);
	return 0;
}

int pack_print_data(int dev_type, int pack_type, int total_packages, int seq, unsigned char* packed_data, int data_len, int *packed_len)
{

#if 1
	static unsigned short s_seq = 0;
	long long  tail_pos = 0;
	packed_data[0] = 0x11;
	packed_data[1] = 0x22;
	packed_data[2] = 0x33;
	packed_data[3] = 0x55;
	s_seq ++;
	*(unsigned short *)(packed_data + 4) = total_packages;
	*(unsigned short *)(packed_data + 6) = s_seq;

	*(unsigned int *)(packed_data + 8) = data_len;
	packed_data[12] = pack_type;
	packed_data[13] = dev_type;
  
	packed_data[14] = 0;//reserved
	packed_data[15] = 0;//reserved

	*(unsigned int *)(packed_data + 16) = crc32(0xffffffff, packed_data+20, data_len);
	
	tail_pos = 20;
	tail_pos += ((data_len>>2)<<2);
	tail_pos += ((data_len&0x3)>0)?4:0;
	packed_data[tail_pos + 0] = 0x99;
	packed_data[tail_pos + 1] = 0x88;
	packed_data[tail_pos + 2] = 0x77;
	packed_data[tail_pos + 3] = 0x55;
	
	*packed_len = tail_pos + 4;
#endif		
}


int pack_data(int dev_type, int pack_type, int total_packages, int seq, unsigned char* data, int data_len, unsigned char* packed_data, int *packed_len)
{
	
#if 1
	static unsigned short s_seq = 0;
	long long  tail_pos = 0;
	packed_data[0] = 0x11;
	packed_data[1] = 0x22;
	packed_data[2] = 0x33;
	packed_data[3] = 0x55;
	s_seq ++;
	*(unsigned short *)(packed_data + 4) = total_packages;
	*(unsigned short *)(packed_data + 6) = s_seq;

	*(unsigned int *)(packed_data + 8) = data_len;
	packed_data[12] = pack_type;
	packed_data[13] = dev_type;

	packed_data[14] = 0;//reserved
	packed_data[15] = 0;//reserved

	*(unsigned int *)(packed_data + 16) = crc32(0xffffffff, data, data_len);
	memcpy((void*)(packed_data + 20), (void*)data, (unsigned int)data_len);

	//tail_pos = 20 + ((data_len>>2)<<2) + (((data_len&0x3)>0)?4:0);
	tail_pos = 20;
	tail_pos += ((data_len>>2)<<2);
	tail_pos += ((data_len&0x3)>0)?4:0;
	packed_data[tail_pos + 0] = 0x99;
	packed_data[tail_pos + 1] = 0x88;
	packed_data[tail_pos + 2] = 0x77;
	packed_data[tail_pos + 3] = 0x55;

	*packed_len = tail_pos + 4;
#endif		
}



//��������
bool uart2_com_tx_data(int bLenth, u8 *bData)
{
	int j;
	for(j=0;j<bLenth;j++)							//ѭ����������
	{
		uint32_t tickstart = GetSysTick();
		while (UART_GetLineStatus(UART_2, UART_LSFLAG_TXEmpty) != SET && (GetSysTick() - tickstart) < uart2_tx_TimeOut)
		{
		  
		}
		UART_SendData(UART_2, (u8)bData[j]);   
	} 

}


#define _____UART4_Define

//****************************************  UART4  **************

//debug uart4 

void UART4_DeInit(void)
{
	UART_DeInit(UART_4);
}

void UART4_Init(void)
{
	UART_InitTypeDef UART_InitStruct;
	
	PORT_InitTypeDef PORT_InitStruct;
	
	CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);
	PORT_InitStruct.PORT_Pin = PORT_Pin_0; // RX = P3.0
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;	
	PORT_Init(PORT_3, &PORT_InitStruct);	
	
	PORT_InitStruct.PORT_Pin = PORT_Pin_1; // TX = P3.1
	PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
	PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
	PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
	PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
	PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;	
	PORT_Init(PORT_3, &PORT_InitStruct);
	
	//PORT_WriteBit(PORT_3, PORT_Pin_0, Bit_SET);
	//PORT_WriteBit(PORT_3, PORT_Pin_1, Bit_SET);

	PORT_PinAFConfig(PORT_3, PORT_PinSource0, PORT_AF_1); // Port alternative function
	PORT_PinAFConfig(PORT_3, PORT_PinSource1, PORT_AF_1);		
	
	CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART4, ENABLE);
	UART_DeInit(UART_4);
	
	UART_ITConfig(UART_4, UART_IT_ReceiveData_RXFIFOTimeOutIE, DISABLE); 
	UART_ITConfig(UART_4, UART_IT_TXREmptyIE, DISABLE);
	
	UART_StructInit(&UART_InitStruct);
	UART_InitStruct.UART_BaudRate = 115200;
	UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
	UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
	UART_InitStruct.UART_Parity = UART_Parity_None;
	UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;	
	UART_Init(UART_4, &UART_InitStruct);
	
	__disable_irq();
	NVIC_DisableIRQ(UART4_IRQn);
	NVIC_ClearPendingIRQ(UART4_IRQn);
	NVIC_SetPriority(UART4_IRQn, 0x2);
	NVIC_DisableIRQ(UART4_IRQn);
	__enable_irq();
}



void UART4_IRQHandler(void)
{
	if (UART_GetITStatus(UART_4, UART_IntID_TXREmpty) == SET)
	{
	}

	if (UART_GetITStatus(UART_4, UART_IntID_ReceiveData) == SET)
	{	
		UART_SendData(UART_4, UART_ReceiveData(UART_4));
		while (UART_GetLineStatus(UART_4, UART_LSFLAG_TXEmpty) != SET);
	}
}


#if 0

static char s_buf_print[256];

int log_printf(const char* format, ...)
{

	int packed_len;
	int len  = 0;
	memset(s_buf_print,0,256);
	//snprintf(s_buf_print,15,"%02d %02d %02d:%02d:%02d",sDate.Month+1,sDate.Date,sTime.Hours,sTime.Minutes,sTime.Seconds);

	va_list vlist;
	va_start(vlist, format);
	vsnprintf(s_buf_print+14, 120-14, format, vlist);
	va_end(vlist);
	
	packed_len = strlen(s_buf_print);
	uart4_com_tx_data(packed_len,s_buf_print);
	return 0;
}

#if (DBG_UART==TRUE)

/*
    �����ʽ

    ��a                ��������ʮ���������ֺ�p-���������ã�����
    %A����������������ʮ���������ֺ�p-�Ƿ����ã�����
    %c��������һ���ַ���
    %d���������з���ʮ����������
    %e����������������e-������
    %E������������������-������
    %f����������������ʮ���Ƽ���������
    %g��������������ֵ��ͬ�Զ�ѡ��f��e��
    %G��������������ֵ��ͬ�Զ�ѡ��f��e.
    %i        �з���ʮ���������룥d��ͬ��
    %o���������޷��Ű˽�������
    %p��������ָ�롡������
    %s���������ַ���
    %u���������޷���ʮ��������
    %x��������ʹ��ʮ���������֣�f���޷���ʮ������������
    %X��������ʹ��ʮ���������֣�f���޷���ʮ����������
    %%����������ӡһ���ٷֺ�

*/
void Sys_Printf(char *fmt, ...)
{
    char buff[100]; 
    u8 i = 0;
    va_list arg_ptr;

    va_start(arg_ptr, fmt);
    vsnprintf(buff, 100+1, fmt, arg_ptr);
    if(buff == SYS_NULL)
        return;
    //�Ӵ��ڷ�������
    for(i = 0 ;(i<100)&&(buff[i] != '\0');i++)
    {
    	uart4_tx_buffer_in(buff[i]);
    }	
}
#else
void Sys_Printf(char *fmt, ...)
{
        return;
}
#endif


#endif


#endif
