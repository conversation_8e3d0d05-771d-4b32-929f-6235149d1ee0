.\objects\uart.o: ..\User\uart.c
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_port.h
.\objects\uart.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\uart.o: ..\xcm32lxx_lib\core\core_cm0plus.h
.\objects\uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\uart.o: ..\xcm32lxx_lib\core\core_cmInstr.h
.\objects\uart.o: ..\xcm32lxx_lib\core\core_cmFunc.h
.\objects\uart.o: ..\xcm32lxx_lib\core\system_XCM32L.h
.\objects\uart.o: ..\xcm32lxx_lib\core\XCM32L.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_conf.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_cmu.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_uart.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_timer.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pca.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rtc.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_wdt.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_flash.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_pmu.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_rmu.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_systick.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_misc.h
.\objects\uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\uart.o: ..\User\user_main.h
.\objects\uart.o: ..\User\uart.h
.\objects\uart.o: ..\xcm32lxx_lib\inc\XCM32Lxx_dma.h
.\objects\uart.o: ..\User\main.h
