/**
 * @file    rtc_optimized.h
 * @brief   Optimized RTC module header file
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _RTC_OPTIMIZED_H_
#define _RTC_OPTIMIZED_H_

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "system_config.h"

/* Exported constants --------------------------------------------------------*/
#define RTC_EPOCH_YEAR              2000    /* RTC epoch year */
#define RTC_SECONDS_PER_MINUTE      60
#define RTC_SECONDS_PER_HOUR        3600
#define RTC_SECONDS_PER_DAY         86400
#define RTC_DAYS_PER_YEAR           365
#define RTC_DAYS_PER_LEAP_YEAR      366

/* Month days table */
#define RTC_DAYS_IN_MONTH_NORMAL    {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}
#define RTC_DAYS_IN_MONTH_LEAP      {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}

/* Exported types ------------------------------------------------------------*/
/**
 * @brief RTC time structure
 */
typedef struct {
    uint16_t year;      /* Year (2000-2099) */
    uint8_t  month;     /* Month (1-12) */
    uint8_t  day;       /* Day (1-31) */
    uint8_t  hour;      /* Hour (0-23) */
    uint8_t  minute;    /* Minute (0-59) */
    uint8_t  second;    /* Second (0-59) */
    uint8_t  weekday;   /* Weekday (0-6, 0=Sunday) */
} rtc_time_t;

/**
 * @brief RTC alarm structure
 */
typedef struct {
    uint8_t  hour;      /* Alarm hour (0-23) */
    uint8_t  minute;    /* Alarm minute (0-59) */
    uint8_t  second;    /* Alarm second (0-59) */
    uint8_t  enabled;   /* Alarm enable flag */
} rtc_alarm_t;

/* Exported function prototypes ----------------------------------------------*/

/* RTC initialization and configuration */
/**
 * @brief  Initialize RTC module
 * @param  None
 * @retval None
 */
void RTC_Run_Init(void);

/**
 * @brief  Deinitialize RTC module
 * @param  None
 * @retval None
 */
void RTC_Run_DeInit(void);

/* Time setting functions */
/**
 * @brief  Set RTC year
 * @param  year: Year value in BCD format
 * @retval None
 */
void RTC_SetYear(uint8_t year);

/**
 * @brief  Set RTC month
 * @param  month: Month value in BCD format (1-12)
 * @retval None
 */
void RTC_SetMonth(uint8_t month);

/**
 * @brief  Set RTC day
 * @param  day: Day value in BCD format (1-31)
 * @retval None
 */
void RTC_SetDay(uint8_t day);

/**
 * @brief  Set RTC hour
 * @param  hour: Hour value in BCD format (0-23)
 * @retval None
 */
void RTC_SetHour(uint8_t hour);

/**
 * @brief  Set RTC minute
 * @param  minute: Minute value in BCD format (0-59)
 * @retval None
 */
void RTC_SetMinute(uint8_t minute);

/**
 * @brief  Set RTC second
 * @param  second: Second value in BCD format (0-59)
 * @retval None
 */
void RTC_SetSecond(uint8_t second);

/**
 * @brief  Set RTC weekday
 * @param  weekday: Weekday value (0-6, 0=Sunday)
 * @retval None
 */
void RTC_SetWeekday(uint8_t weekday);

/* Time getting functions */
/**
 * @brief  Get RTC year
 * @param  None
 * @retval Year value in BCD format
 */
uint8_t RTC_GetYear(void);

/**
 * @brief  Get RTC month
 * @param  None
 * @retval Month value in BCD format (1-12)
 */
uint8_t RTC_GetMonth(void);

/**
 * @brief  Get RTC day
 * @param  None
 * @retval Day value in BCD format (1-31)
 */
uint8_t RTC_GetDay(void);

/**
 * @brief  Get RTC hour
 * @param  None
 * @retval Hour value in BCD format (0-23)
 */
uint8_t RTC_GetHour(void);

/**
 * @brief  Get RTC minute
 * @param  None
 * @retval Minute value in BCD format (0-59)
 */
uint8_t RTC_GetMinute(void);

/**
 * @brief  Get RTC second
 * @param  None
 * @retval Second value in BCD format (0-59)
 */
uint8_t RTC_GetSecond(void);

/**
 * @brief  Get RTC weekday
 * @param  None
 * @retval Weekday value (0-6, 0=Sunday)
 */
uint8_t RTC_GetWeekday(void);

/* Time conversion functions */
/**
 * @brief  Get RTC time in seconds since epoch
 * @param  None
 * @retval Time in seconds since epoch (2000-01-01 00:00:00)
 */
long long GetRtcSecond(void);

/**
 * @brief  Set RTC time from seconds since epoch
 * @param  seconds: Time in seconds since epoch
 * @retval None
 */
void SetRtcFromSeconds(long long seconds);

/**
 * @brief  Get current RTC time structure
 * @param  time: Pointer to time structure
 * @retval None
 */
void RTC_GetTime(rtc_time_t *time);

/**
 * @brief  Set RTC time from structure
 * @param  time: Pointer to time structure
 * @retval None
 */
void RTC_SetTime(const rtc_time_t *time);

/* Utility functions */
/**
 * @brief  Convert HEX to BCD
 * @param  hex_val: HEX value to convert
 * @retval BCD value
 */
uint8_t t_HEX2BCD(uint8_t hex_val);

/**
 * @brief  Convert BCD to HEX
 * @param  bcd_val: BCD value to convert
 * @retval HEX value
 */
uint8_t t_BCD2HEX(uint8_t bcd_val);

/**
 * @brief  Check if year is leap year
 * @param  year: Year to check
 * @retval 1 if leap year, 0 if not
 */
uint8_t RTC_IsLeapYear(uint16_t year);

/**
 * @brief  Get days in month
 * @param  year: Year
 * @param  month: Month (1-12)
 * @retval Number of days in month
 */
uint8_t RTC_GetDaysInMonth(uint16_t year, uint8_t month);

/**
 * @brief  Calculate weekday from date
 * @param  year: Year
 * @param  month: Month (1-12)
 * @param  day: Day (1-31)
 * @retval Weekday (0-6, 0=Sunday)
 */
uint8_t RTC_CalculateWeekday(uint16_t year, uint8_t month, uint8_t day);

/* Alarm functions */
/**
 * @brief  Set RTC alarm
 * @param  alarm: Pointer to alarm structure
 * @retval None
 */
void RTC_SetAlarm(const rtc_alarm_t *alarm);

/**
 * @brief  Get RTC alarm
 * @param  alarm: Pointer to alarm structure
 * @retval None
 */
void RTC_GetAlarm(rtc_alarm_t *alarm);

/**
 * @brief  Enable RTC alarm
 * @param  None
 * @retval None
 */
void RTC_EnableAlarm(void);

/**
 * @brief  Disable RTC alarm
 * @param  None
 * @retval None
 */
void RTC_DisableAlarm(void);

/**
 * @brief  Check if RTC alarm is triggered
 * @param  None
 * @retval 1 if alarm triggered, 0 if not
 */
uint8_t RTC_IsAlarmTriggered(void);

/**
 * @brief  Clear RTC alarm flag
 * @param  None
 * @retval None
 */
void RTC_ClearAlarmFlag(void);

#endif /* _RTC_OPTIMIZED_H_ */
