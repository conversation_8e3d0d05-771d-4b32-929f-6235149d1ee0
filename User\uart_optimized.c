/**
 * @file    uart_optimized.c
 * @brief   Optimized UART communication module implementation
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include "XCM32Lxx_conf.h"
#include "uart_optimized.h"
#include "system_config.h"
#include "main_optimized.h"
#include "XCM32Lxx_dma.h"
#include "crc32.h"
#include <stdarg.h>
#include <stdio.h>

/* Private constants ---------------------------------------------------------*/
#define UART_TX_TIMEOUT_COUNT       1000

/* Private variables ---------------------------------------------------------*/
/* UART buffer variables */
volatile unsigned long long RxReadPos[UART_NUM] = {0};
volatile unsigned long long RxWritePos[UART_NUM] = {0};
volatile unsigned char uart2_RxBuf[UART2_RX_BUF_SIZE] = {0};
volatile unsigned char Uart1_RxBuf[UART1_RX_BUF_SIZE] = {0};
unsigned char uart_proc_buf[UART_BUF_LEN] = {0};
unsigned char uart_proced_buf[UART_BUF_LEN] = {0};

/* Timing variables */
long long g_UpdateTime = 0;

/* External variables */
extern int g_subIdx;
extern int g_XijieRadarIdx;
extern unsigned short g_energe;
extern unsigned short g_distance;
extern long long g_Time;

/* Private function prototypes -----------------------------------------------*/
static void UART_GPIO_Config(uint8_t uart_num);
static void UART_DMA_Config(uint8_t uart_num);
static void UART_NVIC_Config(uint8_t uart_num, uint8_t priority);
static bool UART_Wait_TX_Complete(UART_TypeDef* UARTx, uint32_t timeout);

/* Private functions ---------------------------------------------------------*/
/**
 * @brief  Configure GPIO pins for UART
 * @param  uart_num: UART number (1, 2, or 4)
 * @retval None
 */
static void UART_GPIO_Config(uint8_t uart_num)
{
    PORT_InitTypeDef PORT_InitStruct;
    
    CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);
    
    switch (uart_num) {
        case 1:
            /* UART1: P1.6 (RX), P1.7 (TX) */
            PORT_InitStruct.PORT_Pin = PORT_Pin_6; /* RX */
            PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
            PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
            PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
            PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
            PORT_Init(PORT_1, &PORT_InitStruct);
            
            PORT_InitStruct.PORT_Pin = PORT_Pin_7; /* TX */
            PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
            PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
            PORT_Init(PORT_1, &PORT_InitStruct);
            
            /* Set alternate function */
            PORT_PinAFConfig(PORT_1, PORT_PinSource6, PORT_AF_1);
            PORT_PinAFConfig(PORT_1, PORT_PinSource7, PORT_AF_1);
            break;
            
        case 2:
            /* UART2: P2.0 (RX), P2.1 (TX) */
            PORT_InitStruct.PORT_Pin = PORT_Pin_0; /* RX */
            PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
            PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
            PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
            PORT_Init(PORT_2, &PORT_InitStruct);
            
            PORT_InitStruct.PORT_Pin = PORT_Pin_1; /* TX */
            PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
            PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
            PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
            PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
            PORT_Init(PORT_2, &PORT_InitStruct);
            
            /* Set alternate function */
            PORT_PinAFConfig(PORT_2, PORT_PinSource0, PORT_AF_1);
            PORT_PinAFConfig(PORT_2, PORT_PinSource1, PORT_AF_1);
            break;
            
        default:
            /* Invalid UART number */
            break;
    }
}

/**
 * @brief  Configure NVIC for UART
 * @param  uart_num: UART number
 * @param  priority: interrupt priority
 * @retval None
 */
static void UART_NVIC_Config(uint8_t uart_num, uint8_t priority)
{
    IRQn_Type irq_num;
    
    switch (uart_num) {
        case 1:
            irq_num = UART1_IRQn;
            break;
        case 2:
            irq_num = UART2_IRQn;
            break;
        case 4:
            irq_num = UART4_IRQn;
            break;
        default:
            return;
    }
    
    __disable_irq();
    NVIC_DisableIRQ(irq_num);
    NVIC_ClearPendingIRQ(irq_num);
    NVIC_SetPriority(irq_num, priority);
    NVIC_EnableIRQ(irq_num);
    __enable_irq();
}

/**
 * @brief  Wait for UART transmission complete
 * @param  UARTx: UART peripheral
 * @param  timeout: timeout count
 * @retval true if complete, false if timeout
 */
static bool UART_Wait_TX_Complete(UART_TypeDef* UARTx, uint32_t timeout)
{
    uint32_t count = 0;
    
    while (UART_GetLineStatus(UARTx, UART_LSFLAG_TXEmpty) == RESET) {
        count++;
        if (count >= timeout) {
            return false;
        }
    }
    return true;
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  Initialize UART1 for radar communication
 * @param  BaudRate: UART baud rate
 * @param  dmaEnable: DMA enable flag
 * @retval None
 */
void UART1_Init(uint32_t BaudRate, uint8_t dmaEnable)
{
    UART_InitTypeDef UART_InitStruct;
    
    /* Configure GPIO pins */
    UART_GPIO_Config(1);
    
    /* Enable UART1 peripheral clock */
    CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART1, ENABLE);
    
    /* Deinitialize UART1 */
    UART_DeInit(UART_1);
    
    /* Configure UART1 */
    UART_InitStruct.UART_BaudRate = BaudRate;
    UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
    UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
    UART_InitStruct.UART_Parity = UART_Parity_None;
    UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;
    UART_Init(UART_1, &UART_InitStruct);
    
    /* Clear receive buffer */
    memset((void*)Uart1_RxBuf, 0, sizeof(Uart1_RxBuf));
    
    if (dmaEnable) {
        /* Configure for DMA mode */
        UART_ITConfig(UART_1, (UART_IT_TXREmptyIE | UART_IT_ReceiveData_RXFIFOTimeOutIE), DISABLE);
        UART_FIFOModeConfig(UART_1, UART_TXFIFOThreshValue_0_32FULL, UART_RXFIFOThreshValue_1_32FULL, DISABLE);
        
        /* Configure DMA (implementation would go here) */
        /* ... DMA configuration code ... */
        
        /* Configure NVIC for DMA */
        UART_NVIC_Config(1, 0x00);
    } else {
        /* Configure for interrupt mode */
        UART_FIFOModeConfig(UART_1, UART_TXFIFOThreshValue_2_32FULL, UART_RXFIFOThreshValue_30_32FULL, ENABLE);
        UART_PTXREModeConfig(UART_1, ENABLE);
        
        UART_ITConfig(UART_1, UART_IT_TXREmptyIE, DISABLE);
        UART_ITConfig(UART_1, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE);
        
        /* Configure NVIC */
        UART_NVIC_Config(1, 0x0);
    }
}

/**
 * @brief  Deinitialize UART1
 * @param  None
 * @retval None
 */
void UART1_DeInit(void)
{
    __disable_irq();
    NVIC_DisableIRQ(UART1_IRQn);
    NVIC_ClearPendingIRQ(UART1_IRQn);
    __enable_irq();
    
    UART_DeInit(UART_1);
}

/**
 * @brief  UART1 interrupt handler
 * @param  None
 * @retval None
 */
void UART1_IRQHandler(void)
{
    volatile uint8_t received_data = 0;
    volatile int buffer_pos = 0;
    
    /* Handle TX empty interrupt */
    if (UART_GetITStatus(UART_1, UART_IntID_TXREmpty) != RESET) {
        /* TX handling if needed */
    }
    
    /* Handle RX data available or timeout interrupt */
    if ((UART_GetITStatus(UART_1, UART_IntID_ReceiveData) == SET) ||
        (UART_GetITStatus(UART_1, UART_IntID_RXFIFOTimeOut) == SET)) {
        
        while (UART_GetLineStatus(UART_1, UART_LSFLAG_DataReady) != RESET) {
            received_data = UART_ReceiveData(UART_1);
            buffer_pos = RxWritePos[UART1_POS] % UART1_RX_BUF_SIZE;
            Uart1_RxBuf[buffer_pos] = received_data;
            RxWritePos[UART1_POS]++;
        }
    }
}

/**
 * @brief  Transmit data via UART1
 * @param  bData: pointer to data buffer
 * @param  bLenth: data length
 * @retval true if successful, false if failed
 */
bool uart1_com_tx_data(uint8_t *bData, uint8_t bLenth)
{
    uint8_t i;
    
    if (bData == NULL || bLenth == 0) {
        return false;
    }
    
    for (i = 0; i < bLenth; i++) {
        while (UART_GetLineStatus(UART_1, UART_LSFLAG_TXREmpty_TXFIFOFull) == SET) {
            /* Wait for TX buffer available */
        }
        UART_SendData(UART_1, bData[i]);
    }
    
    return true;
}

/**
 * @brief  Receive data via UART1 with timeout
 * @param  pData: pointer to receive buffer
 * @param  Size: expected data size
 * @param  Timeout: timeout value
 * @retval true if successful, false if failed
 */
bool uart1_com_Receive(uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
    uint16_t received_size = 0;
    uint8_t *data_ptr;
    uint32_t timeout_count;

    if (pData == NULL || Size == 0) {
        return false;
    }

    data_ptr = pData;

    for (timeout_count = 0; timeout_count < Timeout; timeout_count++) {
        if (UART_GetLineStatus(UART_1, UART_LNSR_DATARDY) == SET) {
            *data_ptr = UART_ReceiveData(UART_1);
            data_ptr++;
            received_size++;
        }

        if (received_size == Size) {
            return true;
        }

        Delay_ms(1);
    }

    return false;
}

/**
 * @brief  Enable/disable UART1 interrupt
 * @param  uEnSta: enable status (1: enable, 0: disable)
 * @retval None
 */
void UART1_IRQ_SET(uint8_t uEnSta)
{
    if (uEnSta) {
        UART_ITConfig(UART_1, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE);
        NVIC_EnableIRQ(UART1_IRQn);
    } else {
        UART_ITConfig(UART_1, UART_IT_ReceiveData_RXFIFOTimeOutIE, DISABLE);
        NVIC_DisableIRQ(UART1_IRQn);
    }
}

/**
 * @brief  Initialize UART2 for main module communication
 * @param  None
 * @retval None
 */
void UART2_Init(void)
{
    UART_InitTypeDef UART_InitStruct;

    /* Configure GPIO pins */
    UART_GPIO_Config(2);

    /* Enable UART2 peripheral clock */
    CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART2, ENABLE);

    /* Deinitialize UART2 */
    UART_DeInit(UART_2);

    /* Configure UART2 */
    UART_InitStruct.UART_BaudRate = 115200;
    UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
    UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
    UART_InitStruct.UART_Parity = UART_Parity_None;
    UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;
    UART_Init(UART_2, &UART_InitStruct);

    /* Clear receive buffer */
    memset((void*)uart2_RxBuf, 0, sizeof(uart2_RxBuf));

    /* Configure FIFO mode */
    UART_FIFOModeConfig(UART_2, UART_TXFIFOThreshValue_2_32FULL, UART_RXFIFOThreshValue_30_32FULL, ENABLE);
    UART_PTXREModeConfig(UART_2, ENABLE);

    /* Configure interrupts */
    UART_ITConfig(UART_2, UART_IT_TXREmptyIE, DISABLE);
    UART_ITConfig(UART_2, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE);

    /* Configure NVIC */
    UART_NVIC_Config(2, 0x0);
}

/**
 * @brief  Deinitialize UART2
 * @param  None
 * @retval None
 */
void UART2_DeInit(void)
{
    __disable_irq();
    NVIC_DisableIRQ(UART2_IRQn);
    NVIC_ClearPendingIRQ(UART2_IRQn);
    __enable_irq();

    UART_DeInit(UART_2);
}

/**
 * @brief  UART2 interrupt handler
 * @param  None
 * @retval None
 */
void UART2_IRQHandler(void)
{
    volatile uint8_t received_data = 0;
    volatile int buffer_pos = 0;

    /* Handle TX empty interrupt */
    if (UART_GetITStatus(UART_2, UART_IntID_TXREmpty) != RESET) {
        /* TX handling if needed */
    }

    /* Handle RX data available or timeout interrupt */
    if ((UART_GetITStatus(UART_2, UART_IntID_ReceiveData) == SET) ||
        (UART_GetITStatus(UART_2, UART_IntID_RXFIFOTimeOut) == SET)) {

        while (UART_GetLineStatus(UART_2, UART_LSFLAG_DataReady) != RESET) {
            received_data = UART_ReceiveData(UART_2);
            buffer_pos = RxWritePos[UART2_POS] % UART2_RX_BUF_SIZE;
            uart2_RxBuf[buffer_pos] = received_data;
            RxWritePos[UART2_POS]++;
        }
    }
}

/**
 * @brief  Transmit data via UART2
 * @param  bLenth: data length
 * @param  bData: pointer to data buffer
 * @retval true if successful, false if failed
 */
bool uart2_com_tx_data(int bLenth, uint8_t *bData)
{
    int i;

    if (bData == NULL || bLenth <= 0) {
        return false;
    }

    for (i = 0; i < bLenth; i++) {
        while (UART_GetLineStatus(UART_2, UART_LSFLAG_TXREmpty_TXFIFOFull) == SET) {
            /* Wait for TX buffer available */
        }
        UART_SendData(UART_2, bData[i]);
    }

    return UART_Wait_TX_Complete(UART_2, UART_TX_TIMEOUT_COUNT);
}

/**
 * @brief  Initialize UART4 for debug communication
 * @param  None
 * @retval None
 */
void UART4_Init(void)
{
    UART_InitTypeDef UART_InitStruct;
    PORT_InitTypeDef PORT_InitStruct;

    /* Enable peripheral clocks */
    CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);
    CMU_APBPeriph0ClockCmd(CMU_APBPeriph0_UART4, ENABLE);

    /* Configure GPIO pins: P0.0 (TX), P0.1 (RX) */
    PORT_InitStruct.PORT_Pin = PORT_Pin_0; /* TX */
    PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
    PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
    PORT_InitStruct.PORT_OutType = PORT_OutType_OD;
    PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
    PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
    PORT_Init(PORT_0, &PORT_InitStruct);

    PORT_InitStruct.PORT_Pin = PORT_Pin_1; /* RX */
    PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
    PORT_Init(PORT_0, &PORT_InitStruct);

    /* Set alternate function */
    PORT_PinAFConfig(PORT_0, PORT_PinSource0, PORT_AF_1);
    PORT_PinAFConfig(PORT_0, PORT_PinSource1, PORT_AF_1);

    /* Deinitialize UART4 */
    UART_DeInit(UART_4);

    /* Configure UART4 */
    UART_InitStruct.UART_BaudRate = 115200;
    UART_InitStruct.UART_DataLength = UART_DataLength_8Bits;
    UART_InitStruct.UART_StopBits = UART_StopBits_1Bit;
    UART_InitStruct.UART_Parity = UART_Parity_None;
    UART_InitStruct.UART_HardwareFlowControl = UART_HardwareFlowControl_None;
    UART_Init(UART_4, &UART_InitStruct);

    /* Configure FIFO mode */
    UART_FIFOModeConfig(UART_4, UART_TXFIFOThreshValue_2_32FULL, UART_RXFIFOThreshValue_30_32FULL, ENABLE);
    UART_PTXREModeConfig(UART_4, ENABLE);

    /* Configure interrupts */
    UART_ITConfig(UART_4, UART_IT_TXREmptyIE, DISABLE);
    UART_ITConfig(UART_4, UART_IT_ReceiveData_RXFIFOTimeOutIE, ENABLE);

    /* Configure NVIC */
    UART_NVIC_Config(4, 0x0);
}

/**
 * @brief  Deinitialize UART4
 * @param  None
 * @retval None
 */
void UART4_DeInit(void)
{
    __disable_irq();
    NVIC_DisableIRQ(UART4_IRQn);
    NVIC_ClearPendingIRQ(UART4_IRQn);
    __enable_irq();

    UART_DeInit(UART_4);
}

/**
 * @brief  UART4 interrupt handler
 * @param  None
 * @retval None
 */
void UART4_IRQHandler(void)
{
    volatile uint8_t received_data = 0;

    /* Handle RX data available or timeout interrupt */
    if ((UART_GetITStatus(UART_4, UART_IntID_ReceiveData) == SET) ||
        (UART_GetITStatus(UART_4, UART_IntID_RXFIFOTimeOut) == SET)) {

        while (UART_GetLineStatus(UART_4, UART_LSFLAG_DataReady) != RESET) {
            received_data = UART_ReceiveData(UART_4);
            /* Process received data if needed */
        }
    }
}
