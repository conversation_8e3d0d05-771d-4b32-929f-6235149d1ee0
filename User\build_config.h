/**
 * @file    build_config.h
 * @brief   Build configuration and feature flags
 * @version 1.0
 * @date    2024-01-01
 */

#ifndef _BUILD_CONFIG_H_
#define _BUILD_CONFIG_H_

/* Build version information */
#define BUILD_VERSION_MAJOR         1
#define BUILD_VERSION_MINOR         0
#define BUILD_VERSION_PATCH         30
#define BUILD_DATE                  __DATE__
#define BUILD_TIME                  __TIME__

/* Feature enable/disable flags */
#define FEATURE_DEBUG_ENABLED       1       /* Enable debug features */
#define FEATURE_WATCHDOG_ENABLED    1       /* Enable watchdog timer */
#define FEATURE_RTC_ENABLED         1       /* Enable RTC functionality */
#define FEATURE_4G_MODULE_ENABLED   1       /* Enable 4G module support */
#define FEATURE_RADAR_ENABLED       1       /* Enable radar functionality */
#define FEATURE_LED_ENABLED         1       /* Enable LED control */
#define FEATURE_PWM_ENABLED         1       /* Enable PWM output */
#define FEATURE_DEEP_SLEEP_ENABLED  1       /* Enable deep sleep mode */
#define FEATURE_JSON_PROTOCOL       1       /* Enable JSON protocol */
#define FEATURE_CRC_CHECK           1       /* Enable CRC checking */
#define FEATURE_STATISTICS          1       /* Enable statistics collection */

/* Debug configuration */
#if FEATURE_DEBUG_ENABLED
#define DEBUG_UART_ENABLED          1       /* Enable debug UART output */
#define DEBUG_LEVEL                 2       /* Debug level (0-3) */
#define DEBUG_BUFFER_SIZE           256     /* Debug buffer size */
#else
#define DEBUG_UART_ENABLED          0
#define DEBUG_LEVEL                 0
#define DEBUG_BUFFER_SIZE           0
#endif

/* Memory configuration */
#define HEAP_SIZE                   4096    /* Heap size in bytes */
#define STACK_SIZE                  2048    /* Stack size in bytes */
#define JSON_MEMORY_POOL_SIZE       2048    /* JSON memory pool size */

/* Communication configuration */
#define UART1_BUFFER_SIZE           340     /* UART1 buffer size (85*4) */
#define UART2_BUFFER_SIZE           1024    /* UART2 buffer size */
#define UART4_BUFFER_SIZE           256     /* UART4 buffer size */
#define COMMUNICATION_TIMEOUT       5000    /* Communication timeout (ms) */
#define MAX_RETRY_COUNT             3       /* Maximum retry count */

/* Timing configuration */
#define SYSTEM_TICK_FREQUENCY       1000    /* System tick frequency (Hz) */
#define WATCHDOG_TIMEOUT            30      /* Watchdog timeout (seconds) */
#define RADAR_QUERY_INTERVAL        10      /* Radar query interval (seconds) */
#define RADAR_TIMEOUT               3       /* Radar timeout (seconds) */
#define SYSTEM_OPERATION_TIMEOUT    180     /* System operation timeout (seconds) */
#define DEFAULT_PERIOD_INTERVAL     14400   /* Default period interval (seconds) */

/* Hardware configuration */
#define SYSTEM_CLOCK_FREQUENCY      16000000    /* System clock frequency (Hz) */
#define APB_CLOCK_FREQUENCY         16000000    /* APB clock frequency (Hz) */
#define TIMER_CLOCK_FREQUENCY       16000000    /* Timer clock frequency (Hz) */

/* Power management configuration */
#define BATTERY_LOW_THRESHOLD       3.0     /* Battery low threshold (V) */
#define BATTERY_CRITICAL_THRESHOLD  2.8     /* Battery critical threshold (V) */
#define POWER_SAVE_MODE_ENABLED     1       /* Enable power save mode */

/* Radar configuration */
#if FEATURE_RADAR_ENABLED
#define RADAR_BAUD_RATE             9600    /* Radar UART baud rate */
#define RADAR_DATA_TIMEOUT          1000    /* Radar data timeout (ms) */
#define RADAR_MAX_DISTANCE          4500    /* Maximum valid radar distance (mm) */
#define RADAR_MIN_DISTANCE          100     /* Minimum valid radar distance (mm) */
#define RADAR_STABLE_COUNT          5       /* Radar stable count threshold */
#define DISTANCE_CHANGE_THRESHOLD   500     /* Distance change threshold (mm) */
#define DISTANCE_TRIGGER_THRESHOLD  3000    /* Distance trigger threshold (mm) */
#endif

/* 4G module configuration */
#if FEATURE_4G_MODULE_ENABLED
#define MODULE_4G_BAUD_RATE         115200  /* 4G module UART baud rate */
#define MODULE_4G_TIMEOUT           30000   /* 4G module timeout (ms) */
#define MODULE_4G_RETRY_COUNT       3       /* 4G module retry count */
#endif

/* LED configuration */
#if FEATURE_LED_ENABLED
#define LED_BLINK_INTERVAL          500     /* LED blink interval (ms) */
#define LED_PWM_FREQUENCY           1000    /* LED PWM frequency (Hz) */
#define LED_DEFAULT_BRIGHTNESS      50      /* Default LED brightness (%) */
#endif

/* JSON protocol configuration */
#if FEATURE_JSON_PROTOCOL
#define JSON_MAX_BUFFER_SIZE        1024    /* Maximum JSON buffer size */
#define JSON_MAX_NESTING_DEPTH      10      /* Maximum JSON nesting depth */
#define JSON_MAX_STRING_LENGTH      256     /* Maximum JSON string length */
#endif

/* Statistics configuration */
#if FEATURE_STATISTICS
#define STATS_COLLECTION_ENABLED    1       /* Enable statistics collection */
#define STATS_SAVE_INTERVAL         3600    /* Statistics save interval (seconds) */
#define STATS_MAX_ENTRIES           100     /* Maximum statistics entries */
#endif

/* Compiler and optimization settings */
#ifdef __GNUC__
#define COMPILER_GCC                1
#define OPTIMIZE_FOR_SIZE           1       /* Optimize for size */
#define INLINE                      __inline__
#define FORCE_INLINE                __attribute__((always_inline))
#define WEAK                        __attribute__((weak))
#define PACKED                      __attribute__((packed))
#else
#define COMPILER_GCC                0
#define OPTIMIZE_FOR_SIZE           0
#define INLINE                      inline
#define FORCE_INLINE                inline
#define WEAK
#define PACKED
#endif

/* Safety and validation macros */
#define ASSERT_ENABLED              FEATURE_DEBUG_ENABLED
#define BOUNDS_CHECK_ENABLED        FEATURE_DEBUG_ENABLED
#define NULL_POINTER_CHECK_ENABLED  1

#if ASSERT_ENABLED
#define ASSERT(condition) \
    do { \
        if (!(condition)) { \
            printf("ASSERT failed: %s:%d\r\n", __FILE__, __LINE__); \
            while(1); \
        } \
    } while(0)
#else
#define ASSERT(condition) ((void)0)
#endif

#if BOUNDS_CHECK_ENABLED
#define CHECK_BOUNDS(index, max) \
    do { \
        if ((index) >= (max)) { \
            printf("Bounds check failed: %s:%d\r\n", __FILE__, __LINE__); \
            return -1; \
        } \
    } while(0)
#else
#define CHECK_BOUNDS(index, max) ((void)0)
#endif

#if NULL_POINTER_CHECK_ENABLED
#define CHECK_NULL_POINTER(ptr) \
    do { \
        if ((ptr) == NULL) { \
            printf("NULL pointer: %s:%d\r\n", __FILE__, __LINE__); \
            return -1; \
        } \
    } while(0)
#else
#define CHECK_NULL_POINTER(ptr) ((void)0)
#endif

/* Version string generation */
#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)
#define VERSION_STRING "v" TOSTRING(BUILD_VERSION_MAJOR) "." \
                           TOSTRING(BUILD_VERSION_MINOR) "." \
                           TOSTRING(BUILD_VERSION_PATCH)

#define FULL_VERSION_STRING VERSION_STRING " (" BUILD_DATE " " BUILD_TIME ")"

/* Build information */
#define BUILD_INFO_STRING "XCM32L Parking Detection System " FULL_VERSION_STRING

#endif /* _BUILD_CONFIG_H_ */
