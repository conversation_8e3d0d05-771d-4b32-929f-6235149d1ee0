/**
 * @file    gpio_optimized.c
 * @brief   Optimized GPIO control module with unified interface
 * @version 1.0
 * @date    2024-01-01
 */

/* Includes ------------------------------------------------------------------*/
#include "XCM32Lxx_conf.h"
#include "XCM32Lxx_port.h"
#include "system_config.h"
#include "main_optimized.h"

/* Private types -------------------------------------------------------------*/
/**
 * @brief GPIO configuration structure
 */
typedef struct {
    PORT_TypeDef* port;
    uint16_t pin;
    PORT_Properity_TypeDef property;
    PORT_Mode_TypeDef mode;
    PORT_OutType_TypeDef out_type;
    PORT_PullHigh_TypeDef pull_high;
    PORT_DriveSink_TypeDef drive_sink;
} gpio_config_t;

/* Private constants ---------------------------------------------------------*/
/**
 * @brief LED GPIO configurations
 */
static const gpio_config_t led_configs[] = {
    /* Red LED */
    {LED_RED_PORT, LED_RED_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* Green LED */
    {LED_GREEN_PORT, LED_GREEN_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* Blue LED */
    {LED_BLUE_PORT, LED_BLUE_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* System run LED */
    {LED_SYS_RUN_PORT, LED_SYS_RUN_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal}
};

/**
 * @brief Power control GPIO configurations
 */
static const gpio_config_t power_configs[] = {
    /* Radar power control */
    {POWER_RADAR_PORT, POWER_RADAR_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* 4G module power control */
    {POWER_4G_PORT, POWER_4G_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* Main module power control */
    {POWER_MAIN_PORT, POWER_MAIN_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal},
    /* Boost power control */
    {POWER_BOOST_PORT, POWER_BOOST_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkNormal}
};

/**
 * @brief PWM control GPIO configurations
 */
static const gpio_config_t pwm_configs[] = {
    /* Status LED PWM */
    {PWM_STATUS_LED_PORT, PWM_STATUS_LED_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkStrength},
    /* Fill light PWM */
    {PWM_FILL_LIGHT_PORT, PWM_FILL_LIGHT_PIN, PORT_Properity_Digital, PORT_Mode_OUT, 
     PORT_OutType_PP, PORT_PH_NoPullHigh, PORT_DS_DriveSinkStrength}
};

/* Private function prototypes -----------------------------------------------*/
static void GPIO_Configure_Pin(const gpio_config_t* config);
static void GPIO_Configure_Group(const gpio_config_t* configs, uint32_t count);
static void GPIO_Configure_I2C_Pins(void);
static void GPIO_Configure_PWM_Pins(void);
static void GPIO_Set_Initial_States(void);

/* Private functions ---------------------------------------------------------*/
/**
 * @brief  Configure a single GPIO pin
 * @param  config: pointer to GPIO configuration structure
 * @retval None
 */
static void GPIO_Configure_Pin(const gpio_config_t* config)
{
    PORT_InitTypeDef PORT_InitStruct;
    
    PORT_StructInit(&PORT_InitStruct);
    PORT_InitStruct.PORT_Pin = config->pin;
    PORT_InitStruct.PORT_Properity = config->property;
    PORT_InitStruct.PORT_Mode = config->mode;
    PORT_InitStruct.PORT_OutType = config->out_type;
    PORT_InitStruct.PORT_PullHigh = config->pull_high;
    PORT_InitStruct.PORT_DriveSink = config->drive_sink;
    
    PORT_Init(config->port, &PORT_InitStruct);
}

/**
 * @brief  Configure a group of GPIO pins
 * @param  configs: pointer to array of GPIO configurations
 * @param  count: number of configurations in the array
 * @retval None
 */
static void GPIO_Configure_Group(const gpio_config_t* configs, uint32_t count)
{
    uint32_t i;
    
    for (i = 0; i < count; i++) {
        GPIO_Configure_Pin(&configs[i]);
    }
}

/**
 * @brief  Configure I2C pins
 * @param  None
 * @retval None
 */
static void GPIO_Configure_I2C_Pins(void)
{
    PORT_InitTypeDef PORT_InitStruct;
    
    /* Configure I2C SDA and SCL pins (P2.2 and P2.3) */
    PORT_StructInit(&PORT_InitStruct);
    PORT_InitStruct.PORT_Pin = PORT_Pin_2 | PORT_Pin_3;
    PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
    PORT_InitStruct.PORT_Mode = PORT_Mode_OUT;
    PORT_InitStruct.PORT_OutType = PORT_OutType_PP;
    PORT_InitStruct.PORT_PullHigh = PORT_PH_PullHigh;
    PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
    PORT_Init(PORT_2, &PORT_InitStruct);
    
    /* Set I2C pins to high level */
    PORT_WriteBit(PORT_2, PORT_Pin_2, Bit_SET);
    PORT_WriteBit(PORT_2, PORT_Pin_3, Bit_SET);
}

/**
 * @brief  Configure PWM pins with alternate function
 * @param  None
 * @retval None
 */
static void GPIO_Configure_PWM_Pins(void)
{
    /* Configure PWM pins */
    GPIO_Configure_Group(pwm_configs, sizeof(pwm_configs) / sizeof(pwm_configs[0]));
    
    /* Set alternate function for Timer2 PWM output (P5.0) */
    PORT_PinAFConfig(PWM_FILL_LIGHT_PORT, PORT_PinSource0, PORT_AF_7);
    
    /* Set alternate function for Timer1 PWM output (P5.1) */
    PORT_PinAFConfig(PWM_STATUS_LED_PORT, PORT_PinSource1, PORT_AF_7);
}

/**
 * @brief  Set initial states for GPIO pins
 * @param  None
 * @retval None
 */
static void GPIO_Set_Initial_States(void)
{
    /* Turn on system run LED */
    sys_run_led_ON;
    
    /* Turn off all color LEDs by default */
    red_led_OFF;
    green_led_OFF;
    blue_led_OFF;
    
    /* Turn off radar power */
    ST_ULT_PWRCTL_OFF;
}

/* Public functions ----------------------------------------------------------*/
/**
 * @brief  Deinitialize all GPIO pins
 * @param  None
 * @retval None
 */
void uf_GPIO_DeInit(void)
{
    PORT_DeInit();
}

/**
 * @brief  Initialize all GPIO pins
 * @param  None
 * @retval None
 */
void uf_GPIO_Init(void)
{
    /* Enable PORT peripheral clock */
    CMU_APBPeriph1ClockCmd(CMU_APBPeriph1_PORT, ENABLE);
    
    /* Deinitialize all ports first */
    PORT_DeInit();
    
    /* Configure LED pins */
    GPIO_Configure_Group(led_configs, sizeof(led_configs) / sizeof(led_configs[0]));
    
    /* Configure power control pins */
    GPIO_Configure_Group(power_configs, sizeof(power_configs) / sizeof(power_configs[0]));
    
    /* Configure PWM pins */
    GPIO_Configure_PWM_Pins();
    
    /* Configure I2C pins */
    GPIO_Configure_I2C_Pins();
    
    /* Set initial pin states */
    GPIO_Set_Initial_States();
}

/**
 * @brief  GPIO test function
 * @param  None
 * @retval 0 (never returns)
 */
int GPIO_test_main(void)
{
    PORT_InitTypeDef PORT_InitStruct;
    
    /* Initialize GPIO */
    uf_GPIO_Init();
    
    /* Test booster control */
    PORT_WriteBit(POWER_BOOST_PORT, POWER_BOOST_PIN, Bit_RESET);
    DelayMs(1000);
    
    /* Configure booster pin as input */
    PORT_StructInit(&PORT_InitStruct);
    PORT_InitStruct.PORT_Pin = POWER_BOOST_PIN;
    PORT_InitStruct.PORT_Properity = PORT_Properity_Digital;
    PORT_InitStruct.PORT_Mode = PORT_Mode_IN;
    PORT_InitStruct.PORT_OutType = PORT_OutType_PP;
    PORT_InitStruct.PORT_PullHigh = PORT_PH_NoPullHigh;
    PORT_InitStruct.PORT_DriveSink = PORT_DS_DriveSinkNormal;
    PORT_Init(POWER_BOOST_PORT, &PORT_InitStruct);
    
    /* Turn off all LEDs initially */
    red_led_OFF;
    green_led_OFF;
    blue_led_OFF;
    
    /* Turn on blue LED for test indication */
    blue_led_ON;
    
    /* Main test loop */
    while (1) {
        /* Toggle system run LED */
        sys_run_led_Toggle;
        DelayMs(2000);
    }
}

/**
 * @brief  Control LED state
 * @param  led_port: LED port
 * @param  led_pin: LED pin
 * @param  state: LED state (LED_ON, LED_OFF, LED_TOGGLE)
 * @retval None
 */
void GPIO_Control_LED(PORT_TypeDef* led_port, uint16_t led_pin, uint8_t state)
{
    switch (state) {
        case LED_ON:
            PORT_WriteBit(led_port, led_pin, Bit_SET);
            break;
        case LED_OFF:
            PORT_WriteBit(led_port, led_pin, Bit_RESET);
            break;
        case LED_TOGGLE:
            PORT_ToggleBit(led_port, led_pin);
            break;
        default:
            /* Invalid state, do nothing */
            break;
    }
}

/**
 * @brief  Control power module state
 * @param  power_port: Power control port
 * @param  power_pin: Power control pin
 * @param  state: Power state (POWER_ON or POWER_OFF)
 * @retval None
 */
void GPIO_Control_Power(PORT_TypeDef* power_port, uint16_t power_pin, uint8_t state)
{
    if (state == POWER_ON) {
        PORT_WriteBit(power_port, power_pin, Bit_SET);
    } else {
        PORT_WriteBit(power_port, power_pin, Bit_RESET);
    }
}

/**
 * @brief  Read GPIO pin state
 * @param  gpio_port: GPIO port
 * @param  gpio_pin: GPIO pin
 * @retval Pin state (Bit_SET or Bit_RESET)
 */
uint8_t GPIO_Read_Pin(PORT_TypeDef* gpio_port, uint16_t gpio_pin)
{
    return PORT_ReadInputDataBit(gpio_port, gpio_pin);
}
