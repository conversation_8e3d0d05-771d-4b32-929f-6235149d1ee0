#ifndef _UART_H_
#define _UART_H_


#ifdef _UART_C_
#define UART_EXTERN
#else 
#define UART_EXTERN extern
#endif

#define DBG_UART  (0)
#define     SYS_NULL                    ((void*)0)

#ifndef NULL
#define	NULL 0l
#endif


#define UART_NUM  2

#define UART1_POS 0
#define UART2_POS 1

#define UART1_RX_BUF_SIZE (85*4)
#define UART2_RX_BUF_SIZE (1024)

#define UART_BUF_LEN    (1024)

#define MAX_SUPERWAVE_DATA_NUM	20

UART_EXTERN volatile unsigned long long RxReadPos[UART_NUM];
UART_EXTERN volatile unsigned long long RxWritePos[UART_NUM];

UART_EXTERN volatile unsigned char uart2_RxBuf[UART2_RX_BUF_SIZE];
UART_EXTERN volatile unsigned char Uart1_RxBuf[UART1_RX_BUF_SIZE];

UART_EXTERN unsigned char uart_proc_buf[UART_BUF_LEN];
UART_EXTERN unsigned char uart_proced_buf[UART_BUF_LEN];

UART_EXTERN long long g_UpdateTime;

#define __UART_1_Define



void UART1_Init(uint32_t BaudRate,uint8_t dmaEnable);
void UART1_IRQHandler(void);

int uart1_rx_mengmu_process(unsigned short *pEnerge,unsigned short *distance);
int pack_luna_data(int dev_type, int pack_type, int total_packages, int seq, unsigned short usEnerge, unsigned short usDistance, unsigned char* packed_data, int *packed_len);
//int uart1_rx_xijie_process(unsigned short *pEnerge,unsigned short *distance);
bool uart1_com_tx_data(u8 *bData, u8 bLenth);
bool uart1_com_Receive(uint8_t *pData, uint16_t Size, uint32_t Timeout);
void UART1_IRQ_SET(u8 uEnSta);


#define 	__UART_2_Define


void UART2_Init(void);
void UART2_IRQHandler(void);

int uart2_rx_process(int *type, unsigned char **buffer, int *len);

int uart2_find_head_and_tail(unsigned char *buf, int buf_width, long long pos_start, long long pos_end, long long  *head_pos, long long  *tail_pos);
int uart2_unpack_data(int *dev_type, int *pack_type, int *total_packages, int *seq, unsigned char* data, int data_len, unsigned char* unpacked_data, unsigned int *unpacked_len);
int pack_print_data(int dev_type, int pack_type, int total_packages, int seq, unsigned char* packed_data, int data_len, int *packed_len);
int pack_data(int dev_type, int pack_type, int total_packages, int seq, unsigned char* data, int data_len, unsigned char* packed_data, int *packed_len);

bool uart2_com_tx_data(int bLenth, u8 *bData);


#define __UART_4_Define

void UART4_Init(void);
void UART4_IRQHandler(void);
bool  uart4_tx_buffer_out(u8 *bData);
bool  uart4_tx_buffer_in(u8 bData);
u16  uart4_get_tx_buffer_size(void);

bool uart4_com_tx_data(u8 bLenth, u8 *bData);
void uart4_com_tx(void);
void Sys_Printf(char *fmt, ...);

#endif



